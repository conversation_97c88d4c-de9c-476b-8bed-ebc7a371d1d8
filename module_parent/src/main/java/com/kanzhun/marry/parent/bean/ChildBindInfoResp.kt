package com.kanzhun.marry.parent.bean

import java.io.Serializable

data class ChildBindInfoResp(
    val canBindNum:Int = 0,
    val canBindMaxNum:Int = 0,
    val parentList:List<BindParentBean>? = null
):Serializable{
    /**
     * 已经绑定最大数量
     */
    fun hasBindMax():Boolean{
        val size = parentList?.size?:0
        return size >= canBindMaxNum
    }
    fun getCanBindCount() = canBindMaxNum - (parentList?.size?:0)
}