package com.kanzhun.marry.parent.module.main.home

import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.kotlin.ktx.toGenderString
import com.kanzhun.foundation.kotlin.ktx.toHeightString
import com.kanzhun.foundation.kotlin.ktx.toSimpleBirthYear
import com.kanzhun.foundation.router.ParentPageRouter
import com.kanzhun.marry.parent.bean.ChildUser
import com.kanzhun.marry.parent.databinding.ParentHomeListLockedItemBinding
import com.kanzhun.marry.parent.module.main.viewmodel.ParentHomeViewModel

class ParentHomeListBlockUserItemProvider(val viewModel: ParentHomeViewModel) : BaseItemProvider<BaseListItem, ParentHomeListLockedItemBinding>() {
    override fun onBindItem(binding: ParentHomeListLockedItemBinding, item: BaseListItem) {
        if (item is ChildUser) {
            binding.run {
                ivAvatar.load(item.tinyAvatar)
                tvGender.text = item.gender.toGenderString()
                tvBirth.text = item.birthday.toSimpleBirthYear()
                tvHeight.text = item.height.toHeightString()
                //基本信息
                rcvBaseInfo.setChildBaseInfo1(item)
                root.clickWithTrigger {
                    ParentPageRouter.jumpBindNextPage(it.context, viewModel.bindStatus,PageSource.PARENT_HOME_BLOCK_CARD)
                }
            }
        }
    }


}