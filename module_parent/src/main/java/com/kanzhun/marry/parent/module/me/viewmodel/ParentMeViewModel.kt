package com.kanzhun.marry.parent.module.me.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.http.response.BaseResponse
import com.kanzhun.marry.parent.api.APIParent
import com.kanzhun.marry.parent.module.me.bean.ParentBindInfoBean
import com.kanzhun.marry.parent.module.me.bean.ParentMeBaseBean
import com.kanzhun.marry.parent.module.me.bean.ParentMeBindChildBean
import com.kanzhun.marry.parent.module.me.bean.ParentMeInfoTitleBean
import com.kanzhun.marry.parent.module.me.bean.ParentMeListBean
import io.reactivex.rxjava3.core.Observable

class ParentMeViewModel:BaseViewModel() {

    val userTabModelMutableLiveData = MutableLiveData<MutableList<ParentMeBaseBean>>()
    val mParentBindInfoBean = MutableLiveData<ParentBindInfoBean>()
    fun requestUserTabInfo() {
        val responseObservable: Observable<BaseResponse<ParentBindInfoBean>> =
            RetrofitManager.getInstance().createApi(
                APIParent::class.java
            ).requestProfileInfo()

        HttpExecutor.execute<ParentBindInfoBean>(
            responseObservable,
            object : BaseRequestCallback<ParentBindInfoBean>() {

                override fun handleInChildThread(data: ParentBindInfoBean) {
                    super.handleInChildThread(data)
                    mParentBindInfoBean.postValue(data)
                    val list = mutableListOf<ParentMeBaseBean>()
                    if(data.bindStatus == 3 && !data.childList.isNullOrEmpty()){
                        list.add(ParentMeInfoTitleBean(data.childList))
                    }else if(data.bindStatus == 1 || data.bindStatus == 2){
                        val user = ServiceManager.getInstance().databaseService.userDao.getUser(
                            AccountHelper.getInstance().userId
                        )
                        list.add(ParentMeBindChildBean(
                            data.bindStatus,
                            data.invitedId,
                            data.invitedPhone,
                            user.childInfoGender
                        ))
                    }
                    list.add(ParentMeListBean(data.bindStatus))

                    userTabModelMutableLiveData.postValue(list)
                }

                override fun onSuccess(data: ParentBindInfoBean) {
                }
                override fun dealFail(reason: ErrorReason) {
                    val list = mutableListOf<ParentMeBaseBean>()
                    list.add(ParentMeListBean(3))
                    userTabModelMutableLiveData.postValue(list)
                }

                override fun onComplete() {
                    super.onComplete()
                    showContent()
                }
            })
    }
}