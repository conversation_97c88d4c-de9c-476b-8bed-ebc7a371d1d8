package com.kanzhun.marry.parent.module.me.activity

import android.content.Intent
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.view.View
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.MutableLiveData
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.goMainInteractParentTab
import com.kanzhun.common.kotlin.ext.liveEventBusObserve
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.getViewBinding
import com.kanzhun.common.kotlin.ui.statusbar.useBlackTextStatusBar
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.dialog.cancelInvitingDialog
import com.kanzhun.foundation.dialog.childUnbindParentDialog
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.ParentPageRouter
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.bean.BindParentBean
import com.kanzhun.marry.parent.databinding.ParentActivityChildBindParentsBinding
import com.kanzhun.marry.parent.databinding.ParentActivityChildBindParentsEmptyBinding
import com.kanzhun.marry.parent.databinding.ParentBindParentItemBinding
import com.kanzhun.marry.parent.module.me.utils.showParentAccountHelperDialog
import com.kanzhun.marry.parent.module.me.viewmodel.ParentChildBindParentViewModel
import com.kanzhun.marry.parent.point.ParentPointReporter
import com.kanzhun.utils.T
import com.sankuai.waimai.router.annotation.RouterUri

@RouterUri(path = [ParentPageRouter.PARENT_ROUTER_CHILD_BIND_USER_ACTIVITY])
class ChildBindParentsActivity : BaseBindingActivity<ParentActivityChildBindParentsBinding, ParentChildBindParentViewModel>() {


    override var setStatusBar = {
        useBlackTextStatusBar(R.color.common_color_F5F5F5.toResourceColor())
    }

    private val mAdapter: ChildBindInfoAdapter by lazy {
        ChildBindInfoAdapter({
            reportClickEvent("查看推荐记录",it)
        },this::onListButtonClick)
    }

    override fun preInit(intent: Intent) {

    }

    override fun initView() {
        mBinding.apply {
            titleBar.asBackButton{
                reportClickEvent("返回")
            }
            tvBottomAccountHelp.root.clickWithTrigger {
                reportClickEvent("什么是家长账号")
                showParentAccountHelperDialog()
            }
            recyclerview.adapter = mAdapter
            stateLayout.onEmpty {
                mBinding.titleBar.setTitle("")
                ParentActivityChildBindParentsEmptyBinding.bind(this).setEmpty()
            }
            tvAddParent.clickWithTrigger {
                reportClickEvent("添加绑定家长")
                ParentPageRouter.jumpChildSelectBindWayActivity(this@ChildBindParentsActivity)
            }
        }
    }

    override fun initData() {
        mViewModel.bindInfo.observe(this) {
            it?.run {
                mBinding.titleBar.setTitle(R.string.parent_account)
                mAdapter.setNewInstance(this.parentList?.toMutableList())
                mBinding.tvAddParent.visible(!this.hasBindMax())
                mBinding.tvAddParent.text = "还可以绑定${this.getCanBindCount()}位家长"
            }
        }
        mViewModel.showLoading()
        observeBindStatusChange()
    }

    /**
     * 监听绑定状态变化
     */
    private fun observeBindStatusChange() {
        //接受邀请成功
        liveEventBusObserve(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_SUCCESS) { currentBindCount: Int ->
            mViewModel.loadData()
            //刚好达到绑定上限，且有邀请卡，提示取消其他邀请的toast
            if (mViewModel.bindInfo.value?.canBindMaxNum == currentBindCount && mViewModel.hasInviteCard) {
                T.ss("已达到可绑定数量上限，自动取消其他邀请")
            }

        }
        //对方解除绑定、接受绑定成功
        liveEventBusObserve(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE) { userId: String ->
            if (userId == AccountHelper.getInstance().userId) {
                mViewModel.loadData()
            }

        }

    }


    override fun onResume() {
        super.onResume()
        mViewModel.loadData()

    }

    override fun onRetry() {
        mViewModel.showLoading()
        mViewModel.loadData()
    }

    override fun getStateLayout() = mBinding.stateLayout

    private fun onListButtonClick(item: BindParentBean) {
        if (item.isBindParent()) {
            //解除绑定弹框
            childUnbindParentDialog(item.userId) {
                mViewModel.loadData()
                reportClickEvent("确认解除绑定", item.userId)
            }
            reportClickEvent("解除绑定", item.userId)
        } else {
            //取消邀请弹框
            cancelInvitingDialog(item.inviteId,null)
            reportClickEvent("取消邀请", item.userId)
        }
    }

    /**
     * @param type 添加绑定家长、查看推荐记录、解除绑定、取消邀请、什么是家长账号、返回
     * @param userId 点击解绑或者取消邀请的用户id
     */
    private fun reportClickEvent(type: String, userId: String? = "") {
        ParentPointReporter.reportBindParentClick(type, mAdapter.data, userId)
    }
}


private fun ParentActivityChildBindParentsEmptyBinding.setEmpty(click : (type:String,userId:String?) -> Unit = {_,_->}) {
    tvInviteParent.clickWithTrigger {
        ParentPageRouter.jumpChildSelectBindWayActivity(it.context as FragmentActivity )
        click("添加绑定家长",null)
    }
    tvEmptyAccountHelp.root.clickWithTrigger {
        it.context.showParentAccountHelperDialog()
        click("什么是家长账号",null)
    }

}

class ChildBindInfoAdapter(val moreRecordClick:(userId:String?)->Unit,val callback: (item: BindParentBean) -> Unit) : BaseQuickAdapter<BindParentBean, BaseViewHolder>(R.layout.parent_bind_parent_item) {
    override fun convert(holder: BaseViewHolder, item: BindParentBean) {
        holder.getViewBinding<ParentBindParentItemBinding>().run {
            tvParentName.text = item.getUserNickNameString()
            tvDesc.text = item.getPhoneNumberString()
            tvOperate.text = if (item.isBindParent()) {
                "解除绑定"
            } else {
                "取消邀请"
            }
            if (item.forwardUserNum > 0) {
                tvRecord.textOrGone("查看推荐记录·${item.forwardUserNum}")
            } else {
                tvRecord.textOrGone("查看推荐记录")
            }
            tvRecord.visible(item.isBindParent())
            tvRecord.clickWithTrigger {
                moreRecordClick(item.userId)
                goMainInteractParentTab()
            }

            tvOperate.clickWithTrigger {
                callback(item)
            }
        }
    }

}