package com.kanzhun.marry.parent.module.me

import android.os.Bundle
import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ui.statusbar.addStatusMargin
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.parent.databinding.ParentFragmentMeBinding
import com.kanzhun.marry.parent.module.me.adapter.multiTypeBindingAdapter
import com.kanzhun.marry.parent.module.me.viewmodel.ParentMeViewModel
import com.kanzhun.marry.parent.point.ParentPointAction

/**
 * 父母模式：我的页面
 */
class ParentMeFragment : BaseBindingFragment<ParentFragmentMeBinding, ParentMeViewModel>() {
    val cancelBindliveData = MutableLiveData<String>()
    override fun preInit(arguments: Bundle) {

    }

    override fun initView() {
        val adapter = multiTypeBindingAdapter(activity!!,cancelBindliveData)
        cancelBindliveData.observe(this){
            if(!it.isNullOrBlank()){
                onRetry()
            }
        }

        mBinding.idTitleBar.addStatusMargin()

        mBinding.idRecyclerview.adapter = adapter

        mViewModel.userTabModelMutableLiveData.observe(this) {
            adapter.replaceData(it)
            reportPoint(ParentPointAction.PARENT_F4_PAGE_EXPO){
                binding_status = mViewModel.mParentBindInfoBean.value?.bindStatus
            }
        }

        LiveEventBus.get<Boolean>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_SUCCESS).observe(this) {
            onRetry()
        }

        LiveEventBus.get<String?>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE).observe(this) {
            if(it == AccountHelper.getInstance().userId){
                onRetry()
            }
        }
    }


    override fun onResume() {
        super.onResume()
        onRetry()

    }

    override fun initData() {
    }

    override fun onRetry() {
        mViewModel.requestUserTabInfo()
    }

    override fun getStateLayout() = mBinding.stateLayout
}