package com.kanzhun.marry.parent.module.main.preformance

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.kotlin.base.RefreshViewModel
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance

class ParentAutoRefreshPerformance(val viewmodel: RefreshViewModel) : AbsPerformance() {
    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        LiveEventBus.get<Int>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_SUCCESS).observe(owner) {
            viewmodel.loadData(true,true)
        }

        LiveEventBus.get<String?>(LivedataKeyCommon.EVENT_KEY_PARENT_BIND_STATE_CHANGE).observe(owner) {
            if(it == AccountHelper.getInstance().userId){
                viewmodel.loadData(true,true)
            }
        }
    }

}
