package com.kanzhun.marry.parent.module.me.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.marry.parent.R
import com.kanzhun.marry.parent.module.me.bean.ParentBaseInfoItem

/**
 * 基本信息列表适配器
 */
class PreviewBaseInfoAdapter : BaseQuickAdapter<ParentBaseInfoItem, BaseViewHolder>(R.layout.parent_preview_user_info_item) {
    override fun convert(holder: BaseViewHolder, item: ParentBaseInfoItem) {
        holder.setText(R.id.tvItemName, item.name)
        holder.setText(R.id.tvItemValue, item.value)
    }
}
