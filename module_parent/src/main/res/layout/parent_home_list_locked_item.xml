<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp">

    <com.kanzhun.common.views.image.OImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:background="@color/common_white"
        app:common_circle="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_marginTop="22dp"
        android:background="@drawable/parent_home_item_bg"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_radiusBottomLeft="20dp"
        app:qmui_radiusBottomRight="20dp"
        app:qmui_radiusTopLeft="20dp"
        app:qmui_radiusTopRight="20dp" />

    <View
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginStart="2dp"
        android:layout_marginTop="2dp"
        android:background="@drawable/parent_home_item_bg1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/iv_avatar"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="10dp"
        app:common_circle="true"
        app:common_blur="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/colorAccent" />


    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvGender"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginBottom="13dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_26"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintEnd_toStartOf="@id/divider1_1"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@id/iv_avatar"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="男" />

    <View
        android:id="@+id/divider1_1"
        android:layout_width="1dp"
        android:layout_height="16dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:background="@color/common_color_292929"
        app:layout_constraintBottom_toBottomOf="@id/tvGender"
        app:layout_constraintEnd_toStartOf="@id/tvBirth"
        app:layout_constraintStart_toEndOf="@id/tvGender"
        app:layout_constraintTop_toTopOf="@id/tvGender" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvBirth"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_26"
        app:layout_constraintBottom_toBottomOf="@id/tvGender"
        app:layout_constraintEnd_toStartOf="@id/divider1_2"
        app:layout_constraintStart_toEndOf="@id/divider1_1"
        app:layout_constraintTop_toTopOf="@id/tvGender"
        tools:text="95年" />

    <View
        android:id="@+id/divider1_2"
        android:layout_width="1dp"
        android:layout_height="16dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:background="@color/common_color_292929"
        app:layout_constraintBottom_toBottomOf="@id/tvGender"
        app:layout_constraintEnd_toStartOf="@id/tvHeight"
        app:layout_constraintStart_toEndOf="@id/tvBirth"
        app:layout_constraintTop_toTopOf="@id/tvGender" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/tvHeight"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:ellipsize="end"
        android:singleLine="true"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_26"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@id/tvGender"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_weight="1"
        app:layout_constraintStart_toEndOf="@id/divider1_2"
        app:layout_constraintTop_toTopOf="@id/tvGender"
        tools:text="@string/common_long_placeholder" />

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="100dp"
        android:background="@color/common_white"
        android:paddingHorizontal="16dp"
        android:paddingBottom="24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_radius="20dp">

        <include
            layout="@layout/parent_home_list_item_base_info_layout"
            android:id="@+id/rcvBaseInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            app:layout_constraintTop_toTopOf="parent"
            />
        <View
            android:background="@drawable/parent_bg_color_ffffff_0_to_100"
            android:layout_width="match_parent"
            android:layout_height="102dp"
            app:layout_constraintBottom_toBottomOf="@id/rcvBaseInfo"
            app:layout_constraintStart_toStartOf="parent" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/tvBindChild"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="21dp"
            android:gravity="center"
            android:paddingVertical="10dp"
            android:text="@string/parent_bind_child_button_text"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_18"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rcvBaseInfo"
            app:stv_corner="28dp"
            app:stv_solid="@color/common_color_292929" />
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>