<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:layout_marginHorizontal="12dp"
    android:paddingBottom="24dp"
    android:background="@color/common_white"
    app:qmui_backgroundColor="@color/common_white"
    app:qmui_radius="20dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@mipmap/parent_img_bg_bind_child"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/idTitle"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:text="@string/parent_bind_child_account"
        android:textSize="24dp"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="24dp"
        android:textColor="@color/common_black"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/idSubTitle"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@+id/idTitle"
        app:layout_constraintTop_toBottomOf="@+id/idTitle"
        android:text="已向***********发送绑定邀请"
        android:textSize="18dp"
        android:paddingBottom="12dp"
        android:textColor="@color/common_color_292929"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/idContent"
        app:layout_constraintTop_toBottomOf="@+id/idSubTitle"
        app:layout_constraintLeft_toLeftOf="@+id/idSubTitle"
        android:layout_marginTop="4dp"
        android:text="孩子填写相亲信息，您帮忙找对象"
        android:layout_marginRight="26dp"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="18dp"
        android:textColor="@color/common_color_858585"
        android:layout_width="0dp"
        android:layout_height="wrap_content"/>

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/idBtn"
        app:layout_constraintLeft_toLeftOf="@+id/idTitle"
        app:layout_constraintTop_toBottomOf="@+id/idContent"
        android:layout_marginTop="22dp"
        style="@style/button_large_next_page_style"
        android:text="去绑定"
        />

</com.qmuiteam.qmui.layout.QMUIConstraintLayout>