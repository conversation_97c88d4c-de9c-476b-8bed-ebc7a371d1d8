<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginBottom="12dp"
    android:layout_marginTop="12dp"
    android:background="@color/common_white"
    android:paddingBottom="24dp"
    app:qmui_backgroundColor="@color/common_white"
    app:qmui_radius="20dp">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="h,351:76"
        android:background="@mipmap/parent_img_bg_bind_child"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kanzhun.common.views.textview.BoldTextView
        android:id="@+id/idTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="24dp"
        android:text="@string/parent_bind_child_account"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/idSubTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/parent_share_record_bind_child_hint"
        android:textColor="@color/common_color_858585"
        android:textSize="@dimen/common_text_sp_18"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="@+id/idTitle"
        app:layout_constraintTop_toBottomOf="@+id/idTitle" />


    <com.coorchice.library.SuperTextView
        android:layout_marginHorizontal="16dp"
        android:id="@+id/idBtn"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="去绑定"
        android:textSize="@dimen/common_text_sp_18"
        app:layout_constraintLeft_toLeftOf="@+id/idTitle"
        app:layout_constraintTop_toBottomOf="@+id/idSubTitle"
        app:stv_corner="23dp"
        android:textColor="@color/common_white"
        app:stv_solid="@color/common_color_292929" />

</com.qmuiteam.qmui.layout.QMUIConstraintLayout>