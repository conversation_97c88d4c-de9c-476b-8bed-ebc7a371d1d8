<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.kanzhun.foundation.views.CommonPageTitleView
        android:id="@+id/tv_phone_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:title_all_page="/3"
        app:title_icon="@drawable/login_ic_icon_activate_sex"
        app:title_text_size="32dp"
        app:title_now_page="1"
        app:title_text="您孩子的性别是？" />

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/fl_male"
        android:layout_width="150dp"
        android:layout_height="100dp"
        android:layout_marginStart="28dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_phone_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/fl_female"
        app:qmui_radius="12dp">

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/oigMale"
            android:src="@mipmap/img_ic_male_unselect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"/>

        <ImageView
            android:id="@+id/iv_male_select_rectangle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:src="@drawable/me_bg_activate_house_car_sel"
            />

        <ImageView
            android:id="@+id/iv_male_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:src="@drawable/login_icon_activate_sex_sel"
            android:layout_gravity="end"
            />
    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/fl_female"
        android:layout_width="150dp"
        android:layout_height="100dp"
        app:layout_constraintLeft_toRightOf="@+id/fl_male"
        android:layout_marginEnd="28dp"
        android:layout_marginStart="20dp"
        app:layout_constraintTop_toTopOf="@+id/fl_male"
        app:layout_constraintRight_toRightOf="parent"
        app:qmui_radius="12dp">
        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/oigFemale"
            android:src="@mipmap/img_ic_female_unselect"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY" />

        <ImageView
            android:id="@+id/iv_female_select_rectangle"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent"
            android:src="@drawable/me_bg_activate_house_car_sel"
            />

        <ImageView
            android:id="@+id/iv_female_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:src="@drawable/login_icon_activate_sex_sel"
            android:layout_gravity="end"
            />
    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

    <TextView
        android:id="@+id/tv_male"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/common_color_191919"
        android:textSize="22dp"
        android:layout_marginTop="12dp"
        app:layout_constraintLeft_toLeftOf="@+id/fl_male"
        app:layout_constraintRight_toRightOf="@+id/fl_male"
        app:layout_constraintTop_toBottomOf="@+id/fl_male"
        android:fontFamily="sans-serif-condensed-medium"
        android:text="男孩"/>

    <TextView
        android:id="@+id/tv_female"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/common_color_191919"
        android:textSize="22dp"
        android:layout_marginTop="12dp"
        app:layout_constraintLeft_toLeftOf="@+id/fl_female"
        app:layout_constraintRight_toRightOf="@+id/fl_female"
        app:layout_constraintTop_toBottomOf="@+id/fl_male"
        android:fontFamily="sans-serif-condensed-medium"
        android:text="女孩"/>

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/btn_next"
        android:layout_marginEnd="28dp"
        android:layout_marginBottom="28dp"
        app:layout_constraintLeft_toLeftOf="parent"
        style="@style/button_large_next_page_style"
        android:enabled="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
