<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingTop="28dp"
    android:paddingBottom="4dp"
    android:layout_height="wrap_content">

    <ImageView
        app:layout_constraintLeft_toLeftOf="@+id/idTitle"
        app:layout_constraintBottom_toBottomOf="@+id/idTitle"
        app:layout_constraintRight_toRightOf="@id/idTitle"
        android:background="@drawable/parent_year_item_select_bg"
        android:layout_width="0dp"
        android:layout_height="10dp"/>

    <View
        android:layout_width="0dp"
        app:layout_constraintLeft_toRightOf="@+id/idTitle"
        app:layout_constraintTop_toTopOf="@+id/idTitle"
        app:layout_constraintBottom_toBottomOf="@+id/idTitle"
        android:background="@color/common_color_FFE0E0E0"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginLeft="12dp"
        android:layout_height="1dp"/>

    <TextView
        android:id="@+id/idTitle"
        app:layout_constraintLeft_toLeftOf="parent"
        tools:text="00后"
        android:textColor="@color/common_color_191919"
        android:textStyle="bold"
        android:textSize="24dp"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>