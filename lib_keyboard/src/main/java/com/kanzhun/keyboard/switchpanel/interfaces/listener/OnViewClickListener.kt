package com.kanzhun.keyboard.switchpanel.interfaces.listener

import android.view.View

/**
 * preventing listeners that [com.effective.android.panel.PanelSwitchHelper] set these to view from being overwritten
 * Created by yum<PERSON><PERSON><PERSON> on 18-7-07
 * Email: <EMAIL>
 * blog: yummylau.com
 * update 2020/05/08 支持 dsl
 */
interface OnViewClickListener {
    fun onClickBefore(view: View?)
}

private typealias OnClickBefore = (view: View?) -> Unit

class OnViewClickListenerBuilder : OnViewClickListener {

    private var onClickBefore: OnClickBefore? = null

    override fun onClickBefore(view: View?) {
        onClickBefore?.invoke(view)
    }

    fun onClickBefore(onClickBefore: OnClickBefore) {
        this.onClickBefore = onClickBefore
    }
}