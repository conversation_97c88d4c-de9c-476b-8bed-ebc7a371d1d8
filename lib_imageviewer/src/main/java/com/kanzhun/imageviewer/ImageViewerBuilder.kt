package com.kanzhun.imageviewer

import android.view.View
import androidx.annotation.IdRes
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.StaggeredGridLayoutManager
import com.kanzhun.imageviewer.bean.ActivityConfig
import com.kanzhun.imageviewer.interfaces.IProgress
import com.kanzhun.imageviewer.interfaces.ImageViewLoadFactory
import com.kanzhun.imageviewer.interfaces.OnListener
import com.kanzhun.imageviewer.loader.InstanceLoader
import com.kanzhun.imageviewer.loader.MultiContentLoader
import com.kanzhun.imageviewer.ui.ImageViewerActivity
import kotlin.collections.set

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/4
 */
class ImageViewerBuilder {
    private var originImageUrls: List<String>? = null
    private var targetImageUrls: List<String>? = null
    private var errorDrawableResIdList = hashMapOf<Int, Int>()
    private var viewParams: List<com.kanzhun.imageviewer.bean.ViewParams>? = null
    private var position: Int = 0
    private var autoLoadTarget: Boolean = true
    private var texts: List<CharSequence?>? = null
    private var auditTexts: List<CharSequence?>? = null

    fun originImageUrls(data: List<String>) = apply {
        this.originImageUrls = data
    }

    fun targetImageUrls(data: List<String>) = apply {
        this.targetImageUrls = data
    }

    fun viewParams(data: List<com.kanzhun.imageviewer.bean.ViewParams>) = apply {
        this.viewParams = data
    }

    fun position(data: Int) = apply {
        this.position = data
    }

    fun autoLoadTarget(data: Boolean) = apply {
        this.autoLoadTarget = data
    }

    fun errorDrawableResId(pos: Int, res: Int) = apply {
        errorDrawableResIdList[pos] = res;

    }

    fun urls(imageUrl: String) = apply {
        this.originImageUrls = listOf(imageUrl)
    }

    fun texts(texts: List<CharSequence?>?) = apply {
        this.texts = texts
    }

    fun auditTexts(auditTexts: List<CharSequence?>?) = apply {
        this.auditTexts = auditTexts
    }


    fun urls(imageUrl: String, targetUrl: String) = apply {
        this.originImageUrls = listOf(imageUrl)
        this.targetImageUrls = listOf(targetUrl)
    }


    fun urls(imageUrls: List<String>?) = apply {
        this.originImageUrls = imageUrls
    }

    fun urls(imageUrls: List<String>?, targetImageUrls: List<String>?) = apply {
        this.originImageUrls = imageUrls
        this.targetImageUrls = targetImageUrls
    }

    fun views(view: View?) = apply {
        val views = arrayOfNulls<View>(1)
        views[0] = view
        views(views)
    }

    fun views(views: Array<View?>) = apply {
        val list = mutableListOf<com.kanzhun.imageviewer.bean.ViewParams>()
        for (imageView in views) {
            val imageBean = com.kanzhun.imageviewer.bean.ViewParams()
            if (imageView == null) {
                imageBean.left = 0
                imageBean.top = 0
                imageBean.width = 0
                imageBean.height = 0
            } else {
                val location = IntArray(2)
                imageView.getLocationOnScreen(location)
                imageBean.left = location[0]
                imageBean.top = location[1]
                imageBean.width = imageView.width
                imageBean.height = imageView.height
            }
            list.add(imageBean)
        }
        this.viewParams = list
    }

    fun views(views: List<View?>) = apply {
        val list = mutableListOf<com.kanzhun.imageviewer.bean.ViewParams>()
        for (imageView in views) {
            val imageBean = com.kanzhun.imageviewer.bean.ViewParams()
            if (imageView == null) {
                imageBean.left = 0
                imageBean.top = 0
                imageBean.width = 0
                imageBean.height = 0
            } else {
                val location = IntArray(2)
                imageView.getLocationOnScreen(location)
                imageBean.left = location[0]
                imageBean.top = location[1]
                imageBean.width = imageView.width
                imageBean.height = imageView.height
            }
            list.add(imageBean)
        }
        this.viewParams = list
    }

    fun views(recyclerView: RecyclerView, @IdRes viewId: Int) = apply {
        val originImageViewList = mutableListOf<View?>()
        val childCount = recyclerView.childCount
        for (i in 0 until childCount) {
            val originImage = recyclerView.getChildAt(i).findViewById<View>(viewId)
            if (originImage != null) {
                originImageViewList.add(originImage)
            }
        }
        val layoutManager = recyclerView.layoutManager
        var firstPos = 0
        var lastPos = 0
        val totalCount = layoutManager!!.itemCount
        when (layoutManager) {
            is GridLayoutManager -> {
                firstPos = layoutManager.findFirstVisibleItemPosition()
                lastPos = layoutManager.findLastVisibleItemPosition()
            }

            is LinearLayoutManager -> {
                firstPos = layoutManager.findFirstVisibleItemPosition()
                lastPos = layoutManager.findLastVisibleItemPosition()
            }

            is StaggeredGridLayoutManager -> {
                val lastVisibleItemPositions = layoutManager.findLastVisibleItemPositions(null)
                val firstVisibleItemPositions = layoutManager.findFirstVisibleItemPositions(null)
                lastPos = getLastVisibleItem(lastVisibleItemPositions)
                firstPos = getFirstVisibleItem(firstVisibleItemPositions)
            }
        }
        lastPos = if (lastPos > totalCount) totalCount - 1 else lastPos
        fillPlaceHolder(originImageViewList, totalCount, firstPos, lastPos)
        val views = arrayOfNulls<View>(originImageViewList.size)
        for (i in originImageViewList.indices) {
            views[i] = originImageViewList[i]
        }
        views(views)
    }

    /**
     * @return Last visible item position for staggeredGridLayoutManager
     */
    private fun getLastVisibleItem(lastVisibleItemPositions: IntArray): Int {
        var maxSize = 0
        for (position in lastVisibleItemPositions) {
            if (position > maxSize) {
                maxSize = position
            }
        }
        return maxSize
    }

    /**
     * @return First visible item position for staggeredGridLayoutManager
     */
    private fun getFirstVisibleItem(firstVisibleItemPositions: IntArray): Int {
        var minSize = 0
        if (firstVisibleItemPositions.isNotEmpty()) {
            minSize = firstVisibleItemPositions[0]
            for (position in firstVisibleItemPositions) {
                if (position < minSize) {
                    minSize = position
                }
            }
        }
        return minSize
    }

    /**
     * fill recycleView
     */
    private fun fillPlaceHolder(originImageList: MutableList<View?>, totalCount: Int, firstPos: Int, lastPos: Int) {
        if (firstPos > 0) {
            for (pos in firstPos downTo 1) {
                originImageList.add(0, null)
            }
        }
        if (lastPos < totalCount) {
            for (i in totalCount - 1 - lastPos downTo 1) {
                originImageList.add(null)
            }
        }
    }

    /**
     * data : recyclerView 的数据 不是图片类型的数据传null
     */
    fun views(recyclerView: RecyclerView, @IdRes viewId: Int, data: List<Any?>) = apply {
        val originImageViewList = mutableListOf<View?>()
        val layoutManager = recyclerView.layoutManager
        var firstPos = 0
        var lastPos = 0
        val totalCount = layoutManager!!.itemCount
        when (layoutManager) {
            is GridLayoutManager -> {
                firstPos = layoutManager.findFirstVisibleItemPosition()
                lastPos = layoutManager.findLastVisibleItemPosition()
            }

            is LinearLayoutManager -> {
                firstPos = layoutManager.findFirstVisibleItemPosition()
                lastPos = layoutManager.findLastVisibleItemPosition()
            }

            is StaggeredGridLayoutManager -> {
                val lastVisibleItemPositions = layoutManager.findLastVisibleItemPositions(null)
                val firstVisibleItemPositions = layoutManager.findFirstVisibleItemPositions(null)
                lastPos = getLastVisibleItem(lastVisibleItemPositions)
                firstPos = getFirstVisibleItem(firstVisibleItemPositions)
            }
        }
        lastPos = if (lastPos >= totalCount) totalCount - 1 else lastPos
        for (i in firstPos until lastPos + 1) {
            val view = layoutManager.findViewByPosition(i);
            val originImage = view?.findViewById<View>(viewId)
            if (originImage != null) {
                originImageViewList.add(originImage)
            }
        }
        fillPlaceHolder(originImageViewList, totalCount, firstPos, lastPos, data)
        val views = arrayOfNulls<View>(originImageViewList.size)
        for (i in originImageViewList.indices) {
            views[i] = originImageViewList[i]
        }
        views(views)
    }

    /**
     * fill recycleView
     */
    private fun fillPlaceHolder(
        originImageList: MutableList<View?>, totalCount: Int, firstPos: Int, lastPos: Int, data: List<Any?>
    ) {
        if (firstPos > 0) {
            for (pos in firstPos downTo 1) {
                if (data[pos - 1] != null) {
                    originImageList.add(0, null)
                }
            }
        }
        if (lastPos < totalCount) {
            for (i in lastPos + 1 until totalCount - 1) {
                if (data[i] != null) {
                    originImageList.add(null)
                }
            }
        }
    }


    inline fun imageViewerListener(
        crossinline onStartAnim: (position: Int) -> Unit = {},
        crossinline onClick: (view: View, x: Float, y: Float, position: Int) -> Unit = { _, _, _, _ -> },
        crossinline onLongClick: (fragmentActivity: FragmentActivity?, view: View, x: Float, y: Float, position: Int) -> Unit = { _, _, _, _, _ -> },
        crossinline onShowFinish: (imageViewerView: ImageViewerView, showImmediately: Boolean) -> Unit = { _, _ -> },
        crossinline onMojitoViewFinish: (pagePosition: Int) -> Unit = {},
        crossinline onDrag: (view: ImageViewerView, moveX: Float, moveY: Float) -> Unit = { _, _, _ -> },
        crossinline onLongImageMove: (ratio: Float) -> Unit = {},
        crossinline onViewPageSelected: (position: Int) -> Unit = {},
        crossinline onSaveFile: (fragmentActivity: FragmentActivity?, path: String) -> Unit = { _, _ -> },
    ) = setOnListener(object : OnListener {
        override fun onStartAnim(position: Int) = onStartAnim(position)

        override fun onClick(view: View, x: Float, y: Float, position: Int) = onClick(view, x, y, position)

        override fun onLongClick(fragmentActivity: FragmentActivity?, view: View, x: Float, y: Float, position: Int) =
            onLongClick(fragmentActivity, view, x, y, position)

        override fun onShowFinish(imageViewerView: ImageViewerView, showImmediately: Boolean) =
            onShowFinish(imageViewerView, showImmediately)

        override fun onMojitoViewFinish(pagePosition: Int) = onMojitoViewFinish(pagePosition)

        override fun onDrag(view: ImageViewerView, moveX: Float, moveY: Float) = onDrag(view, moveX, moveY)

        override fun onLongImageMove(ratio: Float) = onLongImageMove(ratio)

        override fun onViewPageSelected(position: Int) = onViewPageSelected(position)
        override fun onSaveFile(fragmentActivity: FragmentActivity?, path: String) = onSaveFile(fragmentActivity, path)

    })

    fun setOnListener(target: OnListener?) = apply {
        ImageViewerActivity.onListener = target
    }

    fun setProgressLoader(progressLoader: InstanceLoader<IProgress>) = apply {
        ImageViewerActivity.progressLoader = progressLoader
    }

    inline fun multiContentLoader(
        crossinline providerLoader: (position: Int) -> ImageViewLoadFactory,
        crossinline providerEnableTargetLoad: (position: Int) -> Boolean,
    ) = setMultiContentLoader(object : MultiContentLoader {
        override fun providerLoader(position: Int): ImageViewLoadFactory = providerLoader(position)

        override fun providerEnableTargetLoad(position: Int): Boolean = providerEnableTargetLoad(position)
    })

    fun setMultiContentLoader(loader: MultiContentLoader) = apply {
        ImageViewerActivity.multiContentLoader = loader
    }

    fun build(): ActivityConfig {
        return ActivityConfig(
            originImageUrls = this.originImageUrls,
            targetImageUrls = this.targetImageUrls,
            viewParams = this.viewParams,
            position = this.position,
            autoLoadTarget = this.autoLoadTarget,
            errorDrawableResIdList = this.errorDrawableResIdList,
            texts = this.texts,
            auditTexts = this.auditTexts
        )
    }
}