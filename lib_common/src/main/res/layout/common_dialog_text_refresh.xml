<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="350dp"
    android:paddingBottom="24dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/common_bg_corner_20_top_2_color_white"
    >

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingBottom="20dp"
        android:paddingTop="20dp"
        android:gravity="left|center_vertical"
        android:paddingLeft="20dp"
        android:text="你的学历是？"
        android:textColor="@color/common_color_141414"
        android:textSize="@dimen/common_text_sp_18"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/tv_sure"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="18dp"
        android:paddingTop="20dp"
        android:paddingBottom="20dp"
        android:paddingRight="18dp"
        android:text="换一换"
        android:drawableLeft="@drawable/common_ic_refresh"
        android:drawablePadding="2dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler"
        android:layout_width="match_parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        android:orientation="horizontal"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="10dp"
        tools:listitem="@layout/common_item_bottom_select_dialog" />
</com.qmuiteam.qmui.layout.QMUIConstraintLayout>
