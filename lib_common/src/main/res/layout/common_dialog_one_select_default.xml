<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="255dp"
    android:layout_gravity="bottom"
    android:background="@drawable/common_bg_conor_16_top_2_white">

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="65dp"
        android:gravity="center"
        android:paddingLeft="18dp"
        android:paddingRight="18dp"
        android:text="@string/common_cancel"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="65dp"
        android:gravity="center"
        android:text="你的学历是？"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_18"
        android:textStyle="bold"

        app:layout_constraintLeft_toRightOf="@+id/tv_cancel"
        app:layout_constraintRight_toLeftOf="@+id/tv_sure"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sure"
        android:layout_width="wrap_content"
        android:layout_height="65dp"
        android:gravity="center"
        android:paddingLeft="18dp"
        android:paddingRight="18dp"
        android:text="@string/common_sure"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_16"
        app:layout_constraintLeft_toRightOf="@+id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/v_wheel"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginLeft="28dp"
        android:layout_marginRight="28dp"
        app:common_wheel_view_loop="false"
        app:common_wheel_view_textSize="@dimen/common_text_sp_24"
        app:common_wheel_view_visible_item="7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />
</androidx.constraintlayout.widget.ConstraintLayout>