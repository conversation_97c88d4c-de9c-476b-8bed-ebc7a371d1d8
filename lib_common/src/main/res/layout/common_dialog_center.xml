<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_system_dialog"
    android:orientation="vertical">


    <FrameLayout
        android:id="@+id/fl_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="104dp"
        android:paddingHorizontal="24dp"
        android:paddingTop="28dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:gravity="center_horizontal"
                android:textColor="@color/common_black"
                android:textSize="@dimen/common_text_sp_20"
                android:textStyle="bold"
                tools:text="确定要退出吗？现在退出将丢失测试进度现在退出将丢失测试进度" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:lineSpacingExtra="2dp"
                    android:maxHeight="300dp"
                    android:textColor="@color/common_color_4C4C4C"
                    android:textSize="@dimen/common_text_sp_16"
                    android:visibility="gone"
                    tools:text="@string/common_long_placeholder"
                    tools:visibility="visible" />
            </ScrollView>

        </LinearLayout>

    </FrameLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_h_btn"
        android:layout_width="match_parent"
        android:layout_height="46dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="28dp">

        <com.coorchice.library.SuperTextView
            android:id="@+id/tv_h_negative"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            android:layout_marginEnd="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_h_positive"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="25dp"
            app:stv_stroke_color="@color/common_color_292929"
            app:stv_stroke_width="1dp"
            tools:text="不同意" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/tv_h_positive"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@color/color_white"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_h_negative"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="25dp"
            app:stv_solid="@color/common_color_292929"
            tools:text="同意" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>