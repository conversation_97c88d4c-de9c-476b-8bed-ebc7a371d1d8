package com.kanzhun.common.animator.anim;

import android.animation.ObjectAnimator;
import android.view.View;

import com.kanzhun.common.animator.BaseViewAnimator;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/1
 */
public class AlphaInAnimator extends BaseViewAnimator {
    @Override
    protected void prepare(View target) {
        getAnimatorAgent().playTogether(
                ObjectAnimator.ofFloat(target, "alpha", 0, 1));
    }
}
