package com.kanzhun.common.animator;

import android.animation.Animator;
import android.view.View;

import com.kanzhun.common.animator.interpolator.InterpolatorUtil;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/1
 */
public class AnimUtil {
    public static final int DEFAULT_DURATION = 500;

    public static void alphaInAnim(View view, Animator.AnimatorListener animatorListener) {
        if (view == null) return;
        YoYo.with(Techniques.AlphaIn).duration(DEFAULT_DURATION).interpolate(InterpolatorUtil.createDefaultBezierInterpolator())
                .withListener(animatorListener).playOn(view);
    }
}
