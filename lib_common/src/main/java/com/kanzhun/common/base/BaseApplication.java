package com.kanzhun.common.base;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Environment;

import java.io.File;
import java.util.List;

import com.kanzhun.common.util.AppActivityLifecycleCallbacks;

/**
 * Created by wa<PERSON><PERSON> on 2018/8/27.
 */

public abstract class BaseApplication extends Application {
    private static final String TAG = "BaseApplication";


    protected AppActivityLifecycleCallbacks mActivityLifecycleCallbacks;

    private static BaseApplication application;

    @Override
    public void onCreate() {
        super.onCreate();
        application = this;
        mActivityLifecycleCallbacks = new AppActivityLifecycleCallbacks(this);
    }

    public Context getTopContext() {
        return mActivityLifecycleCallbacks.getTopContext();
    }

    public void finishAll() {
        mActivityLifecycleCallbacks.finishAll();
    }

    public List<Activity> getAllActivity() {
        return mActivityLifecycleCallbacks.getAllActivity();
    }

    public boolean isForeground() {
        return mActivityLifecycleCallbacks.isForeground();
    }

    public abstract boolean isDebug();

    public static BaseApplication getApplication() {
        return application;
    }

    public AppActivityLifecycleCallbacks getActivityLifecycleCallbacks() {
        return mActivityLifecycleCallbacks;
    }

    public Context getContext() {
        return mActivityLifecycleCallbacks.getTopContext();
    }

    /**
     * app
     */
    private File mCacheFile;

    public File getAppCacheDir() {
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {
            mCacheFile = getApplicationContext().getExternalCacheDir();
        }
        if (mCacheFile == null) {
            mCacheFile = getApplicationContext().getCacheDir();
        }
        return mCacheFile;
    }

    public void finishActivitiesWithOutParamsClass(Class c) {
        mActivityLifecycleCallbacks.finishActivitiesWithOutParamsClass(c);
    }

    public abstract Class getMainTabClass();
}
