package com.kanzhun.common.base

import java.io.Serializable

enum class PageSource : Serializable {
    //公共
    NONE,//未知
    PROTOCOL,//协议
    F1_RECOMMEND_CHILD_FRAGMENT,//F1孩子推荐
    F2_SEEN_ME_CHILD_FRAGMENT,//F2孩子互动看过我
    F2_LIKE_ME_CHILD_FRAGMENT,//F2孩子喜欢我
    F2_PARENT_RECOMMEND_CHILD_FRAGMENT,//F2家长推荐
    F4_ME_CHILD_FRAGMENT,//F4孩子我的
    AUTH_CENTER_ACTIVITY,//认证中心
    AUTH_RECERT_ACTIVITY,//账号申诉
    ME_USER_INFO_PREVIEW_ACTIVITY,//个人预览页
    NEW_USER_TASK_ACTIVITY,//新手任务
    CHAT,//聊天
    USER_EDIT_INFO_ACTIVITY,//F4编辑资料按钮
    CERTIFICATION_FIRST,//实名认证首步页面
    FIRST_NAME_VERIFIED_FRAGMENT,//首善流程-实名认证引导页
    F1_RECOMMEND_CHILD_FRAGMENT_TOP_BAR,//F1孩子推荐顶部bar
    F1_RECOMMEND_CHILD_FRAGMENT_LOCK_CARS,//F1锁定卡片
    CHAT_TO_PREVIEW_ACTIVITY,//从聊天进入喜欢
    LIKE_BLOCK,//喜欢拦截
    F1_ME_USER_INFO_PREVIEW_ACTIVITY,//从F1进入个人信息页点击喜欢
    CHILD_ME_INFO,//我的基本信息
    CHILD_ME_OCCUPATION,//我的基本信息-行业
    CHILD_ME_JOB,//我的基本信息-职业
    CHILD_ME_EDIT_INFO,//个人编辑页
    LIKE_LIST,//收到的称赞列表
    EDU_CHOOSE,//edu认证选择
    WORK_EDIT,//工作信息填写页
    COMPANY_CERT,//公司认证方式页
    MOOD_EDIT_FOR_RESULT,//心情编辑页forResult
    OTHER_MOOD_DETAIL,//别人心情详情页（客态）
    MY_MOOD_DETAIL,//主态我的心情详情页
    MY_MOOD_LIKE_LIST_DIALOG,//我的心情点赞列表
    PROTOCOL_OPEN_MOOD_DETAIL_WITH_THUMB,//协议打开主态我的心情心情页点赞列表
    PROTOCOL_OPEN_MOOD_EDIT,//协议打开心情编辑页
    MOOD_GRID_NEW_MOOD,//心情列表页新增表情
    MY_MOOD_DETAIL_EDIT_MOOD,//主态我的心情详情页,修改心情
    ME_MOOD_REJECT,//我的页面，心情驳回
    CHAT_LIST,//消息列表（联系人）
    ACTIVITY_RECOMMEND,//签到推送嘉宾列表
    EDU_EMAIL_SEND,//学历认证邮件发送页面
    FILM_ACTIVITY,//冲洗页面
    DYNAMIC_ACTIVITY,//动态页面
    DYNAMIC_NOTIFY_LIST,//动态通知列表
    F1_DIALOG,//f1弹窗
    Bubble_select,//理想型标签选择
    GUIDE_CARD, // 引导卡（见面守护）
    SETTINGS, // 设置页面

    //孩子端
    CHILD_F2_I_LIKE_FRAGMENT,//F2我喜欢的
    CHILD_F2_NOVICE_BLOCK,//F2新手阻断
    CHILD_F2_NOVICE_BLOCK_LIKE_ME,//F2新手阻断(喜欢我)
    CHILD_F2_NOVICE_BLOCK_VIEW_ME,//F2新手阻断（看过我）
    CHILD_SEND_LIKE_BLOCK,//新手发送喜欢阻断弹窗
    CHILD_F1_RECOMMEND_TOP_GUIDE,//F1新手顶部引导条
    CHILD_F4_NOVICE_BLOCK,//F4新手阻断
    CHILD_F4_EDIT_BLOCK,//F4我的页面编辑拦截

    //父母端
    PARENT_RECOMMEND_HOME_FRAGMENT, //家长推荐首页
    PARENT_SHARE_RECORD_FRAGMENT,//家长转发记录
    PARENT_ME_FRAGMENT,//家长我的
    PARENT_HOME_BLOCK_CARD,//家长F1阻断卡片
    PARENT_SHARE_RECORD_TOP_CARD,//家长F1新手顶部引导条
    PARENT_ME_INVITE_GUIDE,//家长我的引导绑定

}
