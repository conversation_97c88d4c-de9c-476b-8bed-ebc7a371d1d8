package com.kanzhun.common.router;

import android.content.Context;
import android.net.Uri;

import androidx.annotation.NonNull;

import com.sankuai.waimai.router.common.DefaultUriRequest;

import java.util.HashMap;

public class HiDefaultUriRequest extends DefaultUriRequest {
    public HiDefaultUriRequest(@NonNull Context context, @NonNull Uri uri) {
        super(context, uri);
    }

    public HiDefaultUriRequest(@NonNull Context context, @NonNull String uri) {
        super(context, uri);
    }

    public HiDefaultUriRequest(@NonNull Context context, @NonNull String uri, HashMap<String, Object> extra) {
        super(context, uri, extra);
    }
}
