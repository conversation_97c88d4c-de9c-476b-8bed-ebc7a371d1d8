package com.kanzhun.common.views.image;

import android.content.Context;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.net.Uri;
import android.util.AttributeSet;

import androidx.annotation.DrawableRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RawRes;

import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.shape.RelativeCornerSize;
import com.google.android.material.shape.ShapeAppearanceModel;
import com.kanzhun.common.R;
import com.kanzhun.common.views.image.imageloader.ImageLoader;
import com.kanzhun.common.views.image.imageloader.progress.OnProgressListener;
import com.kanzhun.common.views.image.imageloader.progress.OnRequestListener;
import com.yalantis.ucrop.util.FastBitmapDrawable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/21
 */
public class OImageView extends ShapeableImageView {
    protected int placeholderResId;
    protected int errorResId;
    protected boolean isCircle;
    protected int radius;
    protected boolean isBlur; //是否高斯模糊
    protected boolean cacheSource; //是否缓存原始文件
    protected Paint mStrokePaint;
    protected int mStrokeWidth;
    protected RectF mRectF;
    private Path mPath;
    private int mAntialiasPadding = 2;//防止有倾斜角度时候出现的锯齿
    protected int topLeftRadius;
    protected int topRightRadius;
    protected int bottomLeftRadius;
    protected int bottomRightRadius;

    public OImageView(@NonNull Context context) {
        this(context, null);
    }

    public OImageView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OImageView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    protected void init(Context context, AttributeSet attrs, int defStyleAttr) {
        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.common_OImageView, defStyleAttr, 0);
        errorResId = a.getResourceId(R.styleable.common_OImageView_common_error, 0);
        isCircle = a.getBoolean(R.styleable.common_OImageView_common_circle, false);
        if (!isCircle) {
            placeholderResId = a.getResourceId(R.styleable.common_OImageView_common_placeholder, R.drawable.common_bg_placeholder);
        } else {
            placeholderResId = a.getResourceId(R.styleable.common_OImageView_common_placeholder, R.drawable.common_bg_placeholder_circle);
        }
        radius = (int) a.getDimension(R.styleable.common_OImageView_common_radius, 0);
        topLeftRadius = (int) a.getDimension(R.styleable.common_OImageView_common_top_left_radius, 0);
        topRightRadius = (int) a.getDimension(R.styleable.common_OImageView_common_top_right_radius, 0);
        bottomLeftRadius = (int) a.getDimension(R.styleable.common_OImageView_common_bottom_left_radius, 0);
        bottomRightRadius = (int) a.getDimension(R.styleable.common_OImageView_common_bottom_right_radius, 0);
        isBlur = a.getBoolean(R.styleable.common_OImageView_common_blur, false);
        cacheSource = a.getBoolean(R.styleable.common_OImageView_common_cache_source, false);
        mStrokeWidth = a.getDimensionPixelSize(R.styleable.common_OImageView_common_image_stroke_width, 0);
        int resourceId = a.getResourceId(R.styleable.common_OImageView_common_src, 0);
        int strokeColor = a.getColor(R.styleable.common_OImageView_common_image_stroke_color, 0xffffffff);
        if (resourceId != 0) {
            loadResource(resourceId);
        }
        if (mStrokeWidth > 0) {
            mRectF = new RectF();
            mStrokePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            mStrokePaint.setStrokeWidth(mStrokeWidth);
            mStrokePaint.setStyle(Paint.Style.STROKE);
            mStrokePaint.setColor(strokeColor);
            mPath = new Path();
        }
        if (mStrokeWidth > 0) {
            setStrokeWidth(mStrokeWidth);
            setStrokeColor(ColorStateList.valueOf(strokeColor));
        }
        if (getStrokeWidth() > 0) {
            int padding = mStrokeWidth / 2;
            int contentPaddingLeft = getContentPaddingLeft();
            int contentPaddingRight = getContentPaddingRight();
            int contentPaddingTop = getContentPaddingTop();
            int contentPaddingBottom = getContentPaddingBottom();
            setContentPadding(Math.max(padding, contentPaddingLeft), Math.max(padding, contentPaddingTop), Math.max(padding, contentPaddingRight), Math.max(padding, contentPaddingBottom));
        }
        a.recycle();
        setImageShapeAppearance();
    }

    protected void setImageShapeAppearance() {
        if (isCircle) {
            //圆图
            setScaleType(ScaleType.CENTER_CROP);
            setShapeAppearanceModel(ShapeAppearanceModel.builder()
                    .setAllCornerSizes(new RelativeCornerSize(0.5f))
                    .build());
        } else if (radius > 0) {
            //圆角图片
            if (getScaleType() == ScaleType.FIT_CENTER) {
                //表示没有修改scaleType
                setScaleType(ScaleType.CENTER_CROP);
            }
            setShapeAppearanceModel(ShapeAppearanceModel.builder()
                    .setAllCornerSizes(radius)
                    .build());
        } else if (topLeftRadius > 0 || topRightRadius > 0 || bottomLeftRadius > 0 || bottomRightRadius > 0) {
            setShapeAppearanceModel(ShapeAppearanceModel.builder()
                    .setTopLeftCornerSize(topLeftRadius)
                    .setTopRightCornerSize(topRightRadius)
                    .setBottomLeftCornerSize(bottomLeftRadius)
                    .setBottomRightCornerSize(bottomRightRadius)
                    .build());
        }
    }

    public void load(String url) {
        if (!isBlur) {
            ImageLoader.getInstance().loadImage(getContext(), url, this, cacheSource, placeholderResId, errorResId);
//            if (!isCircle) {
//                if (radius == 0) {
//                    ImageLoader.getInstance().loadImage(getContext(), url, this, cacheSource, placeholderResId, errorResId);
//                } else {
//                    ImageLoader.getInstance().loadRoundImage(getContext(), url, this, cacheSource, placeholderResId, errorResId, radius);
//                }
//            } else {
//                loadCircle(url);
//            }
        } else {
            loadBlur(url);
        }
    }

    public void loadProgress(String url, OnProgressListener progressListener) {
        ImageLoader.getInstance().loadImageProgress(getContext(), url, this, cacheSource, placeholderResId, errorResId, progressListener);
    }

    public void loadBlur(String url) {
        ImageLoader.getInstance().loadBlurImage(getContext(), url, this, cacheSource, placeholderResId, errorResId);

    }

    public void loadBlur(String url, int blurRadius, int sampling) {
        ImageLoader.getInstance().loadBlurImage(getContext(), url, this, cacheSource, placeholderResId, errorResId, blurRadius, sampling);

    }

    public void load(String url, int topLeftRadius, int topRightRadius, int bottomLeftRadius, int bottomRightRadius) {
        radius = 0;
        this.topLeftRadius = topLeftRadius;
        this.topRightRadius = topRightRadius;
        this.bottomLeftRadius = bottomLeftRadius;
        this.bottomRightRadius = bottomRightRadius;
        setImageShapeAppearance();
        load(url);
    }

    public void loadRoundUrl(String url, int radius) {
        this.radius = radius;
        setImageShapeAppearance();
        load(url);
    }


    public void loadRoundUrl(String url) {
        loadRoundUrl(url, radius);
    }

    public void loadRoundUri(Uri uri) {
        loadRoundUri(uri, radius);
    }

    public void loadRoundUri(Uri uri, int radius) {
        this.radius = radius;
        setImageShapeAppearance();
        ImageLoader.getInstance().loadUriImage(getContext(), uri, this, cacheSource, placeholderResId, errorResId);
//        ImageLoader.getInstance().loadRoundUriImage(getContext(), uri, this, cacheSource, placeholderResId, errorResId, radius);
    }

    public void loadImageProgress(String url, OnProgressListener listener) {
        ImageLoader.getInstance().loadImageProgress(getContext(), url, this, cacheSource, placeholderResId, errorResId, listener);
    }

    public void loadRoundImageProgress(String url, boolean cacheSource, OnProgressListener progressListener) {
        ImageLoader.getInstance().loadRoundImageProgress(getContext(), url, this, cacheSource, placeholderResId, errorResId, radius, progressListener);
    }

    public void loadRoundImageByGlide(String url,int radius) {
        ImageLoader.getInstance().loadRoundImage(getContext(), url, this, cacheSource, placeholderResId, errorResId, radius);
    }

    public void loadImageListener(String url, OnRequestListener listener) {
        ImageLoader.getInstance().loadImage(getContext(), url, this, cacheSource, placeholderResId, errorResId, listener);
    }

    public void loadRoundTop(String url) {
        load(url);
//        ImageLoader.getInstance().loadRoundImageWithCornerType(getContext(), url, this, cacheSource, placeholderResId, errorResId, radius, RoundedCornersTransformation.CornerType.TOP);
    }

    public void loadResource(@RawRes @DrawableRes @Nullable Integer resourceId) {
        ImageLoader.getInstance().loadResourceImage(getContext(), resourceId, this);
    }

    public void loadRoundCenterCropImage(String url) {
        setImageShapeAppearance();
        load(url);
//        ImageLoader.getInstance().loadRoundCenterCropImage(getContext(), url, this, cacheSource, placeholderResId, errorResId, radius);
    }

    @Nullable
    public Bitmap getViewBitmap() {
        if (getDrawable() == null || !(getDrawable() instanceof FastBitmapDrawable)) {
            return null;
        } else {
            return ((FastBitmapDrawable) getDrawable()).getBitmap();
        }
    }

    public void loadData(byte[] data) {
        ImageLoader.getInstance().loadDataImage(getContext(), data, this);
    }

    @Override
    public void draw(Canvas canvas) {
        float strokeRadius = radius;
        if (mStrokeWidth > 0) {
            mPath.reset();
            if (isCircle) {
                strokeRadius = getWidth() / 2;
            }
            mRectF.set(mAntialiasPadding, mAntialiasPadding, getWidth() - mAntialiasPadding, getHeight() - mAntialiasPadding);
            mPath.addRoundRect(mRectF, strokeRadius, strokeRadius, Path.Direction.CW);
            canvas.clipPath(mPath);
        }
        super.draw(canvas);
//        if (mStrokeWidth > 0) {
//            canvas.drawRoundRect(mRectF, strokeRadius, strokeRadius, mStrokePaint);
//        }
    }

    public boolean isBlur() {
        return isBlur;
    }

    public void setBlur(boolean blur) {
        isBlur = blur;
    }
}
