package com.kanzhun.common.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.R;

public class ORoundConstraintLayout extends ConstraintLayout {

    private Path mPath;
    private RectF mRectF;
    private int mCorner;
    private Paint mPaint;


    public ORoundConstraintLayout(@NonNull Context context) {
        super(context);
        init(context, null, 0);
    }

    public ORoundConstraintLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public ORoundConstraintLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    public ORoundConstraintLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
        init(context, attrs, defStyleAttr);
    }

    public void init(Context context, AttributeSet attrs, int defStyleAttr) {
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.common_round_layout, defStyleAttr, 0);
            mCorner = (int) a.getDimensionPixelSize(R.styleable.common_round_layout_common_layout_radius, 0);
        } else {
            mCorner = QMUIDisplayHelper.dp2px(context, 10);
        }
        mRectF = new RectF();
        mPath = new Path();
        mPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setColor(0x00000000);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        mPath.reset();
        mRectF.set(0, 0, getWidth(), getHeight());
        mPath.addRoundRect(mRectF, mCorner, mCorner, Path.Direction.CW);
        canvas.clipPath(mPath);
    }
}
