package com.kanzhun.common.views.image.imageloader.progress;

import android.text.TextUtils;

import com.kanzhun.utils.file.FileUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/21
 */
public class ProgressManager {
    private static final Map<String, OnProgressListener> listenersMap = Collections.synchronizedMap(new HashMap<>());

    private ProgressManager() {
    }

    public static final ProgressResponseBody.InternalProgressListener LISTENER = (url, bytesRead, totalBytes) -> {
        OnProgressListener onProgressListener = getProgressListener(FileUtils.getFileUrl(url));
        if (onProgressListener != null) {
            int percentage = (int) ((bytesRead * 1f / totalBytes) * 100f);
            boolean isComplete = percentage >= 100;
            onProgressListener.onProgress(isComplete, percentage, bytesRead, totalBytes);
            if (isComplete) {
                removeListener(url);
            }
        }
    };

    public static void addListener(String url, OnProgressListener listener) {
        if (!TextUtils.isEmpty(url) && listener != null) {
            listenersMap.put(url, listener);
            listener.onProgress(false, 1, 0, 0);
        }
    }

    public static void removeListener(String url) {
        if (!TextUtils.isEmpty(url)) {
            listenersMap.remove(url);
        }
    }

    public static OnProgressListener getProgressListener(String url) {
        if (TextUtils.isEmpty(url) || listenersMap.size() == 0) {
            return null;
        }
        OnProgressListener listenerWeakReference = listenersMap.get(url);
        if (listenerWeakReference != null) {
            return listenerWeakReference;
        }
        return null;
    }
}
