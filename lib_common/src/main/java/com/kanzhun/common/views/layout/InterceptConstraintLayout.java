package com.kanzhun.common.views.layout;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.constraintlayout.widget.ConstraintLayout;

/**
 * Author: <PERSON>
 * Date: 2019/03/26.
 */
public class InterceptConstraintLayout extends ConstraintLayout {


    private boolean isIntercept;

    public InterceptConstraintLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public boolean isIntercept() {
        return isIntercept;
    }

    public void setIntercept(boolean intercept) {
        isIntercept = intercept;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return isIntercept;
    }
}
