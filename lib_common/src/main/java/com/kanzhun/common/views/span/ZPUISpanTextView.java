package com.kanzhun.common.views.span;

import static androidx.core.util.PatternsCompat.AUTOLINK_WEB_URL;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.text.DynamicLayout;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.style.ClickableSpan;
import android.text.style.ForegroundColorSpan;
import android.text.style.ImageSpan;
import android.text.style.UnderlineSpan;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;

import com.kanzhun.common.R;
import com.kanzhun.common.util.ZPUIDrawableHelper;
import com.kanzhun.common.views.span.callback.OnExpandCollapseClickListener;
import com.kanzhun.common.views.span.callback.OnLineCountObserveListener;
import com.kanzhun.common.views.span.callback.OnLinkClickListener;
import com.kanzhun.common.views.span.custom.CustomImageSpan;
import com.kanzhun.common.views.span.internal.ExpandableStateRecord;
import com.kanzhun.common.views.span.internal.FormatRegulation;
import com.kanzhun.common.views.span.internal.LinkType;
import com.kanzhun.common.views.span.internal.LocalLinkMovementMethod;
import com.kanzhun.common.views.span.internal.Regulation;
import com.kanzhun.common.views.span.internal.StateType;
import com.kanzhun.common.views.span.internal.Uuid;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author: zhouyou
 * Date: 2019-12-19
 */
public class ZPUISpanTextView extends AppCompatTextView {

    private static final int DEF_MAX_LINE = 4;
    public static String TEXT_CONTRACT = "收起";
    public static String TEXT_EXPEND = "展开";
    public static final String Space = " ";
    public static final String DEFAULT_LINK_TEXT = "网页链接";
    public static String IMAGE_TARGET = "图";
    public String target; // = IMAGE_TARGET + TEXT_TARGET;
    public static final String DEFAULT_CONTENT = "                                                                                                                                                                                                                                                                                                                           ";

    private static int retryTime = 0;

    /**
     * http?://([-\\w\\.]+)+(:\\d+)?(/([\\w/_\\.]*(\\?\\S+)?)?)?
     */

//    public static final String regexp = "((http[s]{0,1}|ftp)://[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&*+?:_/=<>]*)?)|((www.)|[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&*+?:_/=<>]*)?)";
//    public static final String regexp = "http?://([-\\w\\.]+)+(:\\d+)?(/([\\w/_\\.]*(\\?\\S+)?)?)?";

    public static final String regexp_mention = "@[\\w\\p{InCJKUnifiedIdeographs}-]{1,26}";
    //匹配自定义链接的正则表达式
//    public static final String self_regex = "\\[([\\w\\p{InCJKUnifiedIdeographs}-]*)]\\([\\w\\p{InCJKUnifiedIdeographs}-]*\\)";
    public static final String CUSTOM_REGEX = "\\[([^\\[]*)\\]\\(([^\\(]*)\\)";

    private TextPaint paint;

    private Context mContext;

    /**
     * 记录当前的model
     */
    private ExpandableStateRecord expandableStateRecord;

    /**
     * 计算的layout
     */
    private DynamicLayout dynamicLayout;

    //hide状态下，展示多少行开始省略
    private int mLimitLines;

    private int currentLines;

    private int mWidth;

    private Drawable linkDrawable = null;

    /**
     * 链接和@用户的事件点击
     */
    private OnLinkClickListener linkClickListener;

    /**
     * 点击展开或者收回按钮的时候 是否真的执行操作
     */
    private boolean isSupportRealExpandOrCollapse = true;

    /**
     * 展开是否自定义样式（加粗+下划线）
     */
    private boolean isSupportCustomExpand = false;

    /**
     * 展开或者收回事件监听
     */
    private OnExpandCollapseClickListener onExpandCollapseClickListener;

    /**
     * 行数监听
     */
    private OnLineCountObserveListener onLineCountObserveListener;

    /**
     * 是否需要收起
     */
    private boolean isSupportCollapse = true;

    /**
     * 匹配规则数据
     */
    private FormatRegulation formatRegulation;

    /**
     * 是否需要展开功能
     */
    private boolean isSupportExpand = true;

    /**
     * 是否需要转换url成网页链接四个字
     */
    private boolean isSupportConvertUrl = false;

    /**
     * 是否需要@用户的功能
     */
    private boolean isSupportMention = false;

    /**
     * 是否需要对链接进行处理
     */
    private boolean isSupportLink = false;

    /**
     * 是否需要对自定义情况进行处理
     */
    private boolean isSupportCustom = false;

    /**
     * 是否需要永远将展开或收回显示在最右边
     */
    private boolean isSupportAlwaysShowRight = false;

    /**
     * 是否需要动画 默认开启动画
     */
    private boolean isSupportAnimation = true;

    private int lineCount;

    private CharSequence mContent;

    /**
     * 展开文字的颜色
     */
    private int expandTextColor;
    /**
     * 展开文字的颜色
     */
    private int mMentionTextColor;

    /**
     * 链接的字体颜色
     */
    private int linkTextColor;
    /**
     * 链接的文案定制
     */
    private String linkTextString;

    /**
     * 自定义规则的字体颜色
     */
    private int customTextColor;

    /**
     * 收起的文字的颜色
     */
    private int collapseTextColor;

    /**
     * 展开的文案
     */
    private String expandString;
    /**
     * 收起的文案
     */
    private String collapseString;

    /**
     * 在收回和展开前面添加的内容
     */
    private String mEndExpandContent;

    /**
     * 在收回和展开前面添加的内容的字体颜色
     */
    private int endExpandTextColor;

    // 是否AttachedToWindow
    private boolean isAttached;

    private LocalLinkMovementMethod localLinkMovementMethod;

    public ZPUISpanTextView(Context context) {
        this(context, null);
    }

    public ZPUISpanTextView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, -1);
    }

    public ZPUISpanTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);

        localLinkMovementMethod = LocalLinkMovementMethod.getInstance();

        setMovementMethod(localLinkMovementMethod);
        addOnAttachStateChangeListener(new OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                if (!isAttached) {
                    doSetContent();
                }
                isAttached = true;
            }

            @Override
            public void onViewDetachedFromWindow(View v) {

            }
        });
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.ZPUISpanTextView,
                    defStyleAttr, 0);

            mLimitLines = a.getInt(R.styleable.ZPUISpanTextView_zpui_stv_max_line, DEF_MAX_LINE);
            isSupportExpand = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_expand, true);
            isSupportCollapse = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_collapse, false);
            isSupportAnimation = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_animation, true);
            isSupportCustom = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_custom, false);
            isSupportMention = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_mention, true);
            isSupportLink = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_link, false);
            isSupportAlwaysShowRight = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_always_showright, false);
            isSupportConvertUrl = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_convert_url, false);
            isSupportCustomExpand = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_custom_expand, false);
            collapseString = a.getString(R.styleable.ZPUISpanTextView_zpui_stv_collapse_text);
            expandString = a.getString(R.styleable.ZPUISpanTextView_zpui_stv_expand_text);
            if (TextUtils.isEmpty(expandString)) {
                expandString = TEXT_EXPEND;
            }
            if (TextUtils.isEmpty(collapseString)) {
                collapseString = TEXT_CONTRACT;
            }
            expandTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_expand_color,
                    ContextCompat.getColor(context, R.color.common_color_7171F6));
            endExpandTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_end_color,
                    ContextCompat.getColor(context, R.color.common_color_AAAAAA));
            collapseTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_collapse_color,
                    ContextCompat.getColor(context, R.color.common_color_7171F6));
            linkTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_link_color,
                    ContextCompat.getColor(context, R.color.common_color_7171F6));
            linkTextString = a.getString(R.styleable.ZPUISpanTextView_zpui_stv_link_text);
            if (TextUtils.isEmpty(linkTextString)) {
                linkTextString = DEFAULT_LINK_TEXT;
            }
            target = IMAGE_TARGET + linkTextString;

            isSupportRealExpandOrCollapse = a.getBoolean(R.styleable.ZPUISpanTextView_zpui_stv_support_real_expand_or_collapse, true);
            customTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_custom_color,
                    ContextCompat.getColor(context, R.color.common_color_7171F6));
            mMentionTextColor = a.getColor(R.styleable.ZPUISpanTextView_zpui_stv_mention_color,
                    ContextCompat.getColor(context, R.color.common_color_7171F6));
            int resId = a.getResourceId(R.styleable.ZPUISpanTextView_zpui_stv_link_res, R.mipmap.common_ic_link);
            linkDrawable = context.getResources().getDrawable(resId);
            currentLines = mLimitLines;
            a.recycle();
        } else {
            linkDrawable = context.getResources().getDrawable(R.mipmap.common_ic_link);
        }

        Drawable drawableTint;
        if (linkDrawable != null) {
            drawableTint = linkDrawable.mutate();
            ZPUIDrawableHelper.setDrawableTintColor(drawableTint, linkTextColor);

            //初始化link的图片
            drawableTint.setBounds(0, 0, 30, 30); //必须设置图片大小，否则不显示
        }

        mContext = context;

        paint = getPaint();
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
    }

    private SpannableStringBuilder setRealContent(CharSequence content) {
        //处理给定的数据
        formatRegulation = getFormatRegulation(content);
        //用来计算内容的大小
        dynamicLayout = new DynamicLayout(formatRegulation.formatContent, paint, mWidth, Layout.Alignment.ALIGN_NORMAL, 1.2f, 0.0f, true);
        //获取行数
        lineCount = dynamicLayout.getLineCount();

        if (onLineCountObserveListener != null) {
            onLineCountObserveListener.onGetLineCount(lineCount, lineCount > mLimitLines);
        }

        if (!isSupportExpand || lineCount <= mLimitLines) {
            //不需要展开功能 直接处理链接模块
            return dealLink(formatRegulation, false);
        } else {
            return dealLink(formatRegulation, true);
        }
    }

    /**
     * 设置追加的内容
     *
     * @param endExpendContent
     */
    public void setEndExpendContent(String endExpendContent) {
        this.mEndExpandContent = endExpendContent;
    }

    /**
     * 设置内容
     *
     * @param content
     */
    public void setContent(final CharSequence content) {
        mContent = content;
        if (isAttached)
            doSetContent();
    }

    public void setContent(final CharSequence content, final FormatRegulation regulation) {
        mContent = content;
        formatRegulation = regulation;
        if (isAttached)
            doSetContent();
    }


    /**
     * 实际设置内容的
     */
    private void doSetContent() {
        if (mContent == null) {
            return;
        }
        currentLines = mLimitLines;

        if (mWidth <= 0) {
            if (getWidth() > 0)
                mWidth = getWidth() - getPaddingLeft() - getPaddingRight();
        }

        if (mWidth <= 0) {
            if (retryTime > 10) {
                setText(DEFAULT_CONTENT);
            }
            this.post(new Runnable() {
                @Override
                public void run() {
                    retryTime++;
                    setContent(mContent.toString());
                }
            });
        } else {
            setRealContent(mContent.toString());
            if(callBack != null){
                callBack.success();
            }
        }
    }
    private SpanCallBack callBack;
    public void setCallBack(SpanCallBack callBack){
        this.callBack = callBack;
    }

    public interface SpanCallBack{
        void success();
    }



    /**
     * 设置最后的收起文案
     *
     * @return
     */
    private String getExpandEndContent() {
        if (TextUtils.isEmpty(mEndExpandContent)) {
            return String.format(Locale.getDefault(), "  %s",
                    collapseString);
        } else {
            return String.format(Locale.getDefault(), "  %s  %s",
                    mEndExpandContent, collapseString);
        }
    }

    /**
     * 设置展开的文案
     *
     * @return
     */
    private String getHideEndContent() {
        if (TextUtils.isEmpty(mEndExpandContent)) {
            return String.format(Locale.getDefault(), isSupportAlwaysShowRight ? "  %s" : "...  %s",
                    expandString);
        } else {
            return String.format(Locale.getDefault(), isSupportAlwaysShowRight ? "  %s  %s" : "...  %s  %s",
                    mEndExpandContent, expandString);
        }
    }

    /**
     * 处理文字中的链接问题
     *
     * @param formatData
     * @param ignoreMore
     */
    private SpannableStringBuilder dealLink(FormatRegulation formatData, boolean ignoreMore) {
        SpannableStringBuilder ssb = new SpannableStringBuilder();
        //获取存储的状态
        if (expandableStateRecord != null && expandableStateRecord.getState() != null) {
            boolean isHide = false;
            if (expandableStateRecord.getState() != null) {
                if (expandableStateRecord.getState().equals(StateType.COLLAPSE)) {
                    //收起
                    isHide = true;
                } else {
                    //展开
                    isHide = false;
                }
            }
            if (isHide) {
                currentLines = mLimitLines + ((lineCount - mLimitLines));
            } else {
                if (isSupportCollapse)
                    currentLines = mLimitLines;
            }
        }
        //处理折叠操作
        if (ignoreMore) {
            if (currentLines < lineCount) {
                int index = currentLines - 1;
                int endPosition = dynamicLayout.getLineEnd(index);
                int startPosition = dynamicLayout.getLineStart(index);
                float lineWidth = dynamicLayout.getLineWidth(index);

                String endString = getHideEndContent();

                //计算原内容被截取的位置下标
                int fitPosition = getFitPosition(endString, endPosition, startPosition, lineWidth, paint.measureText(endString), 0);
                String substring = formatData.formatContent.substring(0, fitPosition);
                if (substring.endsWith("\n")) {
                    substring = substring.substring(0, substring.length() - "\n".length());
                }
                ssb.append(substring);

                if (isSupportAlwaysShowRight) {
                    //计算一下最后一行有没有充满
                    float lastLineWidth = 0;
                    for (int i = 0; i < index; i++) {
                        lastLineWidth += dynamicLayout.getLineWidth(i);
                    }
                    lastLineWidth = lastLineWidth / (index);
                    float emptyWidth = lastLineWidth - lineWidth - paint.measureText(endString);
                    if (emptyWidth > 0) {
                        float measureText = paint.measureText(Space);
                        int count = 0;
                        while (measureText * count < emptyWidth) {
                            count++;
                        }
                        count = count - 1;
                        for (int i = 0; i < count; i++) {
                            ssb.append(Space);
                        }
                    }
                }

                //在被截断的文字后面添加 展开 文字
                ssb.append(endString);

                int expendLength = TextUtils.isEmpty(mEndExpandContent) ? 0 : 2 + mEndExpandContent.length();
                if (isSupportRealExpandOrCollapse) {
                    ssb.setSpan(new ClickableSpan() {
                        @Override
                        public void onClick(View widget) {
                            if (expandableStateRecord != null) {
                                expandableStateRecord.setState(StateType.COLLAPSE);
                                action(expandableStateRecord.getState());
                            } else {
                                action();
                            }

                            if (onExpandCollapseClickListener != null) {
                                onExpandCollapseClickListener.onClick(StateType.EXPAND);
                            }
                        }

                        @Override
                        public void updateDrawState(TextPaint ds) {
                            super.updateDrawState(ds);
                            ds.setColor(expandTextColor);
                            ds.setUnderlineText(false);
                        }
                    }, ssb.length() - expandString.length() - expendLength, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                } else {
                    ssb.setSpan(new ForegroundColorSpan(expandTextColor), ssb.length() - expandString.length() - expendLength, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                }

                checkCustomExpand(ssb, expendLength);
            } else {
                ssb.append(formatData.formatContent);
                if (isSupportCollapse) {
                    String endString = getExpandEndContent();

                    if (isSupportAlwaysShowRight) {
                        //计算一下最后一行有没有充满
                        int index = dynamicLayout.getLineCount() - 1;
                        float lineWidth = dynamicLayout.getLineWidth(index);
                        float lastLineWidth = 0;
                        for (int i = 0; i < index; i++) {
                            lastLineWidth += dynamicLayout.getLineWidth(i);
                        }
                        lastLineWidth = lastLineWidth / (index);
                        float emptyWidth = lastLineWidth - lineWidth - paint.measureText(endString);
                        if (emptyWidth > 0) {
                            float measureText = paint.measureText(Space);
                            int count = 0;
                            while (measureText * count < emptyWidth) {
                                count++;
                            }
                            count = count - 1;
                            for (int i = 0; i < count; i++) {
                                ssb.append(Space);
                            }
                        }
                    }

                    ssb.append(endString);

                    int expendLength = TextUtils.isEmpty(mEndExpandContent) ? 0 : 2 + mEndExpandContent.length();

                    if (isSupportRealExpandOrCollapse) {
                        ssb.setSpan(new ClickableSpan() {
                            @Override
                            public void onClick(View widget) {
                                if (expandableStateRecord != null) {
                                    expandableStateRecord.setState(StateType.EXPAND);
                                    action(expandableStateRecord.getState());
                                } else {
                                    action();
                                }
                                if (onExpandCollapseClickListener != null) {
                                    onExpandCollapseClickListener.onClick(StateType.COLLAPSE);
                                }
                            }

                            @Override
                            public void updateDrawState(TextPaint ds) {
                                super.updateDrawState(ds);
                                ds.setColor(collapseTextColor);
                                ds.setUnderlineText(false);
                            }
                        }, ssb.length() - collapseString.length() - expendLength, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    } else {
                        ssb.setSpan(new ForegroundColorSpan(expandTextColor), ssb.length() - expandString.length() - expendLength, ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    }

                    checkCustomExpand(ssb, expendLength);
                } else {
                    if (!TextUtils.isEmpty(mEndExpandContent)) {
                        ssb.append(mEndExpandContent);
                        ssb.setSpan(new ForegroundColorSpan(endExpandTextColor), ssb.length() - mEndExpandContent.length(), ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
                    }
                }
            }
        } else {
            ssb.append(formatData.formatContent);
            if (!TextUtils.isEmpty(mEndExpandContent)) {
                ssb.append(mEndExpandContent);
                ssb.setSpan(new ForegroundColorSpan(endExpandTextColor), ssb.length() - mEndExpandContent.length(), ssb.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }
        //处理链接或者@用户
        List<Regulation> regulations = formatData.regulations;
        for (Regulation data : regulations) {
            if (ssb.length() >= data.end) {
                if (data.linkType.equals(LinkType.LINK)) {
                    if (isSupportExpand && ignoreMore) {
                        int fitPosition = ssb.length() - getHideEndContent().length();
                        if (data.start < fitPosition) {
                            CustomImageSpan imageSpan = new CustomImageSpan(linkDrawable, ImageSpan.ALIGN_BASELINE);
                            //设置链接图标
                            ssb.setSpan(imageSpan, data.start, data.start + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                            //设置链接文字样式
                            int endPosition = data.end;
                            if (currentLines < lineCount) {
                                if (fitPosition > data.start + 1 && fitPosition < data.end) {
                                    endPosition = fitPosition;
                                }
                            }
                            if (data.start + 1 < fitPosition) {
                                addUrl(ssb, data, endPosition);
                            }
                        }
                    } else {
                        CustomImageSpan imageSpan = new CustomImageSpan(linkDrawable, ImageSpan.ALIGN_BASELINE);
                        //设置链接图标
                        ssb.setSpan(imageSpan, data.start, data.start + 1, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                        addUrl(ssb, data, data.end);
                    }
                } else if (data.linkType.equals(LinkType.MENTION)) {
                    //如果需要展开
                    if (isSupportExpand && ignoreMore) {
                        int fitPosition = ssb.length() - getHideEndContent().length();
                        if (data.start < fitPosition) {
                            int endPosition = data.end;
                            if (currentLines < lineCount) {
                                if (fitPosition < data.end) {
                                    endPosition = fitPosition;
                                }
                            }
                            addMention(ssb, data, endPosition);
                        }
                    } else {
                        addMention(ssb, data, data.end);
                    }
                } else if (data.linkType.equals(LinkType.CUSTOM)) {
                    //自定义
                    //如果需要展开
                    if (isSupportExpand && ignoreMore) {
                        int fitPosition = ssb.length() - getHideEndContent().length();
                        if (data.start < fitPosition) {
                            int endPosition = data.end;
                            if (currentLines < lineCount) {
                                if (fitPosition < data.end) {
                                    endPosition = fitPosition;
                                }
                            }
                            addCustom(ssb, data, endPosition);
                        }
                    } else {
                        addCustom(ssb, data, data.end);
                    }
                }
            }
        }
        //清除链接点击时背景效果
        setHighlightColor(Color.TRANSPARENT);
        //将内容设置到控件中
        setText(ssb);
        return ssb;
    }

    /**
     * 获取需要插入的空格
     *
     * @param emptyWidth
     * @param endStringWidth
     * @return
     */
    private int getFitSpaceCount(float emptyWidth, float endStringWidth) {
        float measureText = paint.measureText(Space);
        int count = 0;
        while (endStringWidth + measureText * count < emptyWidth) {
            count++;
        }
        return --count;
    }


    /**
     * 添加自定义规则
     *
     * @param ssb
     * @param data
     * @param endPosition
     */
    private void addCustom(SpannableStringBuilder ssb, final Regulation data, int endPosition) {
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (linkClickListener != null)
                    linkClickListener.onLinkClickListener(LinkType.CUSTOM, data.customAim, data.customContent);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(customTextColor);
                ds.setUnderlineText(false);
            }
        }, data.start, endPosition, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
    }


    /**
     * 添加@用户的Span
     *
     * @param ssb
     * @param data
     * @param endPosition
     */
    private void addMention(SpannableStringBuilder ssb, final Regulation data, int endPosition) {
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (linkClickListener != null)
                    linkClickListener.onLinkClickListener(LinkType.MENTION, data.url, null);
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(mMentionTextColor);
                ds.setUnderlineText(false);
            }
        }, data.start, endPosition, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
    }

    /**
     * 添加链接的span
     *
     * @param ssb
     * @param data
     * @param endPosition
     */
    private void addUrl(SpannableStringBuilder ssb, final Regulation data, int endPosition) {
        ssb.setSpan(new ClickableSpan() {
            @Override
            public void onClick(View widget) {
                if (linkClickListener != null) {
                    linkClickListener.onLinkClickListener(LinkType.LINK, data.url, null);
                } else {
                    //如果没有设置监听 则调用默认的打开浏览器显示连接
                    Intent intent = new Intent();
                    intent.setAction("android.intent.action.VIEW");
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    Uri url = Uri.parse(data.url);
                    intent.setData(url);
                    mContext.startActivity(intent);
                }
            }

            @Override
            public void updateDrawState(TextPaint ds) {
                ds.setColor(linkTextColor);
                ds.setUnderlineText(false);
            }
        }, data.start + 1, endPosition, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
    }

    /**
     * 设置当前的状态
     *
     * @param type
     */
    public void setCurrStatus(StateType type) {
        action(type);
    }

    private void action() {
        action(null);
    }

    /**
     * 执行展开和收回的动作
     */
    private void action(StateType type) {
        boolean isHide = currentLines < lineCount;
        if (type != null) {
            isSupportAnimation = false;
        }
        if (isSupportAnimation) {
            ValueAnimator valueAnimator = ValueAnimator.ofFloat(0, 1);
            final boolean finalIsHide = isHide;
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    Float value = (Float) animation.getAnimatedValue();
                    if (finalIsHide) {
                        currentLines = mLimitLines + (int) ((lineCount - mLimitLines) * value);
                    } else {
                        if (isSupportCollapse)
                            currentLines = mLimitLines + (int) ((lineCount - mLimitLines) * (1 - value));
                    }
                    setText(setRealContent(mContent));
                }
            });
            valueAnimator.setDuration(100);
            valueAnimator.start();
        } else {
            if (isHide) {
                currentLines = mLimitLines + ((lineCount - mLimitLines));
            } else {
                if (isSupportCollapse)
                    currentLines = mLimitLines;
            }
            setText(setRealContent(mContent));
        }
    }

    /**
     * 计算原内容被裁剪的长度
     *
     * @param endString
     * @param endPosition   指定行最后文字的位置
     * @param startPosition 指定行文字开始的位置
     * @param lineWidth     指定行文字的宽度
     * @param endStringWith 最后添加的文字的宽度
     * @param offset        偏移量
     * @return
     */
    private int recurrenceCount = 0; // 最多三次递归，否则返回最后一次的计算结果

    private int getFitPosition(String endString, int endPosition, int startPosition, float lineWidth,
                               float endStringWith, float offset) {
        //最后一行需要添加的文字的字数
        int position = (int) ((lineWidth - (endStringWith + offset)) * (endPosition - startPosition)
                / lineWidth);

        if (position <= endString.length()) {
            recurrenceCount = 0;
            return endPosition;
        }

        //计算最后一行需要显示的正文的长度
        float measureText = paint.measureText(
                (formatRegulation.formatContent.substring(startPosition, startPosition + position)));

        //如果最后一行需要显示的正文的长度比最后一行的长减去“展开”文字的长度要短就可以了  否则加个空格继续算
        if (measureText <= lineWidth - endStringWith) {
            recurrenceCount = 0;
            return startPosition + position;
        } else {
            if (recurrenceCount >= 3) {
                recurrenceCount = 0;
                return startPosition + position;
            }
            recurrenceCount++;
            int realCount = 1 << recurrenceCount;
            StringBuilder spaceBuilder = new StringBuilder();
            for (int i = 0; i < realCount; i++) {
                spaceBuilder.append(Space);
            }
            return getFitPosition(endString, endPosition, startPosition, lineWidth, endStringWith, offset + paint.measureText(spaceBuilder.toString()));


//            return getFitPosition(endString, endPosition, startPosition, lineWidth, endStringWith, offset + paint.measureText(Space));
        }
    }

    /**
     * 对传入的数据进行正则匹配并处理
     *
     * @param content
     * @return
     */
    @SuppressLint("RestrictedApi")
    private FormatRegulation getFormatRegulation(CharSequence content) {
        FormatRegulation formatRegulation = new FormatRegulation();
        List<Regulation> regulations = new ArrayList<>();
        //对链接进行正则匹配
//        Pattern pattern = Pattern.compile(regexp, Pattern.CASE_INSENSITIVE);
        Pattern pattern = Pattern.compile(CUSTOM_REGEX, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(content);
        StringBuffer newResult = new StringBuffer();
        int start = 0;
        int end = 0;
        int temp = 0;
        Map<String, String> convert = new HashMap<>();
        //对自定义的进行正则匹配
        if (isSupportCustom) {
            List<Regulation> datasMention = new ArrayList<>();
            while (matcher.find()) {
                start = matcher.start();
                end = matcher.end();
                newResult.append(content.toString().substring(temp, start));
                //将匹配到的内容进行统计处理
                String result = matcher.group();
                if (!TextUtils.isEmpty(result)) {
                    //解析数据
                    String aimSrt = getTargetSubstring(result, "[", "]");
                    String contentSrt = getTargetSubstring(result, "(", ")");//result.substring(result.indexOf("(") + 1, result.indexOf(")"));
                    String key = Uuid.getUuid(aimSrt.length());
                    datasMention.add(new Regulation(newResult.length() + 1, newResult.length() + 2 + aimSrt.length(), aimSrt, contentSrt, LinkType.CUSTOM));
                    convert.put(key, aimSrt);
                    newResult.append(" " + key + " ");
                    temp = end;
                }
            }
            regulations.addAll(datasMention);
        }
        //重置状态
        newResult.append(content.toString().substring(end, content.toString().length()));
        content = newResult.toString();
        newResult = new StringBuffer();
        start = 0;
        end = 0;
        temp = 0;

        if (isSupportLink) {
            pattern = AUTOLINK_WEB_URL;
            matcher = pattern.matcher(content);
            while (matcher.find()) {
                start = matcher.start();
                end = matcher.end();
                newResult.append(content.toString().substring(temp, start));
                if (isSupportConvertUrl) {
                    //将匹配到的内容进行统计处理
                    regulations.add(new Regulation(newResult.length() + 1, newResult.length() + 2 + target.length(), matcher.group(), LinkType.LINK));
                    newResult.append(" " + target + " ");
                } else {
                    String result = matcher.group();
                    String key = Uuid.getUuid(result.length());
                    regulations.add(new Regulation(newResult.length(), newResult.length() + 2 + key.length(), result, LinkType.LINK));
                    convert.put(key, result);
                    newResult.append(" " + key + " ");
                }
                temp = end;
            }
        }
        newResult.append(content.toString().substring(end, content.toString().length()));
        //对@用户 进行正则匹配
        if (isSupportMention) {
            pattern = Pattern.compile(regexp_mention, Pattern.CASE_INSENSITIVE);
            matcher = pattern.matcher(newResult.toString());
            List<Regulation> datasMention = new ArrayList<>();
            while (matcher.find()) {
                //将匹配到的内容进行统计处理
                datasMention.add(new Regulation(matcher.start(), matcher.end(), matcher.group(), LinkType.MENTION));
            }
            regulations.addAll(0, datasMention);
        }
        if (!convert.isEmpty()) {
            String resultData = newResult.toString();
            for (Map.Entry<String, String> entry : convert.entrySet()) {
                resultData = resultData.replaceAll(entry.getKey(), entry.getValue());
            }
            newResult = new StringBuffer(resultData);
        }
        formatRegulation.formatContent = newResult.toString();
        formatRegulation.regulations = regulations;
        return formatRegulation;
    }

    /**
     * 判断下标
     *
     * @param result
     * @return
     */
    private String getTargetSubstring(String result, String prefix, String suffix) {
        String targetString = "";
        int startIndex = result.indexOf(prefix) + 1;
        int endIndex = result.indexOf(suffix);
        if (endIndex > startIndex && endIndex <= result.length()) {
            targetString = result.substring(startIndex, endIndex);
        }
        return targetString;
    }

    /**
     * 自定义"更多"文字样式
     */
    private void checkCustomExpand(SpannableStringBuilder ssb, int expendLength) {
        if (isSupportCustomExpand) {
            ssb.setSpan(new UnderlineSpan(), ssb.length() - expandString.length() - expendLength, ssb.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE); // 下划线
        }
    }

    /**
     * 绑定状态
     *
     * @param record
     */
    public void bind(ExpandableStateRecord record) {
        expandableStateRecord = record;
    }

    boolean dontConsumeNonUrlClicks = true;

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        localLinkMovementMethod.setLinkHit(false);
        boolean res = super.onTouchEvent(event);

        if (dontConsumeNonUrlClicks) {
            return localLinkMovementMethod.isLinkHit();
        }

        //防止选择复制的状态不消失
        if (action == MotionEvent.ACTION_UP) {
            this.setTextIsSelectable(false);
        }
        return res;
    }

    public void setOnLineCountObserveListener(OnLineCountObserveListener onLineCountObserveListener) {
        this.onLineCountObserveListener = onLineCountObserveListener;
    }

    public OnLineCountObserveListener getOnLineCountObserveListener() {
        return onLineCountObserveListener;
    }

    public OnLinkClickListener getLinkClickListener() {
        return linkClickListener;
    }

    public void setLinkClickListener(OnLinkClickListener linkClickListener) {
        this.linkClickListener = linkClickListener;
    }

    public boolean isSupportMention() {
        return isSupportMention;
    }

    public void setSupportMention(boolean supportMention) {
        this.isSupportMention = supportMention;
    }

    public Drawable getLinkDrawable() {
        return linkDrawable;
    }

    public void setLinkDrawable(Drawable linkDrawable) {
        this.linkDrawable = linkDrawable;
    }

    public boolean isSupportCollapse() {
        return isSupportCollapse;
    }

    public void setSupportCollapse(boolean supportCollapse) {
        this.isSupportCollapse = supportCollapse;
    }

    public boolean isSupportExpend() {
        return isSupportExpand;
    }

    public void setSupportExpend(boolean supportExpend) {
        this.isSupportExpand = supportExpend;
    }

    public boolean isSupportAnimation() {
        return isSupportAnimation;
    }

    public void setSupportAnimation(boolean supportAnimation) {
        this.isSupportAnimation = supportAnimation;
    }

    public int getExpandableLineCount() {
        return lineCount;
    }

    public void setExpandableLineCount(int lineCount) {
        this.lineCount = lineCount;
    }

    public int getExpandTextColor() {
        return expandTextColor;
    }

    public void setExpandTextColor(int mExpandTextColor) {
        this.expandTextColor = mExpandTextColor;
    }

    public int getExpandableLinkTextColor() {
        return linkTextColor;
    }

    public void setExpandableLinkTextColor(int linkTextColor) {
        this.linkTextColor = linkTextColor;
    }

    public void setLinkTextString(String mLinkTextString) {
        if (TextUtils.isEmpty(mLinkTextString)) {
            mLinkTextString = DEFAULT_LINK_TEXT;
        }
        linkTextString = mLinkTextString;
        target = IMAGE_TARGET + linkTextString;
    }

    public String getLinkTextString() {
        return linkTextString;
    }

    public int getCollapseTextColor() {
        return collapseTextColor;
    }

    public void setCollapseTextColor(int collapseTextColor) {
        this.collapseTextColor = collapseTextColor;
    }

    public String getExpandString() {
        return expandString;
    }

    public void setExpandString(String expandString) {
        this.expandString = expandString;
    }

    public String getCollapseString() {
        return collapseString;
    }

    public void setCollapseString(String collapseString) {
        this.collapseString = collapseString;
    }

    public int getEndExpandTextColor() {
        return endExpandTextColor;
    }

    public void setEndExpandTextColor(int endExpandTextColor) {
        this.endExpandTextColor = endExpandTextColor;
    }

    public boolean isSupportLink() {
        return isSupportLink;
    }

    public void setSupportLink(boolean mNeedLink) {
        this.isSupportLink = mNeedLink;
    }

    public int getCustomTextColor() {
        return customTextColor;
    }

    public void setCustomTextColor(int mCustomTextColor) {
        this.customTextColor = mCustomTextColor;
    }

    public boolean isSupportCustom() {
        return isSupportCustom;
    }

    public void setSupportCustom(boolean supportCustom) {
        this.isSupportCustom = supportCustom;
    }

    public boolean isSupportAlwaysShowRight() {
        return isSupportAlwaysShowRight;
    }

    public void setSupportAlwaysShowRight(boolean supportAlwaysShowRight) {
        this.isSupportAlwaysShowRight = supportAlwaysShowRight;
    }

    public OnExpandCollapseClickListener getExpandCollpaseClickListener() {
        return onExpandCollapseClickListener;
    }

    public void setExpandCollpaseClickListener(OnExpandCollapseClickListener onExpandCollapseClickListener) {
        this.onExpandCollapseClickListener = onExpandCollapseClickListener;
    }

    public void setExpandCollpaseClickListener(OnExpandCollapseClickListener onExpandCollapseClickListener, boolean supportRealExpandOrCollapse) {
        this.onExpandCollapseClickListener = onExpandCollapseClickListener;
        this.isSupportRealExpandOrCollapse = supportRealExpandOrCollapse;
    }
}
