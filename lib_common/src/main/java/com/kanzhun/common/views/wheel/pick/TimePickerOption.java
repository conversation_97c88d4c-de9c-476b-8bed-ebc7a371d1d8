package com.kanzhun.common.views.wheel.pick;

import android.view.Gravity;

import com.kanzhun.common.util.LDate;
import com.kanzhun.common.views.wheel.pick.listener.OnTimeSelectChangeListener;

import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2022/3/15.
 */
public class TimePickerOption extends PickerOptions {
    //time picker
    public boolean[] type = new boolean[]{true, true, true, false, false, false};//显示类型，默认显示： 年月日
    public Calendar date;//当前选中时间
    public Calendar startDate;//开始时间
    public Calendar endDate;//终止时间
    public int startYear;//开始年份
    public int endYear;//结尾年份
    public boolean cyclic = false;//是否循环
    public String label_year, label_month, label_day, label_hours, label_minutes, label_seconds;//单位
    public int x_offset_year, x_offset_month, x_offset_day, x_offset_hours, x_offset_minutes, x_offset_seconds;//单位
    public boolean isLunarCalendar = false;//是否显示农历
    public int textGravity = Gravity.CENTER;
    public OnTimeSelectChangeListener timeSelectChangeListener;

    public void setBirthdayRage(String minData,String maxDate) {
        startDate = LDate.getTextToCalendar(minData);
        endDate = LDate.getTextToCalendar(maxDate);
    }

    public void selectData(Date date) {
        this.date.setTime(date);
    }

}
