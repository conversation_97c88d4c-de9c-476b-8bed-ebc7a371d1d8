package com.kanzhun.common.views.layout;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/18
 */
public class InterceptLinerLayout extends LinearLayout {

    public InterceptLinerLayout(@NonNull Context context) {
        this(context, null);
    }

    public InterceptLinerLayout(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public InterceptLinerLayout(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return true;
    }
}
