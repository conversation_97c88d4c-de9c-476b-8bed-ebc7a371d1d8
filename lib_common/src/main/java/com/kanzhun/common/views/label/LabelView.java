package com.kanzhun.common.views.label;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import androidx.annotation.ColorInt;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.views.image.OImageView;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.kanzhun.common.R;

public class LabelView extends FrameLayout {

    public LabelView(@NonNull Context context) {
        super(context);
        initView(context);
    }

    public LabelView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initView(context);
    }

    private void initView(Context context) {
        LayoutInflater.from(context).inflate(R.layout.common_item_label,this,true);
    }

    public LabelView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView(context);
    }


    public void setText(String text) {
        QMUIRoundButton button = findViewById(R.id.idLabel);
        button.setText(text);
    }

    public void setTextColor(@ColorInt int color) {
        QMUIRoundButton button = findViewById(R.id.idLabel);
        button.setTextColor(color);
    }

    public void setImage(String icon){
        OImageView imageView = findViewById(R.id.idImageView);
        if(TextUtils.isEmpty(icon)){
            imageView.setVisibility(GONE);
        }else {
            imageView.setVisibility(VISIBLE);
            imageView.load(icon);
        }
    }

    public void setImageResource(int resId){
        OImageView imageView = findViewById(R.id.idImageView);
        if(resId == 0){
            imageView.setVisibility(GONE);
        }else {
            imageView.setVisibility(VISIBLE);
            imageView.loadResource(resId);
        }
    }
}
