package com.kanzhun.common.kotlin.ui.statelayout

import android.widget.TextView
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.airbnb.lottie.LottieAnimationView
import com.kanzhun.common.R
import com.kanzhun.common.kotlin.entity.StatePageBean
import com.kanzhun.common.kotlin.ext.toResourceColor


class StateLayoutManager : Observer<StatePageBean> {

    private var mStateLayout: StateLayout? = null

    companion object {
        //全局配置
        fun init() {
            StateConfig.apply {
                emptyLayout = R.layout.common_default_empty_layout // 配置全局的空布局
                errorLayout = R.layout.common_default_error_layout  // 配置全局的错误布局
                loadingLayout = R.layout.common_default_loading_layout  // 配置全局的加载中布局
                //设置重试控件id
                setRetryIds(R.id.btn_retry)
                //回调数据到loading view,针对全局配置有效
                onLoading {
                    if (it is StatePageBean) {
                        if (!it.text.isNullOrBlank()) {
                            findViewById<TextView>(R.id.tv_desc).text = it.text
                        }
                    }
                }
                //回调数据到empty view,针对全局配置有效
                onEmpty {
                    if (it is StatePageBean) {
                        if (!it.text.isNullOrBlank()) {
                            findViewById<TextView>(R.id.tv_desc).text = it.text
                        }
                    }
                }
                //回调数据到error view,针对全局配置有效
                onError {
                    if (it is StatePageBean) {
                        if (!it.text.isNullOrBlank()) {
                            findViewById<TextView>(R.id.tv_desc).text = it.text
                        }

                        if (!it.btnText.isNullOrBlank()) {
                            findViewById<TextView>(R.id.btn_retry).text = it.btnText
                        }

                        if (!it.lottieName.isNullOrEmpty()) {
                            findViewById<LottieAnimationView>(R.id.iv_error).setAnimation(it.lottieName)
                            findViewById<LottieAnimationView>(R.id.iv_error).imageAssetsFolder = it.lottieDir
                        }
                        this.setBackgroundColor(R.color.common_translate.toResourceColor())
                    }
                }
            }

        }
    }

    fun observerStateLayout(stateLayout: StateLayout?, owner: LifecycleOwner, liveData: MutableLiveData<StatePageBean>, onRetry: () -> Unit) {
        mStateLayout = stateLayout
        stateLayout?.run {
            onRefresh {
                onRetry()
            }
            liveData.observe(owner, this@StateLayoutManager)
        }

    }

    override fun onChanged(t: StatePageBean) {
        mStateLayout?.run {
            when (t.status) {
                Status.LOADING -> {
                    showLoading(t)
                }

                Status.EMPTY -> {
                    showEmpty(t)
                }

                Status.ERROR -> {
                    showError(t)
                }

                Status.CONTENT -> {
                    showContent()
                }

                else -> {
                    showContent()

                }
            }
        }

    }


}