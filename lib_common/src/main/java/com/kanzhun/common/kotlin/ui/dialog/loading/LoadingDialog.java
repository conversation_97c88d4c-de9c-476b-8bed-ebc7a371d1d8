package com.kanzhun.common.kotlin.ui.dialog.loading;

import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.kanzhun.common.R;
import com.kanzhun.common.databinding.CommonLoadingDialogViewBinding;

public class LoadingDialog extends Dialog {

    private CommonLoadingDialogViewBinding dialogViewBinding;

    public LoadingDialog(Context context) {
        super(context, R.style.common_progress_bar);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialogViewBinding = CommonLoadingDialogViewBinding.inflate(LayoutInflater.from(context));
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(dialogViewBinding.getRoot());
        Window window = getWindow();
        if (window != null) {
            QMUIStatusBarHelper.translucent(window, getContext().getResources().getColor(R.color.common_translate));
            window.setBackgroundDrawable(new ColorDrawable(Color.parseColor("#B3FFFFFF")));
            window.setGravity(Gravity.CENTER);
            window.setLayout(ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT);
        }
    }

    public void setShowTitle(String text) {
        dialogViewBinding.tvDesc.setText(text);
    }

    public void show(String text) {
        if (TextUtils.isEmpty(text)) {
            dialogViewBinding.tvDesc.setVisibility(View.GONE);
        } else {
            dialogViewBinding.tvDesc.setVisibility(View.VISIBLE);
        }
        if (!isShowing()) {
            this.show();
        }
        dialogViewBinding.tvDesc.setText(text);
    }
}
