package com.kanzhun.common.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.LayoutRes;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.ViewModelProvider;

import com.kanzhun.common.base.BaseViewModel;
import com.kanzhun.common.util.BaseUserInfoNavigation;
import com.kanzhun.common.views.WaterMarkBg;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.jurisdiction.JurisdictionUtils;

import java.lang.reflect.ParameterizedType;
import java.util.Arrays;

public abstract class BaseVMActivity<D extends ViewDataBinding, M extends BaseViewModel> extends BaseActivity {
    private D mDataBinding;
    private M mViewModel;
    //    private ViewModelProvider mProvider;
    private View waterView;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mDataBinding = DataBindingUtil.setContentView(this, getContentLayoutId());
//        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(this.getApplication());
//        mProvider = new ViewModelProvider(this, factory);
        Class<M> entityClass = (Class<M>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
//        mViewModel = mProvider.get(entityClass);
        mViewModel = new ViewModelProvider(this).get(entityClass);
        if (getBindingVariable() > 0) {
            mDataBinding.setVariable(getBindingVariable(), mViewModel);
        }
        if (getCallbackVariable() >= 0 && getCallback() != null) {
            mDataBinding.setVariable(getCallbackVariable(), getCallback());
        }
        mDataBinding.executePendingBindings();
        float alpha = showWaterMark() ? 0.02f : 0.007f;
        addWatermark(alpha);
    }

    public void addWatermark(float alpha) {
        try {
            String tips = String.valueOf(BaseUserInfoNavigation.getUserId());
            if (!TextUtils.isEmpty(tips)) {
                if (waterView == null) {
                    waterView = new View(this);
                    waterView.setBackground(new WaterMarkBg(this, Arrays.asList(BaseUserInfoNavigation.getUserName() + " " + BaseUserInfoNavigation.getUserId()), -17, 12, 0xff000000, 100));
                    waterView.setAlpha(alpha);
                    getWindow().addContentView(waterView, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
                } else {
                    waterView.setAlpha(alpha);
                }
            }
        } catch (Throwable e) {

        }
    }

    public boolean showWaterMark() {
        return SettingBuilder.getInstance().getJurisdictionVisible(JurisdictionUtils.WATER_BG_VISIBLE) > 0;
    }

    @LayoutRes
    public abstract int getContentLayoutId();

    public abstract int getCallbackVariable();

    public abstract Object getCallback();

    public abstract int getBindingVariable();

    public D getDataBinding() {
        return mDataBinding;
    }

    public M getViewModel() {
        return mViewModel;
    }

//    public ViewModelProvider getViewModelProvider() {
//        return mProvider;
//    }
}
