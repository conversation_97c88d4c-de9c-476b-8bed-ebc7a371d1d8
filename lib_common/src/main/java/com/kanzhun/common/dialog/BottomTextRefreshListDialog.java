package com.kanzhun.common.dialog;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.PagerSnapHelper;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.kanzhun.common.R;
import com.kanzhun.common.dialog.model.SelectBottomBean;
import com.kanzhun.utils.base.LList;

import java.util.List;


/**
 * Created by monch on 2017/7/11.
 */

public class BottomTextRefreshListDialog extends CommonBaseDialog<BottomTextRefreshListDialog.Builder> {
    protected BaseQuickAdapter<SelectBottomBean, BaseViewHolder> adapter;
    protected PagerSnapHelper pagerSnapHelper;
    protected RecyclerView recyclerView;

    public BottomTextRefreshListDialog(Builder builder, Context context) {
        super(builder, context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(builder.getLayoutId());
        setWindowParams();
    }

    @Override
    protected void onCreate(Window window) {
        window.getDecorView().setBackgroundColor(Color.TRANSPARENT);
        WindowManager.LayoutParams p = window.getAttributes();
        p.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE;
        TextView title = findViewById(R.id.tv_title);
        title.setText(builder.showTitle);
        View cancel = findViewById(R.id.tv_cancel);
        if (cancel != null) {
            cancel.setVisibility(builder.hindCancel ? View.GONE : View.VISIBLE);
            cancel.setOnClickListener(this);
        }
        View sure = findViewById(R.id.tv_sure);
        if (sure != null) {
            sure.setVisibility(builder.hindCancel ? View.GONE : View.VISIBLE);
            sure.setOnClickListener(this);
        }
        if (builder.itemLayout > 0 && !LList.isEmpty(builder.data)) {
            adapter = new BaseQuickAdapter<SelectBottomBean, BaseViewHolder>(builder.itemLayout, builder.data) {
                @Override
                protected void convert(@NonNull BaseViewHolder baseViewHolder, SelectBottomBean selectBottomBean) {
                    baseViewHolder.setText(R.id.tv_name, selectBottomBean.content);
                }
            };
            if (builder.onBottomItemClickListener != null) {
                adapter.setOnItemClickListener(new com.chad.library.adapter.base.listener.OnItemClickListener() {
                    @Override
                    public void onItemClick(BaseQuickAdapter adapter, View view, int position) {
                        builder.data.get(selectPosition).type = 0;
                        selectPosition = position;
                        builder.data.get(selectPosition).type = 1;
                        adapter.notifyDataSetChanged();
                    }
                });
            }
            recyclerView = findViewById(R.id.recycler);
            if (builder.spanCount > 0) {
                recyclerView.setLayoutManager(new GridLayoutManager(getContext(), builder.spanCount));
            } else {
                LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
                linearLayoutManager.setOrientation(LinearLayoutManager.HORIZONTAL);
                recyclerView.setLayoutManager(linearLayoutManager);
            }
            pagerSnapHelper = new PagerSnapHelper();
            pagerSnapHelper.attachToRecyclerView(recyclerView);
            recyclerView.setAdapter(adapter);

            if(builder.select > -1 && builder.data.size() > builder.select){
                builder.data.get(builder.select).type = 1;
                selectPosition = builder.select;
            }
            adapter.setList(builder.data);
        }
    }

    private int selectPosition;

    @Override
    public void onClick(View view) {
        super.onClick(view);
        if (view.getId() == R.id.tv_cancel) {
            dismiss();
        }
        if (view.getId() == R.id.tv_sure){
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
            int position ;
            if (linearLayoutManager.findFirstVisibleItemPosition() == recyclerView.getAdapter().getItemCount() - 1){
                position = 0;
                recyclerView.scrollToPosition(position);
            }else {
                position = linearLayoutManager.findFirstVisibleItemPosition()+1;
                recyclerView.smoothScrollToPosition(position);
            }

        }
    }

    public void show(boolean canceledOnTouchOutside) {
        setCanceledOnTouchOutside(canceledOnTouchOutside);
        super.show();
    }

    public static class Builder extends CommonBaseDialog.Builder<Builder, BottomTextRefreshListDialog> {

        protected String showTitle = "";
        protected boolean hindCancel = false;
        protected int spanCount;
        protected int layoutId;
        protected int select = -1;
        protected int itemLayout;
        protected List<SelectBottomBean> data;
        protected OnBottomItemClickListener onBottomItemClickListener;

        public Builder(Context context) {
            super(context);
            setLayoutId(R.layout.common_dialog_text_refresh);
            setItemLayout(R.layout.common_dialog_text_refresh_item);
            setGravity(Gravity.BOTTOM);
            setAnimationStyle(R.style.common_window_bottom_to_top_anim);
            setCanceledOnTouchOutside(true);
        }

        @Override
        public BottomTextRefreshListDialog createDialog() {
            return new BottomTextRefreshListDialog(this, context);
        }

        public Builder setShowTitle(String showTitle) {
            this.showTitle = showTitle;
            return this;
        }

        public Builder setHindCancel(boolean hindCancel) {
            this.hindCancel = hindCancel;
            return this;
        }

        public Builder setSpanCount(int spanCount) {
            this.spanCount = spanCount;
            return this;
        }

        public Builder setLayoutId(int layoutId) {
            this.layoutId = layoutId;
            return this;
        }

        public Builder setSelect(int select) {
            this.select = select;
            return this;
        }

        public int getLayoutId() {
            return layoutId;
        }

        public Builder setItemLayout(int itemLayout) {
            this.itemLayout = itemLayout;
            return this;
        }

        public Builder setData(List<SelectBottomBean> data) {
            this.data = data;
            return this;
        }

        public Builder setOnBottomItemClickListener(OnBottomItemClickListener onBottomItemClickListener) {
            this.onBottomItemClickListener = onBottomItemClickListener;
            return this;
        }
    }

    public interface OnBottomItemClickListener {
        void onBottomItemClick(View view, int pos, SelectBottomBean bottomBean);
    }
}
