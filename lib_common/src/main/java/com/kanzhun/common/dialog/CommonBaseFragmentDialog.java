package com.kanzhun.common.dialog;

import android.content.Context;
import android.content.DialogInterface;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDialogFragment;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.R;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.base.BaseViewModel;
import com.kanzhun.common.kotlin.ui.dialog.loading.LoadingDialog;

import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/3
 */
public abstract class CommonBaseFragmentDialog<D extends ViewDataBinding, M extends BaseViewModel> extends AppCompatDialogFragment {
    private D mDataBinding;
    private M mFragmentViewModel;
    protected FragmentActivity activity;
    protected Builder builder;

    private Handler progressHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            progressDialog.show(msg.obj.toString());
            return false;
        }
    });

    protected CommonBaseFragmentDialog(Builder builder) {
        this.builder = builder;
    }

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (activity == null) {
            activity = (FragmentActivity) context;
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NO_TITLE, R.style.common_dialog);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        super.onCreateView(inflater, container, savedInstanceState);
        mDataBinding = DataBindingUtil.inflate(inflater, getContentLayoutId(), container, false);
        View view = mDataBinding.getRoot();
        initViewHolder();
        initFragment();
        return view;
    }

    private void initViewHolder() {
        ViewModelProvider.AndroidViewModelFactory factory = ViewModelProvider.AndroidViewModelFactory.getInstance(BaseApplication.getApplication());
        /**
         * owner为fragment本身
         */
        ViewModelProvider providerOwner = new ViewModelProvider(this, factory);
        Class<M> entityClassOwner = (Class<M>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[1];
        mFragmentViewModel = providerOwner.get(entityClassOwner);
        if (getBindingVariable() > 0) {
            mDataBinding.setVariable(getBindingVariable(), mFragmentViewModel);
        }
        if (getCallbackVariable() >= 0 && getCallback() != null) {
            mDataBinding.setVariable(getCallbackVariable(), getCallback());
        }
        mDataBinding.executePendingBindings();
    }

    @Override
    public void onStart() {
        setWindowParams();
        super.onStart();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        getDialog().setCancelable(builder.getCancelable());
        getDialog().setCanceledOnTouchOutside(builder.getCanceledOnTouchOutside());
        super.onActivityCreated(savedInstanceState);
    }

    protected void setWindowParams() {
        Window window = getDialog().getWindow();
        WindowManager.LayoutParams lp = window.getAttributes();
        lp.width = builder.with; //设置宽度
        lp.height = builder.height;
        lp.gravity = builder.gravity;
        window.getDecorView().setPadding(QMUIDisplayHelper.dp2px(getContext(), builder.paddingLeft),
                QMUIDisplayHelper.dp2px(getContext(), builder.paddingTop),
                QMUIDisplayHelper.dp2px(getContext(), builder.paddingRight),
                QMUIDisplayHelper.dp2px(getContext(), builder.paddingBottom));
        if (builder.animationStyle > 0) {
            window.setWindowAnimations(builder.animationStyle);
        }
        window.setAttributes(lp);

    }

    public abstract int getContentLayoutId();

    protected abstract void initFragment();

    public abstract int getCallbackVariable();

    public abstract Object getCallback();

    public abstract int getBindingVariable();

    public D getDataBinding() {
        return mDataBinding;
    }

    public M getViewModel() {
        return mFragmentViewModel;
    }

    public void show(final FragmentActivity activity) {
        show(activity.getSupportFragmentManager(), "");
    }


    @Override
    public void onDestroy() {
        dismissProgressDialog();
        super.onDestroy();
        if (progressHandler != null) {
            progressHandler.removeCallbacksAndMessages(null);
            progressHandler = null;
        }
    }

    /**
     * 加载框
     */
    private LoadingDialog progressDialog;

    public void showProgressDialog(int resId) {
        String text = getResources().getString(resId);
        showProgressDialog(text);
    }

    public void showProgressDialog(String text) {
        showProgressDialog(text, false);
    }

    public void showProgressDialog(int resId, boolean flag) {
        String text = getResources().getString(resId);
        showProgressDialog(text, flag);
    }

    public void showProgressDialog(String text, boolean flag) {
        try {
            if (activity == null || activity.isFinishing()) return;
            if (progressDialog == null) {
                progressDialog = new LoadingDialog(activity);
            }
            progressDialog.setCancelable(flag);
            progressDialog.setOnDismissListener(new DialogInterface.OnDismissListener() {
                @Override
                public void onDismiss(DialogInterface dialog) {
                    dismissProgressDialog();
                }
            });
            progressHandler.removeMessages(1);
            Message msg = Message.obtain();
            msg.what = 1;
            msg.obj = text; //携带当前值
            progressHandler.sendMessageDelayed(msg, 300);
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public void dismissProgressDialog() {
        try {
            progressHandler.removeMessages(1);
            if (progressDialog != null) {
                progressDialog.dismiss();
            }
            progressDialog = null;
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }

    public static class Builder {

        protected Context context;
        protected boolean canceledOnTouchOutside = true;
        protected boolean cancelable;
        protected CommonBaseDialog.OnItemClickListener listener;
        protected int gravity = Gravity.CENTER;
        protected int paddingTop;
        protected int paddingLeft = 24;
        protected int paddingRight = 24;
        protected int paddingBottom;
        protected int animationStyle;
        protected int height = WindowManager.LayoutParams.WRAP_CONTENT;
        protected int with = WindowManager.LayoutParams.MATCH_PARENT;
        protected List<Integer> listenedIds = new ArrayList<Integer>();

        public Builder(Context context) {
            this.context = context;
        }


        public boolean getCanceledOnTouchOutside() {
            return canceledOnTouchOutside;
        }

        public Builder setCanceledOnTouchOutside(boolean canceledOnTouchOutside) {
            this.canceledOnTouchOutside = canceledOnTouchOutside;
            return this;
        }

        public boolean getCancelable() {
            return cancelable;
        }

        public Builder setCancelable(boolean cancelable) {
            this.cancelable = cancelable;
            return this;
        }

        public Builder setOnItemClickListener(CommonBaseDialog.OnItemClickListener listener) {
            this.listener = listener;
            return this;
        }

        public Builder setGravity(int gravity) {
            this.gravity = gravity;
            return this;
        }

        public Builder setPadding(int paddingLeft, int paddingTop, int paddingRight, int paddingBottom) {
            this.paddingBottom = paddingBottom;
            this.paddingLeft = paddingLeft;
            this.paddingRight = paddingRight;
            this.paddingTop = paddingTop;
            return this;

        }

        public Builder setPadding(int paddingLeft, int paddingRight) {
            this.paddingLeft = paddingLeft;
            this.paddingRight = paddingRight;
            return this;

        }

        public Builder setAnimationStyle(int animationStyle) {
            this.animationStyle = animationStyle;
            return this;
        }


        public Builder setHeight(int height) {
            this.height = height;
            return this;
        }

        public Builder setWith(int with) {
            this.with = with;
            return this;
        }
    }
}
