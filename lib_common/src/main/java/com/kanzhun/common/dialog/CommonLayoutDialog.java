package com.kanzhun.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.SparseArray;
import android.view.View;
import android.widget.TextView;


public class CommonLayoutDialog extends CommonBaseDialog<CommonLayoutDialog.Builder> {

    protected CommonLayoutDialog(Builder builder, Context context) {
        super(builder, context);
    }

    protected CommonLayoutDialog(Builder builder, Context context, int style) {
        super(builder, context, style);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (builder.getContentView() != null) {
            setContentView(builder.contentView);
        } else {
            setContentView(builder.getLayoutId());
        }
        setWindowParams();
        setViewClick();
        SparseArray<String> displayTextList = builder.displayTextList;
        for (int index = 0; index < displayTextList.size(); index++) {
            int id = builder.displayTextList.keyAt(index);
            View view = findViewById(id);
            if (view != null && view instanceof TextView) {
                String text = displayTextList.get(id);
                if (TextUtils.isEmpty(text)) {
                    view.setVisibility(View.GONE);
                } else {
                    ((TextView) view).setText(text);
                }
            }
        }
        SparseArray<CharSequence> linkTextList = builder.linkTextList;
        for (int index = 0; index < linkTextList.size(); index++) {
            int id = linkTextList.keyAt(index);
            View view = findViewById(id);
            if (view != null && view instanceof TextView) {
                CharSequence text = linkTextList.get(id);
                if (TextUtils.isEmpty(text)) {
                    view.setVisibility(View.GONE);
                } else {
                    TextView textView = (TextView) view;
                    textView.setMovementMethod(LinkMovementMethod.getInstance());
                    textView.setText(text);
                }
            }
        }
    }

    public static class Builder extends CommonBaseDialog.Builder<CommonLayoutDialog.Builder, CommonLayoutDialog> {
        protected int layoutId;
        protected SparseArray<String> displayTextList = new SparseArray<String>();
        protected SparseArray<CharSequence> linkTextList = new SparseArray<CharSequence>();
        protected View contentView;

        public Builder(Context context) {
            super(context);
        }

        public CommonLayoutDialog.Builder setLayoutId(int layoutId) {
            this.layoutId = layoutId;
            return this;
        }

        public int getLayoutId() {
            return layoutId;
        }

        public View getContentView() {
            return contentView;
        }

        public CommonLayoutDialog.Builder setContentView(View contentView) {
            this.contentView = contentView;
            return this;
        }

        public CommonLayoutDialog.Builder setDisplayTextById(int id, String text) {
            this.displayTextList.put(id, text);
            return this;
        }

        public CommonLayoutDialog.Builder setLinkTextById(int id, CharSequence text) {
            this.linkTextList.put(id, text);
            return this;
        }

        @Override
        public CommonLayoutDialog createDialog() {
            return new CommonLayoutDialog(this, context);
        }

        @Override
        public CommonLayoutDialog createDialog(int style) {
            return new CommonLayoutDialog(this, context, style);
        }

        @Override
        public CommonLayoutDialog create() {
            return super.create();
        }
    }
}

