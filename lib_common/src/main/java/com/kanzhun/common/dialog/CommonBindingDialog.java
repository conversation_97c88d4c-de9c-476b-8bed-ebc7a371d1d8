package com.kanzhun.common.dialog;

import android.content.Context;
import android.os.Bundle;
import android.util.SparseArray;
import android.view.LayoutInflater;

import androidx.annotation.LayoutRes;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;

/**
 * <AUTHOR>
 * @date 2022/2/8.
 */
public class CommonBindingDialog extends CommonBaseDialog<CommonBindingDialog.Builder> {

    protected CommonBindingDialog(Builder builder, Context context) {
        super(builder, context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(builder.getBinding().getRoot());
        setWindowParams();
        setViewClick();
        for (int index = 0; index < builder.variableList.size(); index++) {
            int variableId = builder.variableList.keyAt(index);
            if (variableId > 0) {
                Object value = builder.variableList.get(variableId);
                builder.binding.setVariable(variableId, value);
            }
        }
        builder.binding.executePendingBindings();
    }

    public static class Builder<V extends ViewDataBinding> extends CommonBaseDialog.Builder<Builder, CommonBindingDialog> {
        protected V binding;
        protected SparseArray<Object> variableList = new SparseArray<Object>();
        protected CommonBindingDialog dialog;

        public Builder(Context context, @LayoutRes int layoutId) {
            super(context);
            binding = DataBindingUtil.inflate(LayoutInflater.from(context), layoutId, null, false);
        }

        public V getBinding() {
            return binding;
        }

        public CommonBindingDialog getDialog() {
            return dialog;
        }

        public CommonBindingDialog.Builder addVariable(int variableId, @Nullable Object value) {
            this.variableList.put(variableId, value);
            return this;
        }

        @Override
        public CommonBindingDialog createDialog() {
            dialog = new CommonBindingDialog(this, context);
            return dialog;
        }

        @Override
        public CommonBindingDialog create() {
            return super.create();
        }
    }
}
