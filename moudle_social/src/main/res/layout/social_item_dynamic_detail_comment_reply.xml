<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="160dp"
            android:fontFamily="sans-serif-medium"
            android:onClick="@{v->callback.jumpToUserDynamicActivity(item.userId)}"
            android:text="@{item.nickName}"
            android:textColor="@color/common_color_7171F6"
            android:textSize="@dimen/common_text_sp_14"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="超过十个字缩略展示啦啦啦" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:paddingTop="1dp"
            android:paddingBottom="1dp"
            android:gravity="center"
            android:text="@string/social_author"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="@+id/iv_avatar"
            app:layout_constraintStart_toEndOf="@+id/tv_name"
            app:layout_constraintTop_toTopOf="@+id/iv_avatar"
            app:qmui_backgroundColor="@color/common_color_7171FF"
            app:qmui_radius="10dp"
            app:visibleGone="@{item.isOwner==1}" />

        <LinearLayout
            android:id="@+id/ll_reply"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="4dp"
                android:paddingRight="4dp"
                android:fontFamily="sans-serif-medium"
                android:text="@string/social_dynamic_detail_comment_reply_title"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"  />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxWidth="160dp"
                android:fontFamily="sans-serif-medium"
                android:onClick="@{v->callback.jumpToUserDynamicActivity(item.replyUserId)}"
                android:text="@{item.replyNickName}"
                android:textColor="@color/common_color_7171F6"
                android:textSize="@dimen/common_text_sp_14"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end"
                tools:text="aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="2dp"
            android:fontFamily="sans-serif-medium"
            android:text=":"
            android:textColor="@color/common_color_7171F6"
            android:textSize="@dimen/common_text_sp_14"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:fontFamily="sans-serif-medium"
            android:text="@{item.content}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_14"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"/>
    </LinearLayout>

    <data>
        <import type="android.text.TextUtils" />
        <import type="com.kanzhun.marry.social.R" />
        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.DynamicDetailCallback" />

        <variable
            name="item"
            type="com.kanzhun.marry.social.api.model.DynamicReplyBean" />
    </data>
</layout>