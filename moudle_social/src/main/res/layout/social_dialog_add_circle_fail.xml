<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="312dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:orientation="vertical">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="210dp"
        android:scaleType="fitXY"
        android:src="@drawable/social_bg_add_circle_fail" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_white"
        android:gravity="center"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:text="@string/social_join_fail"
        android:textColor="@color/common_color_191919"
        android:textSize="@dimen/common_text_sp_22"
        android:textStyle="bold" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_bg_corner_20_bottom_2_color_white"
        android:gravity="center"
        android:paddingLeft="38dp"
        android:paddingTop="8dp"
        android:paddingRight="38dp"
        android:paddingBottom="77dp"
        android:text="@string/social_join_fail_assist"
        android:textColor="@color/common_color_929292"
        android:textSize="@dimen/common_text_sp_12" />

    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/rb_i_know"
        android:layout_width="145dp"
        android:layout_height="51dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="27dp"
        android:gravity="center"
        android:text="@string/common_i_know"
        android:textColor="@color/common_white"
        app:qmui_backgroundColor="@color/common_color_7171FF"
        app:qmui_radius="5dp" />

</LinearLayout>