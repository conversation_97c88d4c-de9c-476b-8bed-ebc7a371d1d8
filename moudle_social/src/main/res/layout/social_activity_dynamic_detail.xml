<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".DynamicDetailActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.social.viewmodel.DynamicDetailViewModel" />

        <variable
            name="item"
            type="com.kanzhun.marry.social.api.model.UserDynamicDetailModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.social.callback.DynamicDetailCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:layout_constraintTop_toTopOf="parent">

            <include
                android:id="@+id/title_bar"
                layout="@layout/social_dynamic_detail_title_bar"
                app:callback="@{callback}"
                app:item="@{item}"
                app:title="@{@string/social_dynamic_detail}" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_bar">

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/smart_refresh_layout"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toTopOf="@+id/cl_input"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srlEnableAutoLoadMore="true">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

                </com.scwang.smart.refresh.layout.SmartRefreshLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:onClick="@{v->callback.clickInput()}"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    tools:visibility="visible">

                    <View
                        android:id="@+id/view_line"
                        android:layout_width="match_parent"
                        android:layout_height="0.5dp"
                        android:background="@color/common_color_D3D3D3"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginBottom="20dp"
                        android:background="@color/common_color_F0F0F0"
                        android:gravity="center_vertical"
                        android:paddingLeft="20dp"
                        android:paddingTop="9dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="9dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/view_line"
                        app:qmui_radius="19dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:text="@{viewModel.hint}"
                            android:textColor="@color/common_color_B7B7B7"
                            android:textSize="@dimen/common_text_sp_14"
                            app:layout_constrainedWidth="true"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintHorizontal_bias="0"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toLeftOf="@+id/view_send_divider_outer"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="写评论..." />

                        <TextView
                            android:id="@+id/tv_send_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/social_comment_send"
                            android:textColor="@color/common_color_B7B7B7"
                            android:textSize="@dimen/common_text_sp_14"
                            app:layout_constraintRight_toRightOf="parent" />

                        <View
                            android:id="@+id/view_send_divider_outer"
                            android:layout_width="2dp"
                            android:layout_height="15dp"
                            android:layout_marginRight="14dp"
                            android:background="@color/common_color_B7B7B7"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintRight_toLeftOf="@+id/tv_send_title"
                            app:layout_constraintTop_toTopOf="parent" />
                    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--底部的弹框-->
        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/bottom_sheet_parent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_color_000000_60"
            android:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>