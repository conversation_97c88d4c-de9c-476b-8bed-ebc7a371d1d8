package com.kanzhun.marry.social.viewmodel;

import android.app.Application;
import android.view.View;

import androidx.databinding.ObservableInt;

import com.kanzhun.common.activity.fragment.BaseFragment;
import com.kanzhun.common.base.AllBaseFragment;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.marry.social.R;
import com.kanzhun.marry.social.fragment.SocialNotifyCommentFragment;

import java.util.ArrayList;
import java.util.List;

public class SocialNotifyViewModel extends FoundationViewModel {
    private static final String TAG = "SocialNotifyViewModel";

    public static final int S_REPORT = 0;
    public static final int S_DELETE = 1;

    private List<AllBaseFragment> fragments = new ArrayList<>();
    private List<String> fragmentTitles = new ArrayList<>();
    private ObservableInt[] unreadCountVisible = new ObservableInt[2];

    public SocialNotifyViewModel(Application application) {
        super(application);
        SocialNotifyCommentFragment commentFragment = new SocialNotifyCommentFragment();
//        SocialNotifyFriendFragment friendFragment = new SocialNotifyFriendFragment();
        fragments.add(commentFragment);
//        fragments.add(friendFragment);
        fragmentTitles.add(getApplication().getString(R.string.social_notify_comment));
        fragmentTitles.add(getApplication().getString(R.string.social_notify_friends));
        unreadCountVisible[0] = new ObservableInt(View.INVISIBLE);
        unreadCountVisible[1] = new ObservableInt(View.INVISIBLE);
    }

    public List<AllBaseFragment> getFragments() {
        return fragments;
    }

    public List<String> getFragmentTitles() {
        return fragmentTitles;
    }

    public void setUnreadCountVisible(int index, int unreadCount) {
        if (index >= 0 && index < unreadCountVisible.length) {
            if (unreadCountVisible[index] == null) {
                unreadCountVisible[index] = new ObservableInt(0);
            }
            unreadCountVisible[index].set(unreadCount > 0 ? View.VISIBLE : View.INVISIBLE);
        }
    }

    public ObservableInt[] getUnreadCountVisible() {
        return unreadCountVisible;
    }
}