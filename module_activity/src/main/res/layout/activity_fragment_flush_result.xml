<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="HardcodedText,SpUsage,ContentDescription,UseCompatTextViewDrawableXml,UnusedAttribute">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@mipmap/activity_film_flush_bg" />

    <LinearLayout
        android:id="@+id/ll_content_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#80191919"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_close"
                android:layout_width="wrap_content"
                android:layout_height="52dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:src="@drawable/common_ic_white_close"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <ImageView
                android:layout_width="78dp"
                android:layout_height="24dp"
                android:layout_gravity="center"
                android:scaleType="fitCenter"
                android:src="@drawable/activity_ff_ic_fujifilm" />

        </FrameLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_flush_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="20dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:shadowColor="#FFE2C0"
                        android:shadowRadius="30"
                        android:singleLine="true"
                        android:textColor="#FFE2C0"
                        android:textFontWeight="900"
                        android:textSize="@dimen/common_text_sp_28"
                        android:textStyle="bold"
                        tools:text="收到一张新胶片" />

                    <TextView
                        android:id="@+id/tv_flush_desc"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="12dp"
                        android:layout_marginRight="20dp"
                        android:drawablePadding="4dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="冲洗后的照片会通过站内信发送给你"
                        android:textColor="#99FFFFFF"
                        android:textFontWeight="400"
                        android:textSize="@dimen/common_text_sp_14" />

                </LinearLayout>

                <FrameLayout
                    android:id="@+id/fl_flush_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="18dp">

                    <include
                        layout="@layout/activity_flush_result_pic"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="gone" />

                    <include
                        layout="@layout/activity_flush_result_txt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="visible" />

                    <include
                        layout="@layout/activity_flush_result_failed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:visibility="gone"
                        tools:visibility="gone" />

                </FrameLayout>

                <LinearLayout
                    android:id="@+id/ll_flush"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="24dp"
                    android:layout_marginTop="58dp"
                    android:background="#F7F1E7"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingVertical="12dp">

                    <TextView
                        android:id="@+id/tv_flush_button_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableEnd="@drawable/activity_ff_2"
                        android:drawablePadding="8dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:singleLine="true"
                        android:textColor="#191919"
                        android:textSize="@dimen/common_text_sp_16"
                        tools:text="冲洗这张（消耗1次 余3次）" />

                </LinearLayout>

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tv_remain_flush_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="22dp"
                        android:drawableStart="@drawable/activity_ff_ic_shake"
                        android:drawablePadding="8dp"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:singleLine="true"
                        android:text="摇晃换一张"
                        android:textColor="#F7F1E7"
                        android:textFontWeight="400"
                        android:textSize="@dimen/common_text_sp_14" />

                </FrameLayout>

            </LinearLayout>

        </ScrollView>

    </LinearLayout>

</FrameLayout>