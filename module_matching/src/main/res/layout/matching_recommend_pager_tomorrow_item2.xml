<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/app_f1_top_text_height"
    android:orientation="vertical">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!--        <com.lihang.ShadowLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            app:layout_constraintStart_toStartOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="parent">-->

        <!--            <ImageView-->
        <!--                android:layout_width="match_parent"-->
        <!--                android:layout_height="500dp"-->
        <!--                android:background="@mipmap/matching_bg_tomorrow_card" />-->
        <!--        </com.lihang.ShadowLayout>-->


        <com.kanzhun.foundation.views.MinDistanceRecyclerView
            android:id="@+id/rvList"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:layout_width="0dp"
            android:layout_height="132dp"
            android:background="@drawable/matching_bg_color_00000000_to_d1ffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</LinearLayout>

