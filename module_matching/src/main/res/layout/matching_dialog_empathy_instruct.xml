<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="556dp"
    android:background="@drawable/common_bg_conor_12_color_white">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@mipmap/matching_bg_empathy_instruct"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:text="@string/matching_what_is_empathy"
        android:textColor="@color/common_black"
        android:textSize="@dimen/common_text_sp_24"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="28dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_positive"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginRight="28dp"
                android:text="心情共鸣是一种全新匹配模式，根据当前心情为您匹配能产生共鸣的用户，并开启限时2分钟的直播式文字聊天"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="共鸣"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_22" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="28dp"
                android:text="心境相似的人会匹配到一起"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="实时"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_22" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="28dp"
                android:text="直播式文字聊天，不用点击发送对方即可实时看到"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="匿名"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_22" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="28dp"
                android:text="限时2分钟匿名聊天，过程中互相公开头像即可解锁全部资料，成为好友开启正式聊天"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="16dp"
                android:fontFamily="sans-serif-medium"
                android:text="安全"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_22" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="28dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="28dp"
                android:text="聊天过程中若产生违规行为，会直接进行限制使用、封号等处罚"
                android:textColor="@color/common_color_333333"
                android:textSize="@dimen/common_text_sp_16" />

        </LinearLayout>
    </ScrollView>


    <com.kanzhun.common.views.RoundAlphaButton
        android:id="@+id/tv_positive"
        style="@style/common_blue_button_style"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:text="@string/common_me_know"
        android:textColor="@color/common_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>