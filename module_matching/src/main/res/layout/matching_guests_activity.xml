<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/idStateLayout"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>



</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>