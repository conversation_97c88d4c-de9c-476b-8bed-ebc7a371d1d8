<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".MatchingReviewListActivityActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.MatchingReviewListViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.MatchingReviewListCallback" />
    </data>

    <LinearLayout
        android:fitsSystemWindows="true"
        android:id="@+id/activityMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_FAFAFA"
        android:orientation="vertical">

        <include
            android:background="@android:color/white"
            layout="@layout/common_title_bar"
            app:callback="@{callback}"
            app:hasDivider="@{true}"
            app:title="@{@string/matching_review_title}"/>

        <LinearLayout
            android:layout_marginTop="11dp"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <TextView
                android:onClick="@{()->callback.clickTitlePage(0)}"
                android:paddingLeft="20dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingRight="20dp"
                android:background="@drawable/matching_bg_review_title_selected"
                android:textSize="16dp"
                android:text="@string/matching_review_no_like"
                android:textColor="@color/common_color_7171F6"
                android:id="@+id/tv_no_like_page"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="23dp"/>
            <TextView
                android:onClick="@{()->callback.clickTitlePage(1)}"
                android:paddingLeft="20dp"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:paddingRight="20dp"
                android:background="@drawable/matching_bg_review_title_normal"
                android:textSize="16dp"
                android:text="@string/matching_review_like"
                android:textColor="@color/common_color_191919"
                android:id="@+id/tv_like_page"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewpager"
            android:layout_marginTop="11dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    </LinearLayout>

</layout>