<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".EmpathyGuideActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.matching.viewmodel.EmpathyGuideViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.matching.callback.EmpathyGuideCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/matching_bg_empathy_guide"
        android:fitsSystemWindows="true">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_layout_only_left_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_anim"
            android:layout_width="338dp"
            android:layout_height="338dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:lottie_fileName="matching_empathy_guide.json"
            app:lottie_loop="true" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:fontFamily="sans-serif-medium"
            android:onClick="@{()->callback.clickEmpathyInstruct()}"
            android:text="@string/matching_what_is_empathy"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_40"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_anim" />

        <TextView
            android:id="@+id/tv_empathy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:onClick="@{()->callback.clickEmpathyInstruct()}"
            android:text="@string/matching_empathy"
            android:textColor="@color/common_color_7171F6"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toLeftOf="@+id/tv_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <ImageView
            android:id="@+id/iv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/matching_empathy_guide_notice"
            app:layout_constraintLeft_toRightOf="@+id/tv_empathy"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <TextView
            android:id="@+id/tv_empathy_assist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="是一种全新匹配模式，根据当"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toRightOf="@+id/iv"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <TextView
            android:id="@+id/tv_empathy_assist_one"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="前心情为您匹配能产生共鸣的用户，并开"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toLeftOf="@+id/tv_empathy"
            app:layout_constraintTop_toBottomOf="@+id/tv_empathy" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="启限时2分钟的直播式文字聊天"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toLeftOf="@+id/tv_empathy"
            app:layout_constraintTop_toBottomOf="@+id/tv_empathy_assist_one" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/common_blue_button_style"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="16dp"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@string/common_me_know"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>