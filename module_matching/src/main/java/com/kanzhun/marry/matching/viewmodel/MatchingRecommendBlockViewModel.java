package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.model.matching.MatchingBlockInfoModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.profile.GuideItemsResponse;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.bean.BlockAnimBean;
import com.kanzhun.utils.T;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class MatchingRecommendBlockViewModel extends FoundationViewModel {

    private ObservableField<String> descTitleObservable = new ObservableField();
    private ObservableBoolean fromLikeObservable = new ObservableBoolean();
    private ObservableBoolean verifyingObservable = new ObservableBoolean();
    private ObservableField<String> btnTitleObservable = new ObservableField();
    private ObservableField<Integer> certificateIconObservable = new ObservableField();
    private ObservableField<Integer> profileIconObservable = new ObservableField();
    private ObservableField<Integer> characterIconObservable = new ObservableField();
    private ObservableField<Integer> educationIconObservable = new ObservableField();
    // 阻断动画
    private MutableLiveData<BlockAnimBean> blockAnimLivaData = new MutableLiveData<>();
    // 新手任务
    private MutableLiveData<GuideItemsResponse> guideItemsLiveData = new MutableLiveData<>();
    // 还原icons的状态
    private MutableLiveData<Boolean> restoreIconLivaData = new MutableLiveData<>();

    private MatchingBlockInfoModel lastBlockInfoModel;// 上一次阻断状态
    public MatchingBlockInfoModel blockInfoModel;

    /**
     * @param blockInfoModel "certStatus":0, // 实名认证、学历认证进度 0:未认证 1:已实名人脸 2:学历认证审核中 3:学历认证审核被驳回 4:学历认证完成 5:学历认证完成后审核中 6:学历认证完成后被驳回
     *                       "profileStatus": 0 , // 个人页信息状态 1-未填写，2-新增被驳回，3-新增审核中，4-全部通过，5-更新被驳回，6-更新审核中
     *                       "surveyStatus": 0 // 问卷测试状态 0 未完成 1 已完成
     */
    public void initStatus(MatchingBlockInfoModel blockInfoModel) {
        this.blockInfoModel = blockInfoModel;
        // 学历审核中、个人资料审核中、恋爱性格探索已完成时，无按钮、展示审核中状态
        verifyingObservable.set(blockInfoModel.showVerifying());

        refreshText(blockInfoModel);

        if (blockInfoModel.isCertStatusVerifyingOrComplete()) {// 已完成实名认证和学历认证
            certificateIconObservable.set(0);
        } else {
            certificateIconObservable.set(R.mipmap.matching_ic_certificate);
        }

        if (blockInfoModel.profileStatus == 1 || blockInfoModel.profileStatus == 2) {// 个人资料未提交、被驳回
            // 未认证时，个人资料icon处于置灰状态
            profileIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() ? R.mipmap.matching_ic_profile_disable : R.mipmap.matching_ic_profile);
        } else {
            profileIconObservable.set(0);
        }

        if (blockInfoModel.isSurveyComplete()) {// 恋爱性格探索已完成
            characterIconObservable.set(0);
        } else {
            // 未认证或个人资料未完善时，恋爱性格icon处于置灰状态
            characterIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete()
                    ? R.mipmap.matching_ic_character_disable : R.mipmap.matching_ic_character);
        }

        if (blockInfoModel.isEducationStatusVerifyingOrComplete()) {// 恋爱性格探索已完成
            educationIconObservable.set(0);
        } else {
            // 未认证或个人资料未完善或恋爱性格时，学历认证icon处于置灰状态
            educationIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete() || !blockInfoModel.isSurveyComplete()
                    ? R.mipmap.matching_ic_education_disable : R.mipmap.matching_ic_education);
        }

        lastBlockInfoModel = blockInfoModel;
    }

    public void refreshStatus(@NonNull MatchingBlockInfoModel blockInfoModel) {
        this.blockInfoModel = blockInfoModel;
        if (lastBlockInfoModel == null) {
            initStatus(blockInfoModel);
        } else {
            if (blockInfoModel.certStatus == lastBlockInfoModel.certStatus
                    && blockInfoModel.profileStatus == lastBlockInfoModel.profileStatus
                    && blockInfoModel.surveyStatus == lastBlockInfoModel.surveyStatus) return;

            refreshText(blockInfoModel);

            checkReShowIcon(blockInfoModel);

            if (blockInfoModel.profileStatus == 1 || blockInfoModel.profileStatus == 2) {// 个人资料未提交、新增被驳回
                // 未认证时，个人资料icon处于置灰状态
                profileIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() ? R.mipmap.matching_ic_profile_disable : R.mipmap.matching_ic_profile);
            }

            if (!blockInfoModel.isSurveyComplete()) {
                // 未认证或个人资料未完善时，恋爱性格icon处于置灰状态
                characterIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete()
                        ? R.mipmap.matching_ic_character_disable : R.mipmap.matching_ic_character);
            }

            if (!blockInfoModel.isEducationStatusVerifyingOrComplete()) {
                // 未认证或个人资料未完善或恋爱性格时，学历认证icon处于置灰状态
                educationIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete() || !blockInfoModel.isSurveyComplete()
                        ? R.mipmap.matching_ic_education_disable : R.mipmap.matching_ic_education);
            }

            // 判断是否需要动画
            boolean certOver = blockInfoModel.isCertStatusVerifyingOrComplete() && !lastBlockInfoModel.isCertStatusVerifyingOrComplete();
            boolean profileOver = blockInfoModel.isProfileVerifyingOrComplete() && !lastBlockInfoModel.isProfileVerifyingOrComplete();
            boolean surveyOver = blockInfoModel.isSurveyComplete() && !lastBlockInfoModel.isSurveyComplete();
            boolean educationOver = blockInfoModel.isEducationStatusVerifyingOrComplete() && !lastBlockInfoModel.isEducationStatusVerifyingOrComplete();

            if (certOver || profileOver || surveyOver || educationOver) {
                blockAnimLivaData.postValue(new BlockAnimBean(certOver, profileOver, surveyOver, educationOver, blockInfoModel.showVerifying()));
            } else {
                // 学历审核中、个人资料审核中、恋爱性格探索已完成时，无按钮、展示审核中状态
                verifyingObservable.set(blockInfoModel.showVerifying());
                restoreIconLivaData.postValue(true);
            }

            lastBlockInfoModel = blockInfoModel;
        }
    }

    private void refreshText(MatchingBlockInfoModel blockInfoModel) {
        if (blockInfoModel.isCertStatusVerifyingOrComplete()) {// 已完成实名认证
            if (blockInfoModel.isProfileVerifyingOrComplete()) {// 个人资料审核通过
                if (blockInfoModel.isSurveyComplete()) {// 恋爱性格探索已完成
                    if (blockInfoModel.certStatus == 1 || blockInfoModel.certStatus == 3) {// 学历认证未提交、被驳回
                        descTitleObservable.set(getResources().getString(R.string.matching_recommend_educate));
                        btnTitleObservable.set(getResources().getString(R.string.matching_go_to_certificate));
                    } else {
                        descTitleObservable.set("");
                        btnTitleObservable.set("");
                    }
                } else {
                    descTitleObservable.set(getResources().getString(R.string.matching_recommend_prob_character));
                    btnTitleObservable.set(getResources().getString(R.string.matching_go_to_prob));
                }
            } else if (blockInfoModel.profileStatus == 1 || blockInfoModel.profileStatus == 2) {// 个人资料未提交、新增被驳回
                descTitleObservable.set(getResources().getString(R.string.matching_recommend_edit_profile));
                btnTitleObservable.set(getResources().getString(R.string.matching_go_to_fill));
            }
        } else {
            if (blockInfoModel.certStatus == 0) {
                descTitleObservable.set(getResources().getString(R.string.matching_recommend_certificate));
                btnTitleObservable.set(getResources().getString(R.string.matching_go_to_certificate));
            } else {
                descTitleObservable.set("");
                btnTitleObservable.set("");
            }
        }
    }

    /**
     * 检查是否上一次icon没显示，这一次需要显示
     * 场景：审核中被驳回
     */
    private void checkReShowIcon(MatchingBlockInfoModel blockInfoModel) {
        if (lastBlockInfoModel.isCertStatusVerifyingOrComplete() && !blockInfoModel.isCertStatusVerifyingOrComplete()) {
            certificateIconObservable.set(R.mipmap.matching_ic_certificate);// 显示认证图标
        }
        if (lastBlockInfoModel.isProfileVerifyingOrComplete() && !blockInfoModel.isProfileVerifyingOrComplete()) {
            // 未认证时，个人资料icon处于置灰状态
            profileIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() ? R.mipmap.matching_ic_profile_disable : R.mipmap.matching_ic_profile);
        }
        if (lastBlockInfoModel.isSurveyComplete() && !blockInfoModel.isSurveyComplete()) {
            // 未认证或个人资料未完善时，恋爱性格icon处于置灰状态
            characterIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete()
                    ? R.mipmap.matching_ic_character_disable : R.mipmap.matching_ic_character);
        }
        if (lastBlockInfoModel.isEducationStatusVerifyingOrComplete() && !blockInfoModel.isEducationStatusVerifyingOrComplete()) {
            // 未认证或个人资料未完善或恋爱性格时，学历认证icon处于置灰状态
            educationIconObservable.set(!blockInfoModel.isCertStatusVerifyingOrComplete() || !blockInfoModel.isProfileVerifyingOrComplete() || !blockInfoModel.isSurveyComplete()
                    ? R.mipmap.matching_ic_education_disable : R.mipmap.matching_ic_education);
        }
    }

    /**
     * 判断阻断信息
     */
    public void requestBlockInfo() {
        Observable<BaseResponse<MatchingBlockInfoModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).requestBlockInfo();
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<MatchingBlockInfoModel>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess(MatchingBlockInfoModel data) {
                if (data == null) return;
                refreshStatus(data);
            }

            @Override
            public void showFailed(ErrorReason reason) {
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    /**
     * 获取新手流程信息
     */
    public void getGuideItems() {
        Observable<BaseResponse<GuideItemsResponse>> responseObservable = RetrofitManager.getInstance().createApi(FoundationApi.class).getGuideItems();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<GuideItemsResponse>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess(GuideItemsResponse data) {
                ProfileHelper.getInstance().setGuideItemsResponse(data);
                guideItemsLiveData.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                ProfileHelper.getInstance().setExtInfo(null);
                ProfileHelper.getInstance().clearUploadCache();
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public ObservableField<String> getDescTitleObservable() {
        return descTitleObservable;
    }

    public void setDescTitleObservable(ObservableField<String> descTitleObservable) {
        this.descTitleObservable = descTitleObservable;
    }

    public ObservableBoolean getFromLikeObservable() {
        return fromLikeObservable;
    }

    public void setFromLikeObservable(ObservableBoolean fromLikeObservable) {
        this.fromLikeObservable = fromLikeObservable;
    }

    public ObservableBoolean getVerifyingObservable() {
        return verifyingObservable;
    }

    public void setVerifyingObservable(ObservableBoolean verifyingObservable) {
        this.verifyingObservable = verifyingObservable;
    }

    public ObservableField<String> getBtnTitleObservable() {
        return btnTitleObservable;
    }

    public void setBtnTitleObservable(ObservableField<String> btnTitleObservable) {
        this.btnTitleObservable = btnTitleObservable;
    }

    public ObservableField<Integer> getCertificateIconObservable() {
        return certificateIconObservable;
    }

    public void setCertificateIconObservable(ObservableField<Integer> certificateIconObservable) {
        this.certificateIconObservable = certificateIconObservable;
    }

    public ObservableField<Integer> getProfileIconObservable() {
        return profileIconObservable;
    }

    public void setProfileIconObservable(ObservableField<Integer> profileIconObservable) {
        this.profileIconObservable = profileIconObservable;
    }

    public ObservableField<Integer> getCharacterIconObservable() {
        return characterIconObservable;
    }

    public void setCharacterIconObservable(ObservableField<Integer> characterIconObservable) {
        this.characterIconObservable = characterIconObservable;
    }

    public ObservableField<Integer> getEducationIconObservable() {
        return educationIconObservable;
    }

    public void setEducationIconObservable(ObservableField<Integer> educationIconObservable) {
        this.educationIconObservable = educationIconObservable;
    }

    public MutableLiveData<BlockAnimBean> getBlockAnimLivaData() {
        return blockAnimLivaData;
    }

    public MutableLiveData<GuideItemsResponse> getGuideItemsLiveData() {
        return guideItemsLiveData;
    }

    public MutableLiveData<Boolean> getRestoreIconLivaData() {
        return restoreIconLivaData;
    }

    public MatchingRecommendBlockViewModel(@NonNull Application application) {
        super(application);
    }

}