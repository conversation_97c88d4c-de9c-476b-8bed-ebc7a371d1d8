package com.kanzhun.marry.matching.fragment;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.decoration.GridDividerItemDecoration;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.activity.MatchingReviewHistoryActivity;
import com.kanzhun.marry.matching.api.model.MySkipBean;
import com.kanzhun.marry.matching.api.model.MySkipItemBean;
import com.kanzhun.marry.matching.api.model.MySkipTagBean;

import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.kanzhun.marry.matching.databinding.FragmentMatchingReviewNoLikeBinding;
import com.kanzhun.marry.matching.databinding.MatchingItemLayoutReviewSkipBinding;
import com.kanzhun.marry.matching.databinding.MatchingItemReviewSkipTagBinding;
import com.kanzhun.marry.matching.viewmodel.MatchingReviewListViewModel;
import com.kanzhun.marry.matching.viewmodel.MatchingReviewNoLikeViewModel;
import com.kanzhun.marry.matching.callback.MatchingReviewNoLikeCallback;
import com.kanzhun.utils.base.LList;

public class MatchingReviewNoLikeFragment extends FoundationVMShareFragment<FragmentMatchingReviewNoLikeBinding, MatchingReviewNoLikeViewModel, MatchingReviewListViewModel> implements MatchingReviewNoLikeCallback {

    private BaseBinderAdapter adapter;

    @Override
    protected void initFragment() {
        super.initFragment();
        LinearLayoutManager layoutManager = new LinearLayoutManager(activity, RecyclerView.VERTICAL, false);
        getDataBinding().recycler.setLayoutManager(layoutManager);
        adapter = new BaseBinderAdapter();
        adapter.addItemBinder(MySkipTagBean.class, new BaseDataBindingItemBinder<MySkipTagBean, MatchingItemReviewSkipTagBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.matching_item_review_skip_tag;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MatchingItemReviewSkipTagBinding> holder, MatchingItemReviewSkipTagBinding binding, MySkipTagBean item) {
                binding.setItem(item);
                if (!LList.isEmpty(item.skipList)) {
                    StaggeredGridLayoutManager layoutManager = new StaggeredGridLayoutManager(2, StaggeredGridLayoutManager.VERTICAL);
                    binding.itemRecycler.setLayoutManager(layoutManager);
                    if (binding.itemRecycler.getItemDecorationCount() == 0) {
                        binding.itemRecycler.addItemDecoration(new GridDividerItemDecoration(QMUIDisplayHelper.dp2px(activity, 9)));
                    }
                    BaseBinderAdapter itemAdapter = getItemAdapter();
                    binding.itemRecycler.setAdapter(itemAdapter);
                    itemAdapter.setList(item.skipList);
                }

            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MatchingItemReviewSkipTagBinding> holder, @NonNull View view, MySkipTagBean data, int position) {

            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {

            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MatchingItemReviewSkipTagBinding> holder, @NonNull View view, MySkipTagBean data, int position) {

            }
        });

        getActivityViewModel().getMySkipMutableLiveData().observe(this, new Observer<MySkipBean>() {
            @Override
            public void onChanged(MySkipBean mySkipBean) {
                adapter.setList(mySkipBean.result);
            }
        });
        getDataBinding().recycler.setAdapter(adapter);
    }

    private BaseBinderAdapter getItemAdapter() {
        BaseBinderAdapter itemAdapter = new BaseBinderAdapter();
        itemAdapter.addItemBinder(MySkipItemBean.class, new BaseDataBindingItemBinder<MySkipItemBean, MatchingItemLayoutReviewSkipBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.matching_item_layout_review_skip;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MatchingItemLayoutReviewSkipBinding> holder, MatchingItemLayoutReviewSkipBinding binding, MySkipItemBean item) {
                binding.setItem(item);
            }

            @Override
            public void onClick(@NonNull BinderDataBindingHolder<MatchingItemLayoutReviewSkipBinding> holder, @NonNull View view, MySkipItemBean data, int position) {
                AppUtil.startActivity(activity, MatchingReviewHistoryActivity.createIntent(activity, data.userId, false, getActivityViewModel().getLikeList(), getActivityViewModel().getSkipList(), 11));
            }

            @Override
            protected void bindChildClickViewIds(ViewGroup parent, int viewType) {

            }

            @Override
            public void onChildClick(@NonNull BinderDataBindingHolder<MatchingItemLayoutReviewSkipBinding> holder, @NonNull View view, MySkipItemBean data, int position) {

            }
        });
        return itemAdapter;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.fragment_matching_review_no_like;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }

}