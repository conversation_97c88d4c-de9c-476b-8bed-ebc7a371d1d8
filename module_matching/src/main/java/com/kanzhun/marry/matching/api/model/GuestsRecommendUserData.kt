package com.kanzhun.marry.matching.api.model

import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.foundation.model.profile.UserTabModel
import java.io.Serializable

data class GuestsRecommendUserData(
    val isUpdate: Int = 0,
    val notBlockCount: Int = 0,//未阻断的数量
    val userList: List<RecommendUser>? = null,
    val previewUser: TomorrowRecommendData? = null,
    val blockInfo: UserGuideBlockInfoBean? = null, //阻断信息
    val unlockUserList: List<RecommendUser>? = null, //阻断用户
    val topContent:String? = "",
    val bottomContent:String? = "",
    val activityInfo: UserTabModel.ActivityBean? = null
) : Serializable {

    fun hasUpdate(): Boolean {
        return isUpdate == 1
    }

    fun hasBlock(): Boolean {
        return blockInfo != null
    }
}

