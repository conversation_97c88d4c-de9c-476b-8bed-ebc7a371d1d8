package com.kanzhun.marry.matching.activity

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.MutableLiveData
import com.techwolf.lib.tlog.TLog
import com.kanzhun.common.bindadapter.CommonBindingAdapters
import com.kanzhun.common.dialog.showTwoButtonDialog
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.common.kotlin.ext.toResourceString
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.common.views.RangeBar
import com.kanzhun.common.views.RangeBar.OnRangeSliderListener
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.api.model.AreaModel
import com.kanzhun.foundation.kotlin.common.activity.SelectHometownActivity
import com.kanzhun.foundation.kotlin.ktx.toEducationNameForFilter
import com.kanzhun.foundation.kotlin.ktx.toSalaryName1
import com.kanzhun.foundation.kotlin.ui.dialog.showSelectEducationDialog1
import com.kanzhun.foundation.kotlin.ui.dialog.showSelectSalaryDialog1
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.api.MatchingApi
import com.kanzhun.marry.matching.api.model.FavorFilterModel
import com.kanzhun.marry.matching.databinding.MatchingActivityRequirementBinding
import com.kanzhun.utils.T

class MatchingRequirementActivity : BaseBindingActivity<MatchingActivityRequirementBinding, MatchingRequirementViewModel>() {
    override fun preInit(intent: Intent) {

    }

    override fun initView() {
        mBinding.run {
            titleBar.asBackButton()
            //学历
            tvEducation.setOnClickListener {
                selectEducationDialog()
            }
            //家乡
            tvHome.setOnClickListener {
                selectHomeDialog()
            }
            //年收入
            tvRevenue.setOnClickListener {
                selectSalaryDialog()
            }
            btnConfirm.setOnClickListener {
                submit()
            }
        }


    }

    private fun submit(){
        if(mViewModel.hasChange()){
            showProgressDialog("",true)
            mViewModel.submitFavorFilter()
        }else{
            finish()
        }

    }

    override fun initData() {
        observeData()
        mViewModel.getFavorFilter()
    }

    private fun observeData() {
        mViewModel.resultLiveData.observe(this) {
            it?.run {
                initViewByData(this)
            }
        }
        mViewModel.updateResult.observe(this){
            dismissProgressDialog()
            finish()
        }
    }

    private fun initViewByData(model: FavorFilterModel) {
        //年龄
        initAgeRangeBar(model)
        //身高
        initHeightRangeBar(model)
        //学历
        setEducation()
        //家乡
        setHome()
        //年收入
        setSalary()
    }


    //年龄选择
    private fun initAgeRangeBar(filterModel: FavorFilterModel) {
        if (filterModel.rangeAgeMax > filterModel.rangeAgeMin) {
            setCurrentAgeRange()
            TLog.info("age", "filterModel.rangeAgeMax:${filterModel.rangeAgeMax},filterModel.rangeAgeMin:${filterModel.rangeAgeMin}")
            TLog.info("age", "filterModel.ageMin:${filterModel.ageMin},filterModel.ageMax:${filterModel.ageMax}")
            mBinding.selectAge.run {
                tvSelectTitle.setText(R.string.common_age_name)
                ivLeft.setImageResource(R.drawable.matching_ic_select_age_left)
                ivRight.setImageResource(R.drawable.matching_ic_select_age_right)
                CommonBindingAdapters.setRangeBarEnable(rb, true)
                rb.setMinRange(1)
                rb.setMinIndex(filterModel.rangeAgeMin ?: 20)
                rb.setMaxIndex(filterModel.rangeAgeMax ?: 50)
                rb.setStartingMinMax(filterModel.ageMin ?: 20, filterModel.ageMax ?: 35)
                rb.setOnRangeSliderListener(object : OnRangeSliderListener {
                    override fun onMaxChanged(rangeBar: RangeBar, maxIndex: Int) {
                        mViewModel.setAgeMax(maxIndex)
                        setCurrentAgeRange()
                    }

                    override fun onMinChanged(rangeBar: RangeBar, minIndex: Int) {
                        mViewModel.setAgeMin(minIndex)
                        setCurrentAgeRange()
                    }
                })

            }

        }
    }

    private fun setCurrentAgeRange() {
        mBinding.selectAge.tvSelectContent.text = mViewModel.getAgeShowContent()
    }


    //身高选择
    private fun initHeightRangeBar(filterModel: FavorFilterModel) {
        if (filterModel.rangeHeightMax > filterModel.rangeHeightMin) {
            setCurrentHeightRange()
            mBinding.selectStature.run {
                tvSelectTitle.setText(R.string.common_stature)
                ivLeft.setImageResource(R.drawable.matching_ic_select_stature_enable_left)
                ivRight.setImageResource(R.drawable.matching_ic_select_stature_enable_right)
                CommonBindingAdapters.setRangeBarEnable(rb, true)
                rb.setMinRange(1)
                rb.setMinIndex(0)
                var maxIndex = (filterModel.rangeHeightMax - filterModel.rangeHeightMin) / 5
                if (maxIndex <= 0) {
                    maxIndex = 1
                }
                rb.setMaxIndex(maxIndex)
                val sMin = (filterModel.heightMin - filterModel.rangeHeightMin) / 5
                val sMax = (filterModel.heightMax - filterModel.rangeHeightMin) / 5
                rb.setStartingMinMax(sMin, sMax)
                TLog.info("height", "filterModel.rangeHeightMax:${filterModel.rangeHeightMax},filterModel.rangeHeightMin:${filterModel.rangeHeightMin}")
                TLog.info("height", "sMin:${sMin},sMax:${sMax}")
                TLog.info("height", "filterModel.heightMin:${filterModel.heightMin},filterModel.heightMax:${filterModel.heightMax}")
                TLog.info("height", "maxIndex:${maxIndex}")
                rb.setOnRangeSliderListener(object : OnRangeSliderListener {
                    override fun onMaxChanged(rangeBar: RangeBar, maxIndex: Int) {
                        val maxStature = maxIndex * 5 + filterModel.rangeHeightMin
                        mViewModel.setMaxStature(maxStature)
                        setCurrentHeightRange()
                    }

                    override fun onMinChanged(rangeBar: RangeBar, minIndex: Int) {
                        val minStature = minIndex * 5 + filterModel.rangeHeightMin
                        mViewModel.setMinStature(minStature)
                        setCurrentHeightRange()
                    }
                })
                rb.setOnRangeValueCallback { index ->
                    (index * 5 + filterModel.rangeHeightMin).toString()
                }
            }
        }
    }

    private fun setCurrentHeightRange() {
        mBinding.selectStature.tvSelectContent.text = mViewModel.getStatureContent()
    }

    private fun selectEducationDialog() {
        showSelectEducationDialog1(mViewModel.currentFilterModel.degree.toString()) { _, code ->
            mViewModel.currentFilterModel.degree = code.toInt()
            setEducation()
        }
    }

    private fun setEducation(){
        mBinding.tvEducation.text = mViewModel.currentFilterModel.degree.toEducationNameForFilter()
    }

    private fun selectSalaryDialog(){
        showSelectSalaryDialog1(mViewModel.currentFilterModel.annualIncome.toString()){name, code ->
            mViewModel.currentFilterModel.annualIncome = code.toInt()
            setSalary()
        }
    }

    private fun setSalary(){
        mBinding.tvRevenue.text = mViewModel.currentFilterModel.annualIncome.toSalaryName1()
    }

    private fun selectHomeDialog(){
            SelectHometownActivity.intent(this, mViewModel.currentFilterModel.localHometownList)
    }

    private fun setHome(){
        mBinding.tvHome.text = mViewModel.getHometownContent()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if(requestCode == RequestCodeConstants.REQUEST_CODE_SELECT_HOMETOWN && resultCode == Activity.RESULT_OK){
            val hometownList = SelectHometownActivity.getSelectedHometownFromResult(requestCode,resultCode,data)
            mViewModel.currentFilterModel.localHometownList = hometownList
            setHome()
        }
    }

    override fun onBackPressed() {
        if(mViewModel.hasChange()){
            showExitDialog()
        }else{
            super.onBackPressed()
        }
    }

    private fun showExitDialog(){
        showTwoButtonDialog(title = R.string.common_save_changed_title.toResourceString(),
            positiveText = R.string.common_save_p.toResourceString(),
            negativeText = R.string.common_save_n.toResourceString(),
            positiveButtonClick = {
                submit()
            },
            negativeButtonClick = {
                finish()
            })
    }

    override fun onRetry() {
        mViewModel.getFavorFilter()
    }

    override fun getStateLayout() = mBinding.stateLayout
}


class MatchingRequirementViewModel : BaseViewModel() {

    var rangeAgeMax = 0 //默认年龄最大值

    var rangeAgeMin = 0 //默认年龄最小值

    var rangeHeightMin = 0 //默认身高最小值

    var rangeHeightMax = 0 //默认身高最大值

    //实时修改的数据
    var currentFilterModel: FavorFilterModel = FavorFilterModel()

    //初始化的数据
    var oldFilterModel: FavorFilterModel = FavorFilterModel()

    var resultLiveData: MutableLiveData<FavorFilterModel?> = MutableLiveData()
    //提交结果
    var updateResult:MutableLiveData<Boolean> = MutableLiveData()


    var hometownList:ArrayList<AreaModel>? = null


    fun getFavorFilter() {
        val observable = RetrofitManager.getInstance().createApi(MatchingApi::class.java).favorFilter
        HttpExecutor.execute<FavorFilterModel>(observable, object : BaseRequestCallback<FavorFilterModel?>() {

            override fun onSuccess(data: FavorFilterModel?) {
                if (data != null) {
                    showContent()
                    rangeAgeMin = data.rangeAgeMin
                    rangeAgeMax = data.rangeAgeMax
                    rangeHeightMin = data.rangeHeightMin
                    rangeHeightMax = data.rangeHeightMax
                    data.transformLocalHometownList()
                    currentFilterModel.copyValue(data)
                    oldFilterModel.copyValue(data)
                    resultLiveData.postValue(currentFilterModel)
                } else {
                    showError()
                }
            }

            override fun dealFail(reason: ErrorReason) {
                showError()
            }
        })
    }

    fun submitFavorFilter(){
        val params: MutableMap<String, Any> = HashMap()
        params["ageMin"] = if (currentFilterModel.ageMin <= rangeAgeMin) 0 else currentFilterModel.ageMin
        params["ageMax"] = if (currentFilterModel.ageMax >= rangeAgeMax) 0 else currentFilterModel.ageMax
        params["heightMin"] = if (currentFilterModel.heightMin <= rangeHeightMin) 0 else currentFilterModel.heightMin
        params["heightMax"] = if (currentFilterModel.heightMax >= rangeHeightMax) 0 else currentFilterModel.heightMax
        if (currentFilterModel.degree > 0) {
            params["degree"] = currentFilterModel.degree
        }
        if (!currentFilterModel.localHometownList.isNullOrEmpty()) {
            if(currentFilterModel.localHometownList.size ==1 && currentFilterModel.localHometownList[0].code == "0"){
                params["hometownList"] = ""
            }else{
                params["hometownList"] = currentFilterModel.localHometownList.map { it.code }.joinToString(",")
            }
        }

        if (currentFilterModel.annualIncome > 0) {
            params["annualIncome"] = currentFilterModel.annualIncome
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_MATCH_UPDATE_FAVOR_FILTER, params, object : SimpleRequestCallback(true) {
            override fun onSuccess() {
                T.ss("保存成功，明天开始将为你优化推荐")
                updateResult.postValue(true)
            }

            override fun dealFail(reason: ErrorReason) {
                updateResult.postValue(false)

            }
        })
    }

    //年龄相关
    fun setAgeMax(maxIndex: Int) {
        currentFilterModel?.ageMax = maxIndex
    }

    fun setAgeMin(minIndex: Int) {
        currentFilterModel?.ageMin = minIndex
    }

    fun getAgeShowContent(): String {
        val minAge = currentFilterModel?.ageMin ?: 0
        val maxAge = currentFilterModel?.ageMax ?: 0
        val age = StringBuilder()
        if (maxAge >= rangeAgeMax) {
            age.append(minAge)
            age.append(R.string.matching_up_age.toResourceString())
        } else {
            age.append(minAge)
            age.append("-")
            age.append(maxAge)
            age.append(R.string.common_age.toResourceString())
        }
        return age.toString()
    }

    //身高相关
    fun setMinStature(minStature: Int) {
        currentFilterModel?.heightMin = minStature
    }

    fun setMaxStature(maxStature: Int) {
        currentFilterModel?.heightMax = maxStature
    }

    fun getStatureContent(): String {
        val minStature = currentFilterModel?.heightMin ?: 0
        val maxStature = currentFilterModel?.heightMax ?: 0
        val stature = java.lang.StringBuilder()
        if (minStature <= rangeHeightMin) {
            if (maxStature >= rangeHeightMax) {
                stature.append("不限身高")
            } else {
                stature.append(maxStature)
                stature.append("cm以下")
            }
        } else {
            if (maxStature >= rangeHeightMax) {
                stature.append(minStature)
                stature.append("cm以上")
            } else {
                stature.append(minStature)
                stature.append("-")
                stature.append(maxStature)
                stature.append("cm")
            }
        }
        return stature.toString()
    }

    fun getHometownContent():String{
        val hometown = currentFilterModel.localHometownList
        return if (hometown.isEmpty()) {
            "不限"
        } else {
            hometown.map { it.name }.joinToString("、")
        }
    }

    fun hasChange():Boolean{
        return currentFilterModel != oldFilterModel
    }

}