package com.kanzhun.marry.matching.fragment;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;

import androidx.lifecycle.Observer;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.base.HostConfig;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.fragment.FoundationVMFragment;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.model.WebViewBean;
import com.kanzhun.foundation.router.AppPageRouter;
import com.kanzhun.foundation.router.MatchingPageRouter;
import com.kanzhun.foundation.views.ViewPager2Helper;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.activity.MatchRequirementActivityOld;
import com.kanzhun.foundation.model.matching.MatchingUserInfoModel;
import com.kanzhun.marry.matching.callback.MatchRecommendCallback;
import com.kanzhun.marry.matching.databinding.MatchingFragmentMatchRecommendBinding;
import com.kanzhun.marry.matching.databinding.MatchingItemMatchRecommendSelectTabBinding;
import com.kanzhun.marry.matching.viewmodel.MatchRecommendViewModel;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;

import net.lucode.hackware.magicindicator.buildins.commonnavigator.CommonNavigator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.CommonNavigatorAdapter;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerIndicator;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.abs.IPagerTitleView;
import net.lucode.hackware.magicindicator.buildins.commonnavigator.titles.CommonPagerTitleView;

public class MatchRecommendFragment extends FoundationVMFragment<MatchingFragmentMatchRecommendBinding, MatchRecommendViewModel> implements MatchRecommendCallback {

    int matchPreferencePage;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.matching_fragment_match_recommend;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if (matchPreferencePage == MatchingUserInfoModel.UN_SET_SHOW_MATCH_GUIDE) {
            //未设置过匹配模式
            WebViewBean webViewBean = new WebViewBean();
            webViewBean.setUrl(HostConfig.transformUrlHost(URLConfig.URL_H5_MATCH_PREFERENCE) + "?noHead=1");
            webViewBean.setStyle(WebViewBean.STYLE_HAS_NO_TITLE_AND_TRANSLUCENT);
            Bundle bundle = new Bundle();
            bundle.putSerializable(BundleConstants.BUNDLE_WEB_VIEW_BEAN, webViewBean);
            AppUtil.startUri(activity, AppPageRouter.WEB_VIEW_ACTIVITY, bundle);
            return;
        }
        AppUtil.startActivity(activity, new Intent(activity, MatchRequirementActivityOld.class));
    }

    @Override
    public void clickReview() {
        MatchingPageRouter.jumpToReviewListActivity(activity);
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        boolean matchHide = TextUtils.equals(SettingBuilder.getInstance().getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_INVISIBLE), "1");
        if (matchHide) {
            setMatchHide(true);
        }
        ServiceManager.getInstance().getSettingService().getMatchVisibleLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (getViewModel().isMatchHide() != aBoolean) {
                    setMatchHide(aBoolean);
                }
            }
        });
        ServiceManager.getInstance().getProfileService().getUserLiveData().observe(this, new Observer<User>() {
            @Override
            public void onChanged(User user) {
                if (user != null) {
                    boolean locked = user.getProfileLocked() == Account.PROFILE_LOCKED;
                    // 锁定状态不显示回看按钮
                    getDataBinding().ivReview.setVisibility(locked ? View.GONE : View.VISIBLE);
                }
            }
        });
        RxBus.getInstance().post(0, Constants.POST_TAG_FIRST_TAB_CHECKED_INDEX);
        getDataBinding().clMatchRecommend.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        getDataBinding().clHide.setPadding(0, StatusBarUtil.getStatusBarHeight(activity), 0, 0);
        ViewPagerFragmentAdapter adapter = new ViewPagerFragmentAdapter(this);
        adapter.setData(getViewModel().getFragments());
        getDataBinding().viewPagerMatchRecommend.setAdapter(adapter);
        getDataBinding().viewPagerMatchRecommend.setOffscreenPageLimit(adapter.getItemCount());
        getDataBinding().viewPagerMatchRecommend.setUserInputEnabled(false);
        initMagicIndicator();
        RxBus.getInstance().subscribe(this, Constants.POST_TAG_MATCH_PREFERENCE_PAGE, new RxBus.Callback<Integer>() {
            @Override
            public void onEvent(Integer integer) {
                if (integer != null) {
                    matchPreferencePage = integer;
                }
            }
        });
        RxBus.getInstance().subscribe(this, Constants.POST_TAG_MATCH_TAB_SELECT_INDEX, new RxBus.Callback<Integer>() {

            @Override
            public void onEvent(Integer index) {
                if (index != null && index >= 0 && index <= 1) {
                    getDataBinding().viewPagerMatchRecommend.setCurrentItem(index);
                }
            }
        });
        RxBus.getInstance().subscribe(this, Constants.POST_TAG_MATCH_PREFERENCE_REQUEST_SUCCESS, new RxBus.Callback<String>() {
            @Override
            public void onEvent(String s) {
                //恋爱要素设置成功更改matchPreferencePage状态
                if (matchPreferencePage == MatchingUserInfoModel.UN_SET_SHOW_MATCH_GUIDE) {
                    matchPreferencePage = 0;
                }
            }
        });
    }

    /**
     * 是否隐藏匹配个人页
     */
    private void setMatchHide(Boolean matchHide) {
        getViewModel().setMatchHide(matchHide);
        if (matchHide) {
            getDataBinding().clHide.setVisibility(View.VISIBLE);
            getDataBinding().clMatchRecommend.setVisibility(View.GONE);
        } else {
            getDataBinding().clHide.setVisibility(View.GONE);
            getDataBinding().clMatchRecommend.setVisibility(View.VISIBLE);
        }
    }

    private void initMagicIndicator() {
        CommonNavigator commonNavigator = new CommonNavigator(activity);
        int padding = QMUIDisplayHelper.dp2px(activity, 3);
        commonNavigator.setRightPadding(padding);
        commonNavigator.setLeftPadding(padding);
        commonNavigator.setAdapter(new CommonNavigatorAdapter() {
            @Override
            public int getCount() {
                return getViewModel().getTabTitle().size();
            }

            @Override
            public IPagerTitleView getTitleView(Context context, final int index) {
                CommonPagerTitleView commonPagerTitleView = new CommonPagerTitleView(context);
                MatchingItemMatchRecommendSelectTabBinding tabBinding = MatchingItemMatchRecommendSelectTabBinding.inflate(LayoutInflater.from(context));
                tabBinding.tvTitle.setText(getViewModel().getTabTitle().get(index));
                commonPagerTitleView.setContentView(tabBinding.getRoot());
                commonPagerTitleView.setOnPagerTitleChangeListener(new CommonPagerTitleView.OnPagerTitleChangeListener() {

                    @Override
                    public void onSelected(int index, int totalCount) {
                        tabBinding.tvTitle.setTextSize(24);
                        RxBus.getInstance().post(index, Constants.POST_TAG_FIRST_TAB_CHECKED_INDEX);
                    }

                    @Override
                    public void onDeselected(int index, int totalCount) {
                        tabBinding.tvTitle.setTextSize(18);
                    }

                    @Override
                    public void onLeave(int index, int totalCount, float leavePercent, boolean leftToRight) {
                    }

                    @Override
                    public void onEnter(int index, int totalCount, float enterPercent, boolean leftToRight) {

                    }
                });

                commonPagerTitleView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (AppPageRouter.getFirstTabLockStatus()) {
                            return;
                        }
                        getDataBinding().viewPagerMatchRecommend.setCurrentItem(index, false);
                    }
                });
                return commonPagerTitleView;
            }

            @Override
            public IPagerIndicator getIndicator(Context context) {
                return null;
            }
        });
        getDataBinding().magicIndicator.setNavigator(commonNavigator);
        ViewPager2Helper.bind(getDataBinding().magicIndicator, getDataBinding().viewPagerMatchRecommend);
    }

    @Override
    public void clickCancellation() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        getViewModel().requestMatchHide();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        RxBus.getInstance().unregister(this);
    }
}