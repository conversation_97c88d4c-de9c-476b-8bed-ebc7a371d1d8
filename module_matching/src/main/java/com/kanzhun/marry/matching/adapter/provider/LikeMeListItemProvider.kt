package com.kanzhun.marry.matching.adapter.provider

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.textOrInvisible
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.bean.InteractUserBean
import com.kanzhun.marry.matching.databinding.MatchingLikeMeListItemV2Binding
import com.kanzhun.marry.matching.fragment.interact.perform.InteractGuruGuideViewModel
import com.kanzhun.marry.matching.utils.point.MatchPointReporter

class LikeMeListItemProvider(
    val viewModel: InteractGuruGuideViewModel,
    val chatClickListener: (user: InteractUserBean?) -> Unit = {}
) : BaseItemProvider<BaseListItem, MatchingLikeMeListItemV2Binding>() {
    override fun onBindItem(binding: MatchingLikeMeListItemV2Binding, item: BaseListItem) {
        if (item is InteractUserBean) {
            binding.run {
                if (viewModel.hasGuideBlock) {
                    ivAvatar.loadBlur(item.tinyAvatar)
                } else {
                    ivAvatar.load(item.tinyAvatar)
                }

                if (item.sameActivity == true) {
                    composeViewCoevent.visible()
                    composeViewCoevent.onSetWindowContent {
                        CoEvent(
                            modifier = Modifier.padding(
                                top = 8.dp,
                                start = 8.dp,
                                bottom = 2.dp
                            )
                        )
                    }
                } else {
                    composeViewCoevent.gone()
                }

                composeView.onSetWindowContent {
                    UserDesc(item)
                }

                val statusString = item.getStatusString()
                llStatus.setBackgroundResource(if (statusString.isEmpty()) R.color.common_translate else R.color.common_black_60)

                // 互相喜欢
                tvStatus.textOrInvisible(statusString)
                if (item.likeEachOther) {
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
                        0,
                        0,
                        R.drawable.matching_icon_like_each_other_16,
                        0
                    )
                } else {
                    tvStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, 0, 0)
                }

                tvNew.visible(item.readStatus == 0)
                setInteractListTag(tvAge, divider11, tvHeight, item.recommendTag)
                tvRecommendReason.textOrGone(item.recommendReason)

                root.clickWithTrigger {
                    MatchPointReporter.reportLikeMeItemClick(
                        item,
                        if (item.readStatus == 0) "1" else "0",
                        getAdapter()?.data,
                        viewModel.page
                    )
                    ChatPageRouter.jumpToSingleChatActivityWithTemporaryContact(
                        it.context, item.userId,
                        item.tinyAvatar, item.nickName, item.relationStatus, true, item.securityId,
                        PageSource.F2_LIKE_ME_CHILD_FRAGMENT
                    )
                    chatClickListener(item)
                    if (item.readStatus == 0) {
                        item.readStatus = 1
                        tvNew.gone()
                    }
                }
            }
        }
    }
}

@Composable
private fun UserDesc(userBean: InteractUserBean) {
    val top = colorResource(R.color.common_color_000000)
    val bottom = colorResource(R.color.common_color_000000_40)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .conditional(userBean.showLikeReason) {
                background(
                    brush = Brush.verticalGradient(
                        colors = listOf(top, bottom, bottom)
                    )
                )
            }
            .padding(horizontal = 12.dp)
            .padding(top = 4.dp, bottom = 8.dp)
    ) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = userBean.nickName ?: "",
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = TextStyle(
                    color = Color.White,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.W100,
                    fontFamily = boldFontFamily()
                ),
                modifier = Modifier.weight(1f)
            )

            if (!userBean.showLikeReason) {
                Image(
                    painter = painterResource(R.drawable.matching_icon_interact_chat),
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
            }
        }

        if (userBean.showLikeReason) {
            Row(
                modifier = Modifier
                    .padding(top = 6.dp)
                    .background(
                        color = colorResource(R.color.common_color_000000_60),
                        shape = RoundedCornerShape(12.dp)
                    )
                    .padding(horizontal = 8.dp)
                    .wrapContentWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = userBean.likeReason ?: "",
                    color = Color.White,
                    maxLines = 1,
                    fontSize = 12.sp,
                    fontWeight = FontWeight(500),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(top = 3.dp, bottom = 4.dp)
                        .wrapContentWidth()
                        .weight(weight = 1f, fill = false)
                )

                Spacer(modifier = Modifier.width(8.dp))

                Image(
                    painter = painterResource(R.drawable.matching_icon_interact_chat),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(vertical = 3.dp)
                        .size(18.dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewNoUserDesc() {
    UserDesc(userBean = InteractUserBean().apply {
        nickName = "李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽"
        showLikeReason = false
    })
}

@Preview(showBackground = true)
@Composable
private fun PreviewUserDesc() {
    UserDesc(userBean = InteractUserBean().apply {
        nickName = "李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽李旭泽"
        likeReason = "你好\uD83D\uDC4B，有兴趣互相聊一聊吗？加个好友。你真的很有意思！哈哈哈～"
        showLikeReason = true
    })
}