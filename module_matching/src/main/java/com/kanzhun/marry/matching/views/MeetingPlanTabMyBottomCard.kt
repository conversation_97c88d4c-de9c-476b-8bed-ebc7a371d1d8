package com.kanzhun.marry.matching.views

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.kotlin.ext.colorResource
import com.kanzhun.common.util.AppUtil
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.R
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.simplePost
import com.kanzhun.foundation.model.profile.UserTabModel.MenuBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.router.SocialPageRouter
import com.kanzhun.marry.matching.meetingplan.viewmodel.TabMyViewModel
import com.kanzhun.utils.T


@Composable
fun MeetingPlanTabMyBottomCard(
    modifier: Modifier = Modifier,
    viewModel: TabMyViewModel = viewModel(),
    inPreview: Boolean = false
){
    val context = LocalContext.current
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(color = Color(0xFFFFFFFF))
            .padding(vertical = 16.dp)
    ) {
        val userTabModel = viewModel.userTabModel
//        val menuList = viewModel.userTabModel?.menuList ?: emptyList<MenuBean>()
        val officeActivityBean = viewModel.officeActivityBean

        Column {
//            menuList.forEach { menuItem ->
//                MeetingPlanTabMyItem(url = menuItem.menuIcon,
//                    title = menuItem.menuTitle,
//                    tipContent = menuItem.tipContent,
//                    showRedPoint = false,
//                    onClick = {
//                        if (menuItem.jumpUrl.isNotEmpty()) {
//                            ProtocolHelper.parseProtocol(menuItem.jumpUrl)
//                        }
//                    })
//
//            }

            MeetingPlanTabMyItem(
                title = "我的二维码",
                id = R.drawable.me_ic_icon_my_qr_code,
                showRedPoint = false,
                onClick = {
                    if ( (userTabModel?.blockInfo?.block == true && userTabModel.blockInfo.baseInfoBlock == true)
                        || !AccountHelper.getInstance().isCanMatch()) {
                        T.ss("请先完成新手任务哦");
                    } else {
                        MePageRouter.jumpQRCodeActivity(context, PageSource.F4_ME_CHILD_FRAGMENT, "0")
                    }
                })

            MeetingPlanTabMyItem(
                title = "我的认证",
                tipContent = userTabModel?.certInfo?.tipContent,
                id = R.drawable.me_ic_icon_verity,
                showRedPoint = false,
                onClick = {
                    if (userTabModel?.blockInfo?.block == true && userTabModel.blockInfo.baseInfoBlock == true) {
                        T.ss("请先完成个人信息填写");
                    } else {
                        MePageRouter.jumpToMeAuthActivity(context, PageSource.F4_ME_CHILD_FRAGMENT, "")
                    }
                })

            val str = if(userTabModel?.moment?.publishStatus == 1) "" else "去发布"
            MeetingPlanTabMyItem(
                title = "我的动态",
                id = R.drawable.me_ic_icon_friend_group,
                tipContent = str,
                showRedPoint = userTabModel?.moment?.showRedPoint == 1,
                onClick = {
                    if (userTabModel == null)return@MeetingPlanTabMyItem
                    if( userTabModel.showNoviceTask == 1){
                        T.ss("请先完成新手任务哦")
                        return@MeetingPlanTabMyItem
                    }
                    if (AccountHelper.getInstance().isUserCommunityLocked()) {
                        T.ss("社区功能被限制")
                        return@MeetingPlanTabMyItem
                    }
                    if( userTabModel.moment.showRedPoint == 1) {
                        URLConfig.URL_MINE_RED_POINT_CLEAR.simplePost()
                    }
                    if (userTabModel.moment.publishStatus == 0) {
                        SocialPageRouter.jumpToPublishActivity(context, "", Constants.PUBLISH_TYPE_SQUARE)
                    } else {
                        SocialPageRouter.jumpToUserDynamicActivity(context, AccountHelper.getInstance().getUserId())
                    }
                })

            MeetingPlanTabMyItem(
                title = "帮助与反馈",
                id = R.drawable.me_ic_icon_setting_help,
                showRedPoint = false,
                onClick = {
                    MePageRouter.jumpToSettingHelpActivity(context)
                })

            MeetingPlanTabMyItem(
                title = "设置",
                id = R.drawable.me_ic_icon_me_setting,
                showRedPoint = false,
                onClick = {
                    AppUtil.startUri(context, MePageRouter.ME_SETTING)
                })
        }

    }
}

@Composable
fun MeetingPlanTabMyItem(
    url: String? = "",
    @DrawableRes id: Int = R.drawable.me_ic_icon_my_qr_code,
    title: String? = "",
    tipContent: String? = "",
    showRedPoint: Boolean = false,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .background(color = Color(0xFFFFFFFF))
            .padding(horizontal = 16.dp, vertical = 16.dp)
            .fillMaxWidth()
            .noRippleClickable {
                onClick()
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (url?.isNotEmpty() == true){
            AsyncImage(
                model =  url,
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(20.dp)
            )
        }else{
            Image(
                painter = painterResource(id = id),
                contentDescription = "image description",
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.size(20.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = title?:"",
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF292929),
            ),
            maxLines = 1,
            overflow = androidx.compose.ui.text.style.TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f).fillMaxWidth()
        )

        Text(
            text = tipContent?:"",
            style = TextStyle(
                fontSize = 13.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFFB8B8B8),
            )
        )
        Spacer(modifier = Modifier.width(4.dp))

        if (showRedPoint){
            Box(modifier = Modifier.size(8.dp).background(color = R.color.image_color_red.colorResource(),
                shape = CircleShape))

            Spacer(modifier = Modifier.width(4.dp))
        }


        Image(
            painter = painterResource(id = R.drawable.me_ic_gray_right_arrow),
            contentDescription = "image description",
            contentScale = ContentScale.None,
            modifier = Modifier
        )

    }
}


@Preview
@Composable
private fun PreviewMeetingPlanTabMyItem() {
    MeetingPlanTabMyItem(
        url = "",
        title = "我的活动",
        tipContent = "tipContent",
        showRedPoint = true
    )
}



@Preview
@Composable
private fun PreviewMeetingPlanTabMyBottomCard() {
    MeetingPlanTabMyBottomCard(
    )
}