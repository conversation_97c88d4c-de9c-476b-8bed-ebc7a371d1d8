package com.kanzhun.marry.matching.api.model

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import com.google.gson.annotations.SerializedName
import java.io.Serializable

/*
    {
        "planId":"xxx",//计划id
        "nickName":"张三",//张三
        "meetPlanStatus":1,//见面计划状态：0:未开启 10-等待中；20-排序中；30-匹配中；40-聊天中；50-已结束；
        "allowSortSubmitTime":11111234421233,//允许提交排序时间
        "processList":[
            {
                "icon":"http://xxx",//图标
                "content":"测评分析",
                "remark":"捞人中",//备注，只有等待中状态会有值
                "status":0,//状态：0-未开始；1-进行中；2-已完成
                "statusDesc":"已完成",//状态描述
            }
        ],
        "description":"规则说明",//中间颜色字段，通过特殊符号隔开，参考官方消息里的点击跳转按钮，开始字符："\u200b"，结束字符："\u2060"
        "dialogContent":"我们将参照双方的排序意愿",//排序二次确认弹窗文案
        "userList":[
            {
                "userId":"xxxxx",//加密用户id
                "tinyAvatar": "xxxx", //用户头像缩略图
                "nickName": "张三", // 昵称
                "matchScore": 90, // 匹配分
                "recommendTag": ["30岁", "195cm"], // 推荐标签
                "intro":"自我介绍",
                "matchAnalysisOverview":"匹配建议",
            }
        ]
    }
 */
data class SortingDetailResponse(
    @SerializedName("planId")
    var planId: String? = null,

    @SerializedName("nickName")
    var nickName: String? = null,

    @SerializedName("meetPlanStatus")
    var meetPlanStatus: Int? = null, // 0:未开启 10-等待中；20-排序中；30-匹配中；40-聊天中；50-已结束

    @SerializedName("allowSortSubmitTime")
    var allowSortSubmitTime: Long? = null,

    @SerializedName("processList")
    var processList: List<ProcessItem>? = null,

    @SerializedName("description")
    var description: String? = null, // 中间颜色字段，通过特殊符号隔开，参考官方消息里的点击跳转按钮，开始字符："\u200b"，结束字符："\u2060"

    @SerializedName("dialogContent")
    var dialogContent: String? = null, // 排序二次确认弹窗文案

    @SerializedName("userList")
    var userList: List<User>? = null,

    var testModule: TestModuleResponse? = null
) : Serializable {
    data class User(
        @SerializedName("userId")
        var userId: String? = null,

        @SerializedName("tinyAvatar")
        var tinyAvatar: String? = null,

        @SerializedName("nickName")
        var nickName: String? = null,

        @SerializedName("matchScore")
        var matchScore: String? = null,

        @SerializedName("recommendTag")
        var recommendTag: List<String>? = null,

        @SerializedName("intro")
        var intro: String? = null,

        @SerializedName("matchAnalysisOverview")
        var matchAnalysisOverview: String? = null,

        // 匹配建议跳转链接
        @SerializedName("analysisJumpUrl")
        var analysisJumpUrl: String? = null,

        val selectedIndex: MutableState<Int> = mutableIntStateOf(0),
    ) : Serializable
}