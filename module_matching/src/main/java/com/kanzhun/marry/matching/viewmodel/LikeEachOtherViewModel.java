package com.kanzhun.marry.matching.viewmodel;

import android.app.Application;
import android.content.res.Resources;

import androidx.databinding.ObservableField;

import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.marry.matching.R;
import com.kanzhun.foundation.model.matching.LikeEachOtherBean;

public class LikeEachOtherViewModel extends FoundationViewModel {
    private static final String TAG = "LikeEachOtherViewModel";

    private ObservableField<String> otherName = new ObservableField<>();
    private ObservableField<String> myName = new ObservableField<>("");
    private ObservableField<String> otherLikeModule = new ObservableField<>("");
    private ObservableField<String> youLikeModule = new ObservableField<>("");
    private ObservableField<String> otherLikeReason = new ObservableField<>();
    private ObservableField<String> youLikeReason = new ObservableField<>();
    private ObservableField<String> otherPhoto = new ObservableField<>();
    private ObservableField<String> myPhoto = new ObservableField<>();
    private LikeEachOtherBean likeBean;
    private Resources mRes;

    public LikeEachOtherViewModel(Application application) {
        super(application);
        mRes = application.getResources();
    }

    public ObservableField<String> getOtherName() {
        return otherName;
    }

    public void setOtherName(String otherName) {
        this.otherName.set(otherName);
    }

    public ObservableField<String> getOtherLikeModule() {
        return otherLikeModule;
    }

    public void setOtherLikeModule(String otherLikeModule) {
        this.otherLikeModule.set(otherLikeModule);
    }

    public ObservableField<String> getYouLikeModule() {
        return youLikeModule;
    }

    public void setYouLikeModule(String youLikeModule) {
        this.youLikeModule.set(youLikeModule);
    }

    public ObservableField<String> getOtherLikeReason() {
        return otherLikeReason;
    }

    public void setOtherLikeReason(String otherLikeReason) {
        this.otherLikeReason.set(otherLikeReason);
    }

    public ObservableField<String> getYouLikeReason() {
        return youLikeReason;
    }

    public void setYouLikeReason(String youLikeReason) {
        this.youLikeReason.set(youLikeReason);
    }

    public LikeEachOtherBean getLikeBean() {
        return likeBean;
    }

    public void setLikeBean(LikeEachOtherBean likeBean) {
        this.likeBean = likeBean;
        setOtherLikeModule(getModuleName(likeBean.otherInfo.resourceType));
        setOtherLikeReason(likeBean.otherInfo.likeReason);
        setOtherName(likeBean.otherInfo.nickName);
        setYouLikeModule(getModuleName(likeBean.myInfo.resourceType));
        setYouLikeReason(likeBean.myInfo.likeReason);
        setOtherPhoto(likeBean.otherInfo.tinyPhoto);
        setMyPhoto(likeBean.myInfo.tinyPhoto);
        setMyName(getApplication().getResources().getString(R.string.matching_you));
    }

    public String getModuleName(int type) {
        switch (type) {
            case Constants.TYPE_LIKE_AVATAR:
            case Constants.TYPE_LIKE_A_B_FACE:
            case Constants.TYPE_LIKE_STORY:
                return mRes.getString(R.string.matching_photo);
            case Constants.TYPE_LIKE_TEXT:
                return mRes.getString(R.string.matching_story);
            case Constants.TYPE_LIKE_VOICE:
                return mRes.getString(R.string.matching_voice);
        }
        return getApplication().getResources().getString(R.string.matching_photo);
    }

    public ObservableField<String> getOtherPhoto() {
        return otherPhoto;
    }

    public ObservableField<String> getMyPhoto() {
        return myPhoto;
    }

    public void setOtherPhoto(String otherPhoto) {
        this.otherPhoto.set(otherPhoto);
    }

    public void setMyPhoto(String myPhoto) {
        this.myPhoto.set(myPhoto);
    }

    public ObservableField<String> getMyName() {
        return myName;
    }

    public void setMyName(String myName) {
        this.myName.set(myName);
    }
}