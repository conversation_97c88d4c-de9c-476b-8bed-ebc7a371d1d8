package com.kanzhun.marry.matching.activity;

import android.os.Bundle;

import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.adpter.ViewPagerFragmentAdapter;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.router.MatchingPageRouter;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import android.view.View;

import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.matching.databinding.MatchingActivityReviewListBinding;
import com.kanzhun.marry.matching.viewmodel.MatchingReviewListViewModel;
import com.kanzhun.marry.matching.callback.MatchingReviewListCallback;

@RouterUri(path = MatchingPageRouter.MATCHING_REVIEW_LIST_ACTIVITY)
public class MatchingReviewListActivity extends FoundationVMActivity<MatchingActivityReviewListBinding, MatchingReviewListViewModel> implements MatchingReviewListCallback {
    private boolean isFirst = true;

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.matching_activity_review_list;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ViewPagerFragmentAdapter adapter = new ViewPagerFragmentAdapter(this);
        adapter.setData(getViewModel().getFragments());
        getDataBinding().viewpager.setAdapter(adapter);
        getDataBinding().viewpager.setOffscreenPageLimit(2);
        getDataBinding().viewpager.setCurrentItem(0);
        getDataBinding().viewpager.setUserInputEnabled(false);
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    public void clickTitlePage(int index) {
        if (index == 0) {
            getDataBinding().tvNoLikePage.setBackgroundResource(R.drawable.matching_bg_review_title_selected);
            getDataBinding().tvLikePage.setBackgroundResource(R.drawable.matching_bg_review_title_normal);
            getDataBinding().tvNoLikePage.setTextColor(getResources().getColor(R.color.common_color_7171F6));
            getDataBinding().tvLikePage.setTextColor(getResources().getColor(R.color.common_color_191919));
        } else {
            getDataBinding().tvLikePage.setBackgroundResource(R.drawable.matching_bg_review_title_selected);
            getDataBinding().tvNoLikePage.setBackgroundResource(R.drawable.matching_bg_review_title_normal);
            getDataBinding().tvLikePage.setTextColor(getResources().getColor(R.color.common_color_7171F6));
            getDataBinding().tvNoLikePage.setTextColor(getResources().getColor(R.color.common_color_191919));
        }
        getDataBinding().viewpager.setCurrentItem(index);
    }

    @Override
    protected void onResume() {
        super.onResume();
        getViewModel().requestLikeList();
        getViewModel().requestNoLikeList(isFirst);
        if (isFirst) {
            isFirst = false;
        }
    }
}