package com.kanzhun.marry.matching.adpter.home

import android.content.Context
import android.view.ViewGroup.MarginLayoutParams
import androidx.fragment.app.FragmentActivity
import com.facebook.drawee.view.SimpleDraweeView
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.util.load
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.adpter.MatchingInterestTagsAdapter
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.LifePhoto
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.api.model.UserTagBean
import com.kanzhun.marry.matching.databinding.MatchingRecommendPagerItem2Binding
import com.kanzhun.marry.matching.dialog.MatchingHomeBlockDialog

class MatchingGuideUserViewHolder(private val mBinding: MatchingRecommendPagerItem2Binding) :
    IBaseMatchingPageViewHolder(mBinding) {

    override fun onBindView(data: IPageBean, position: Int, size: Int) {
        if (data is RecommendUser) {
            mBinding
                .setTags(data)
                .setUserAvatar(data)
                .setUserBaseInfo(data)
                .setIdentityTags(data)
//                .setUserDesc(data)
                .setLifePhoto(data)
                .setSendLikeButton(data)
                .root.clickWithTrigger {
                    onClick(it.context, data)
                }
            if(data.signInNum.isNullOrEmpty()){
                mBinding.idCount.gone()
            }else{
                mBinding.idCount.visible()
                mBinding.idCount.text = data.signInNum
                if(data.baseInfo?.gender == 1){
                    mBinding.idCount.setBackgroundColor(mBinding.idCount.context.getColor(R.color.common_color_53C0FF))
                }else{
                    mBinding.idCount.setBackgroundColor(mBinding.idCount.context.getColor(R.color.common_color_FF8FFF))
                }
            }

            if(data.baseInfo?.gender == 1){
                mBinding.idOfficeActivity.setBackgroundResource(R.mipmap.img_office_activity_male)
            }else{
                mBinding.idOfficeActivity.setBackgroundResource(R.mipmap.img_office_activity_female)
            }
            if(data.sameActivity != null){
                mBinding.idOfficeActivityLayout.visible()
                mBinding.idOfficeActivityText.text = data.sameActivity.showText
            }else{
                mBinding.idOfficeActivityLayout.gone()
            }
            val lp = mBinding.idCard.layoutParams
            if (lp is MarginLayoutParams){
                if(lp.topMargin != 0){
                    lp.topMargin = 0
                    mBinding.idCard.layoutParams = lp
                }
            }
        }
    }

    private fun onClick(context: Context, data: RecommendUser) {
        if (data.getBlock()) {
            MatchingHomeBlockDialog(context as FragmentActivity, data).show()
        } else {
            MePageRouter.jumpToInfoPreviewActivity(
                context,
                data.baseInfo?.encUserId,
                data.lid,
                PageSource.ACTIVITY_RECOMMEND,
                sourceType = "2",
                securityId = data.securityId
            )
        }

    }

    private fun MatchingRecommendPagerItem2Binding.setTags(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        //形象标签
        llSimilar.visible(data.hasTheSameTag())
        if (data.hasTheSameTag()) {
            tvSimilar.text = "你们有${data.tagInfo?.sameCount ?: 0}个共同点"
        }
        val tagList = data.tagInfo?.tagList ?: listOf()
        //不给item设置点击事件的话，点击没反应
        this.flTags.setAdapter(MatchingInterestTagsAdapter(tagList, data))
        return this
    }

    /**
     * @return 如果返回值是0，则不是相同标签，如果返回值>0，则是相同标签
     */
    private fun List<UserTagBean>.isAllTagTheSameLength(): Boolean {
        if (isEmpty()) {
            return false
        }
        var isTheSameLength = true
        val firstLength = this[0].tagName?.length ?: 0
        //对比是否是一样长度
        for (i in 1 until this.size) {
            val length = this[i].tagName?.length ?: 0
            if (length != firstLength) {
                isTheSameLength = false
                break
            }
        }
        return isTheSameLength
    }

    private fun MatchingRecommendPagerItem2Binding.setUserAvatar(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        if (data.getBlock()) {
            ivUserAvatar.loadBlur(data.baseInfo?.tinyAvatar, 40, 2)
        } else {
            ivUserAvatar.load(data.baseInfo?.tinyAvatar)
        }
        return this
    }

    private fun MatchingRecommendPagerItem2Binding.setUserBaseInfo(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        this.clInfo.run {
//            if (data.blockCard) {
//                ivUserAvatar.loadBlur(data.baseInfo?.tinyAvatar)
//            } else {
//                ivUserAvatar.load(data.baseInfo?.tinyAvatar)
//            }
            tvUserName.text = data.baseInfo?.nickName
            tvAge.text = "${data.baseInfo?.age}岁"
            tvHeight.text = "${data.baseInfo?.height}cm"
            tvJobInfo.setText(
                data.blockFields?.industryBlock ?: false,
                listOfNotNull(data.baseInfo?.industry, data.baseInfo?.career).joinToString("·")
            )
            tvAddress.text = data.baseInfo?.addressDesc
//            tvSalary.setText(
//                data.blockFields?.annualIncomeBlock ?: false,
//                data.privateInfo?.annualIncome
//            )
//            tvSalary.visible(!data.privateInfo?.annualIncome.isNullOrBlank())
//            divider2.visible(!data.privateInfo?.annualIncome.isNullOrBlank())
        }
        return this
    }

    private fun MatchingRecommendPagerItem2Binding.setIdentityTags(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        val certInfo = data.certInfo
        if (certInfo != null) {
//            tvIdentityCount.text = "已完成平台${certInfo.certPassCount}项信息认证"
//            tvIdentityCount.visible(certInfo.certPassCount > 0)
            tagView.layoutManager = FlexboxLayoutManager(root.context)
            tagView.adapter = HomeIdentityTagAdapter(data.certInfo.certTagList ?: listOf())
        }
        tagClickArea.clickWithTrigger {
            onClick(it.context, data)
        }
        llIdentityInfo.visible(certInfo != null)
        return this
    }

//    private fun MatchingRecommendPagerItem2Binding.setUserDesc(data: RecommendUser): MatchingRecommendPagerItem2Binding {
//        tvSelfIntroduction.textOrGone(data.baseInfo?.intro)
//        return this
//    }

    private fun MatchingRecommendPagerItem2Binding.setSendLikeButton(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        sendLikeButton.setLike(
            data.baseInfo?.encUserId,
            data.baseInfo?.nickName,
            data.baseInfo?.tinyAvatar,
            data.relationShowStatus,
            data.showAnimation,
            PageSource.ACTIVITY_RECOMMEND,
            data.securityId
        )
        //重置状态
        data.showAnimation = false
        sendLikeButton.isBlockByParent = data.getBlock()
        sendLikeButton.blockCallback = {
            onClick(root.context, data)
        }
        return this
    }

    private fun MatchingRecommendPagerItem2Binding.setLifePhoto(data: RecommendUser): MatchingRecommendPagerItem2Binding {
        val photoList = mutableListOf<LifePhoto>()
        photoList.addAll(data.lifePhotoList?:mutableListOf())
        photoList.addAll(data.introImgList ?: mutableListOf())
        photoList.addAll(data.interestImgList ?: mutableListOf())
        photoList.addAll(data.familyImgList ?: mutableListOf())
        when {
            photoList.isEmpty() -> {
                clLifePhoto.gone()
            }

            photoList.size == 1 -> {
                clLifePhoto.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                ivPicture2.gone()
                ivPicture3.gone()
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            photoList.size == 2 -> {
                clLifePhoto.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                ivPicture2.visible()
                ivPicture3.gone()
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            photoList.size == 3 -> {
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                loadPic(ivPicture3,data, photoList[2].tinyPhoto, photoList[2].photoStyle)
                clLifePhoto.visible()
                ivPicture2.visible()
                ivPicture3.visible()
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            else -> {
                clLifePhoto.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                loadPic(ivPicture3,data, photoList[2].tinyPhoto, photoList[2].photoStyle)
                ivPicture2.visible()
                ivPicture3.visible()
                ivPicture4.visible()
                tvRemainNum.visible()
                tvRemainNum.setText("+" + (photoList.size - 3))

            }
        }
        return this
    }

    private fun loadPic(
        ivPicture1: SimpleDraweeView,
        data: RecommendUser,
        url:String?,
        style:Int?
    ) {
        if (data.getBlock()) {
            ivPicture1.load(url, style)
        } else {
            ivPicture1.setImageURI(url)
        }
    }

}
