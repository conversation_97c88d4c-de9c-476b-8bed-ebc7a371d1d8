package com.kanzhun.marry.matching.adpter.home

import android.content.Context
import android.view.ViewGroup
import androidx.fragment.app.FragmentActivity
import com.facebook.drawee.view.SimpleDraweeView
import com.google.android.flexbox.FlexboxLayoutManager
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.sendBooleanLiveEvent
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.util.load
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.AppTheme
import com.kanzhun.foundation.utils.THEME
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.R
import com.kanzhun.marry.matching.adpter.MatchingInterestTagsAdapter
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.LifePhoto
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.kanzhun.marry.matching.api.model.UserTagBean
import com.kanzhun.marry.matching.databinding.MatchingRecommendPagerItemCBinding
import com.kanzhun.marry.matching.dialog.MatchingHomeBlockDialog
import com.kanzhun.utils.T

class MatchingUserChristmasViewHolder(private val mBinding: MatchingRecommendPagerItemCBinding) :
    IBaseMatchingPageViewHolder(mBinding) {

    override fun onBindView(data: IPageBean, position: Int, size: Int) {
        if (data is RecommendUser) {
            mBinding
                .setTags(data)
                .setUserAvatar(data)
                .setUserBaseInfo(data)
                .setIdentityTags(data)
//                .setUserDesc(data)
                .setLifePhoto(data)
                .setSendLikeButton(data)
                .root.clickWithTrigger {
                    onClick(it.context, data)
                }
            if(data.signInNum.isNullOrEmpty()){
                mBinding.idCount.gone()
            }else{
                mBinding.idCount.visible()
                mBinding.idCount.text = data.signInNum
                if(data.baseInfo?.gender == 1){
                    mBinding.idCount.setBackgroundColor(mBinding.idCount.context.getColor(R.color.common_color_53C0FF))
                }else{
                    mBinding.idCount.setBackgroundColor(mBinding.idCount.context.getColor(R.color.common_color_FF8FFF))
                }
            }

            if(data.sameActivity != null){
                mBinding.idOfficeActivityLayout.visible()
                mBinding.idOfficeActivityText.text = data.sameActivity.showText

            }else{
                mBinding.idOfficeActivityLayout.gone()
            }
            if (AppTheme.getTheme() == THEME.CHRISTMAS && data.getBlock()){
                mBinding.clLifePhoto.gone()
                mBinding.idThemeShadowText.visible()
                mBinding.sendLikeButtonTheme.visible()
                mBinding.sendLikeButton.gone()
                mBinding.idThemeShadow.visible()
            } else if(AppTheme.christmasShinShow == 1 && data.getBlock()){
                mBinding.clLifePhoto.gone()
                mBinding.idThemeShadowText.visible()
                mBinding.sendLikeButtonTheme.visible()
                mBinding.sendLikeButton.gone()
                mBinding.idThemeShadow.gone()
            }else{
                mBinding.sendLikeButtonTheme.gone()
                mBinding.sendLikeButton.visible()
                mBinding.idThemeShadowText.gone()
                mBinding.clLifePhoto.visible()
                mBinding.idThemeShadow.visible()
            }
            mBinding.sendLikeButtonTheme.onClick {

//                //兑换-刷新f1
                HttpExecutor.requestSimplePost(
                    URLConfig.URL_MATCHING_LIMITED_ACTIVITY_TASK_RCDCARD_EXCHANGE,
                    null,
                    object : SimpleRequestCallback(true) {

                        override fun onSuccess() {
                            T.ss("恭喜兑换成功")
                            sendBooleanLiveEvent(Constants.POST_TAG_REFRESH_F1, true)
                        }

                        override fun dealFail(reason: ErrorReason) {

                        }
                    })

            }
            mBinding.idThemeShadowText.onClick {
                ChatPageRouter.jumpToSantaClausActivity(mBinding.idThemeShadowText.context,PageSource.F1_RECOMMEND_CHILD_FRAGMENT)
            }
            when(AppTheme.getTheme()){
                THEME.NORMAL -> {
                    mBinding.idIcon.gone()
                    mBinding.idThemeShadow.gone()
                    val lp = mBinding.idCard.layoutParams as ViewGroup.MarginLayoutParams
                    lp.topMargin = mBinding.root.context.resources.getDimension(R.dimen.app_f1_top_text_height_and_bar).toInt()
                }
                THEME.CHRISTMAS -> {
                    val lp = mBinding.idCard.layoutParams as ViewGroup.MarginLayoutParams
                    lp.topMargin = 60.dpI
                    mBinding.idIcon.visible()
                    when(position % 5){
                        0 -> {
                            mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_4)
                        }
                        1 -> {
                            mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_0)
                        }
                        2 -> {
                            mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_1)
                        }
                        3 -> {
                            mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_2)
                        }
                        4 -> {
                            mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_3)
                        }
                    }
                    when(position % 2){
                        0 -> {
                            mBinding.idThemeShadow.setBackgroundResource(R.mipmap.theme_christmas_frame_0)
                        }
                        1 -> {
                            mBinding.idThemeShadow.setBackgroundResource(R.mipmap.theme_christmas_frame_1)
                        }
                    }
                    if (position == 10) {
                        mBinding.idIcon.setImageResource(R.mipmap.theme_christmas_toy_11)
                    }
                    mBinding.idThemeShadow.visible()
                }
            }
        }
    }

    private fun onClick(context: Context, data: RecommendUser) {
        if (AppTheme.christmasShinShow == 1 && data.getBlock()){
            return
        }
        if (data.getBlock()) {
            MatchingHomeBlockDialog(context as FragmentActivity, data).show()
        } else {
            MePageRouter.jumpToInfoPreviewActivity(
                context,
                data.baseInfo?.encUserId,
                data.lid,
                PageSource.F1_RECOMMEND_CHILD_FRAGMENT,
                securityId = data.securityId
            )
        }

    }

    private fun MatchingRecommendPagerItemCBinding.setTags(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        //形象标签
        llSimilar.visible(data.hasTheSameTag())
        if (data.hasTheSameTag()) {
            tvSimilar.text = "你们有${data.tagInfo?.sameCount ?: 0}个共同点"
        }
        val tagList = data.tagInfo?.tagList ?: listOf()
        //不给item设置点击事件的话，点击没反应
        this.flTags.setAdapter(MatchingInterestTagsAdapter(tagList, data))
        return this
    }

    /**
     * @return 如果返回值是0，则不是相同标签，如果返回值>0，则是相同标签
     */
    private fun List<UserTagBean>.isAllTagTheSameLength(): Boolean {
        if (isEmpty()) {
            return false
        }
        var isTheSameLength = true
        val firstLength = this[0].tagName?.length ?: 0
        //对比是否是一样长度
        for (i in 1 until this.size) {
            val length = this[i].tagName?.length ?: 0
            if (length != firstLength) {
                isTheSameLength = false
                break
            }
        }
        return isTheSameLength
    }

    private fun MatchingRecommendPagerItemCBinding.setUserAvatar(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        if (data.getBlock()) {
            if(data.baseInfo?.avatarStyle == 2){
                ivUserAvatar.loadBlur(data.baseInfo.tinyAvatar, 40, 2)
            }else {
                ivUserAvatar.load(data.baseInfo?.tinyAvatar)
            }
        } else {
            ivUserAvatar.load(data.baseInfo?.tinyAvatar)
        }
        return this
    }

    private fun MatchingRecommendPagerItemCBinding.setUserBaseInfo(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        this.clInfo.run {
            tvUserName.text = data.baseInfo?.nickName
            tvAge.text = "${data.baseInfo?.age}岁"
            tvHeight.text = "${data.baseInfo?.height}cm"
            tvJobInfo.setText(
                data.blockFields?.industryBlock ?: false,
                listOfNotNull(data.baseInfo?.industry, data.baseInfo?.career).joinToString("·")
            )
            tvAddress.text = data.baseInfo?.addressDesc
        }
        return this
    }

    private fun MatchingRecommendPagerItemCBinding.setIdentityTags(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        val certInfo = data.certInfo
        if (certInfo != null) {
            tagView.layoutManager = FlexboxLayoutManager(root.context)
            tagView.adapter = HomeIdentityTagAdapter(data.certInfo.certTagList ?: listOf())
        }
        tagClickArea.clickWithTrigger {
            onClick(it.context, data)
        }
        llIdentityInfo.visible(certInfo != null)
        return this
    }

    private fun MatchingRecommendPagerItemCBinding.setSendLikeButton(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        sendLikeButton.setLike(
            data.baseInfo?.encUserId,
            data.baseInfo?.nickName,
            data.baseInfo?.tinyAvatar,
            data.relationShowStatus,
            data.showAnimation,
            PageSource.F1_RECOMMEND_CHILD_FRAGMENT,
            data.securityId
        )
        //重置状态
        data.showAnimation = false
        sendLikeButton.isBlockByParent = data.getBlock()
        sendLikeButton.blockCallback = {
            onClick(root.context, data)
        }
        return this
    }

    private fun MatchingRecommendPagerItemCBinding.setLifePhoto(data: RecommendUser): MatchingRecommendPagerItemCBinding {
        val photoList = mutableListOf<LifePhoto>()
        photoList.addAll(data.lifePhotoList?:mutableListOf())
        photoList.addAll(data.introImgList ?: mutableListOf())
        photoList.addAll(data.interestImgList ?: mutableListOf())
        photoList.addAll(data.familyImgList ?: mutableListOf())
        when {
            photoList.isEmpty() -> {
                clLifePhoto.gone()
            }

            photoList.size == 1 -> {
                clLifePhoto.visible()
                ivPicture1.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                ivPicture2.gone()
                ivPicture3.gone()
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            photoList.size == 2 -> {
                clLifePhoto.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                ivPicture1.visible()
                ivPicture2.visible()
                ivPicture3.gone()
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            photoList.size == 3 -> {
                clLifePhoto.visible()
                ivPicture1.visible()
                ivPicture2.visible()
                ivPicture3.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                loadPic(ivPicture3,data, photoList[2].tinyPhoto, photoList[2].photoStyle)
                ivPicture4.gone()
                tvRemainNum.gone()

            }

            else -> {
                clLifePhoto.visible()
                ivPicture2.visible()
                loadPic(ivPicture1,data, photoList[0].tinyPhoto, photoList[0].photoStyle)
                loadPic(ivPicture2,data, photoList[1].tinyPhoto, photoList[1].photoStyle)
                loadPic(ivPicture3,data, photoList[2].tinyPhoto, photoList[2].photoStyle)
                ivPicture3.visible()
                ivPicture4.visible()
                tvRemainNum.visible()
                tvRemainNum.setText("+" + (photoList.size - 3))

            }
        }
        return this
    }

    private fun loadPic(
        ivPicture1: SimpleDraweeView,
        data: RecommendUser,
        url:String?,
        style:Int?
    ) {
        if (data.getBlock()) {
            ivPicture1.load(url, style)
        } else {
            ivPicture1.setImageURI(url)
        }
    }
}