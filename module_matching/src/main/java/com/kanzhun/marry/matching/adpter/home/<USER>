package com.kanzhun.marry.matching.adpter.home

import android.view.ViewGroup
import com.kanzhun.marry.matching.api.model.IPageBean
import com.kanzhun.marry.matching.api.model.RecommendUser
import com.youth.banner.adapter.BannerAdapter

/**
 * <AUTHOR>
 * @date 2022/9/6.
 */
class MatchingRecommendPageAdapter(datas: List<IPageBean>) : BannerAdapter<IPageBean, IBaseMatchingPageViewHolder>(datas) {
    override fun onCreateHolder(parent: ViewGroup, viewType: Int): IBaseMatchingPageViewHolder {
        return MatchingHomeViewHolderFactory.createViewHolder(parent, viewType)
    }

    override fun onBindView(holder: IBaseMatchingPageViewHolder, data: IPageBean, position: Int, size: Int) {
        holder.onBindView(data, position, size)
    }

    override fun getItemViewType(position: Int): Int {
        return getData(position).getItemType()
    }

    fun updateSendLikeStatus(userId: String?,status:Int,hasShowToast:Boolean = true) {
        if (!userId.isNullOrBlank()) {
            mDatas.forEachIndexed { index, iPageBean ->
                if (iPageBean is RecommendUser && iPageBean.baseInfo?.encUserId == userId) {
                    iPageBean.relationShowStatus = status
                    iPageBean.showAnimation = !hasShowToast
                    notifyItemChanged(index)
//                    notifyDataSetChanged()
                }
            }
        }
    }

    fun updateList(newList: List<IPageBean>) {
        val newSize = newList.size
        val oldSize = mDatas.size
        if (newSize == oldSize) {
            newList.forEachIndexed { index, iPageBean ->
                mDatas.set(index, iPageBean)
                notifyItemChanged(index)
            }
        } else {
            setDatas(newList)
        }
    }

    fun getAllData(): MutableList<IPageBean>? {
        return mDatas
    }

}

