package com.kanzhun.marry.matching.bindingadapter;

import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.text.TextUtils;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;

import com.kanzhun.common.util.DateFormatUtils;
import com.kanzhun.common.util.LText;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.model.User;
import com.kanzhun.marry.matching.R;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/1
 */
public class MatchingBindingAdapter {

    @BindingAdapter("loveStartDate")
    public static void setLoveStartDate(TextView textView, long dateTime) {
        if (dateTime > 0) {
            textView.setText(DateFormatUtils.getMatchLoveDate(dateTime));
        }
    }

    @BindingAdapter("dateLoveDay")
    public static void setDateLoveDay(TextView textView, MatchingPageInfo info) {
        if (info == null) {
            return;
        }
        if (info.matchStatus == User.STATUS_MATCH_DATE) {
//            long currentTime = System.currentTimeMillis();
//            long date = (long) Math.floor((currentTime - info.dateTime) / (24.0f * 3600 * 1000));
            textView.setText(String.valueOf(info.dateHours));
        } else if (info.matchStatus == User.STATUS_MATCH_LOVE) {
//            long currentTime = System.currentTimeMillis();
//            long date = (long) Math.ceil((currentTime - info.loveTime) / (24.0f * 3600 * 1000));
            if (!TextUtils.isEmpty(info.loveDays)) {
                textView.setText(info.loveDays);
            }
        }
    }

    @BindingAdapter("dateLoveDayUnit")
    public static void setDateLoveDayUnit(TextView textView, MatchingPageInfo info) {
        if (info == null) {
            return;
        }
        if (info.matchStatus == User.STATUS_MATCH_DATE) {
            if (info.dateHours > 1) {
                textView.setText(R.string.matching_lovers_date_hours);
            } else {
                textView.setText(R.string.matching_lovers_date_hour);
            }
        } else if (info.matchStatus == User.STATUS_MATCH_LOVE) {
            try {
                if (!TextUtils.isEmpty(info.loveDays)) {
                    int loveDays = Integer.parseInt(info.loveDays);
                    if (loveDays > 1) {
                        textView.setText(R.string.matching_lovers_love_days);
                    } else {
                        textView.setText(R.string.matching_lovers_love_day);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
    }

    @BindingAdapter({"likeType", "gender"})
    public static void setLikeSource(TextView textView, int resourceType, int gender) {
            String genderString = "Ta";
            if (gender == Constants.MALE) {
                genderString = "他";
            } else if (gender == Constants.FEMALE) {
                genderString = "她";
            }
            Resources resources = textView.getContext().getResources();
            String likeModuleName = "";
            Drawable drawable = null;
            switch (resourceType) {
                case Constants.TYPE_LIKE_AVATAR:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_pic, null);
                    likeModuleName = LText.getString(com.kanzhun.foundation.R.string.common_photo);
                    break;
                case Constants.TYPE_LIKE_A_B_FACE:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_a_b, null);
                    likeModuleName = LText.getString(com.kanzhun.foundation.R.string.common_a_b_face);
                    break;
                case Constants.TYPE_LIKE_STORY:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_story, null);
                    likeModuleName = LText.getString(com.kanzhun.foundation.R.string.common_photo);
                    break;
                case Constants.TYPE_LIKE_TEXT:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_text_answer, null);
                    likeModuleName = LText.getString(com.kanzhun.foundation.R.string.common_answer);
                    break;
                case Constants.TYPE_LIKE_VOICE:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_voice, null);
                    likeModuleName = LText.getString(com.kanzhun.foundation.R.string.common_voice);
                    break;
                default:
                    break;
            }
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
                textView.setCompoundDrawables(drawable, null, null, null);
            }

            String content;
            content = resources.getString(R.string.chat_like_item_source, "", genderString, likeModuleName);
            textView.setText(content);
    }
}
