package com.kanzhun.marry.matching.activity;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Message;
import android.text.Editable;
import android.text.TextUtils;
import android.util.Log;
import android.view.ActionMode;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.common.dialog.CommonLayoutDialog;
import com.kanzhun.common.dialog.CommonSystemCenterDialog;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.http.OkHttpClientFactory;
import com.kanzhun.marry.matching.BR;
import com.kanzhun.marry.matching.R;
import com.kanzhun.marry.matching.callback.MatchingTemporaryChatCallback;
import com.kanzhun.marry.matching.databinding.ActivityMatchingTemporaryChatBinding;
import com.kanzhun.marry.matching.dialog.EmpathyMakeFriendDialog;
import com.kanzhun.marry.matching.viewmodel.MatchingTemporaryChatViewModel;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.rxbus.RxBus;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.text.SimpleDateFormat;
import java.util.Locale;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;

public class MatchingTemporaryChatActivity extends FoundationVMActivity<ActivityMatchingTemporaryChatBinding, MatchingTemporaryChatViewModel> implements MatchingTemporaryChatCallback {
    private static final String TAG = "MatchingTemporaryChatAc";

    private static final String ROOM_ID = "room_id";
    private static final String TIME = "time";
    //    private Socket socket;
    private AnimatorSet animatorSet;
    private ValueAnimator valueAnimator;
    private int mLeftMargin;
    private MatchingTemporaryChatHandler mHandler;
    int remainTimes;
    private static final String LEAVE_ROOM = "leaveRoom";
    private static final String SEND_MESSAGE = "sendMessage";
    private static final String SHOW_AVATAR = "showAvatar";
    private static final String USER_INFO = "userInfo";
    private static final String ALERT = "alert";
    private static final String DISCONNECT = "disconnect";

    private static final int MSG_USER_INFO_CHANGE = 0;
    private static final int MSG_SEND_MESSAGE = 1;
    private static final int MSG_ALERT = 2;
    private static final int MSG_ONLINE = 3;
    private static final int MSG_OFFLINE = 4;
    private static final int MSG_DISCONNECT = 5;
    private static final int MSG_SEND_INPUT = 6;
    private static final int MSG_EXIST_TO_SEARCH = 7;//退出到匹配搜索
    private static final int MSG_EXIST_TO_TAB = 8;//退出到匹配tab

    private int mIconTranslateY;

    private WebSocket webSocket;
    private OkHttpClient client;
    private TemporaryChatWebSocketListener mListener;
    private String mPreText = "";
    private CommonSystemCenterDialog quiteDialog;
    private EmpathyMakeFriendDialog successDialog;
    private CommonLayoutDialog failDialog;
    private LinearLayout.LayoutParams mTVCountDownMarginParams;
    private CountDownTimer mTimer;

    public static void jumpToTemporaryChatActivity(Context context, String roomId, int time, int remainTimes, String moodIc) {
        Intent intent = new Intent(context, MatchingTemporaryChatActivity.class);
        intent.putExtra(ROOM_ID, roomId);
        intent.putExtra(TIME, time);
        intent.putExtra(BundleConstants.BUNDLE_REMAIN_TIMES, remainTimes);
        intent.putExtra(BundleConstants.BUNDLE_MOOD_IC, moodIc);
        AppUtil.startActivity(context, intent);
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.activity_matching_temporary_chat;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        remainTimes = getIntent().getIntExtra(BundleConstants.BUNDLE_REMAIN_TIMES, 0);
        String roomId = getIntent().getStringExtra(ROOM_ID);
        long time = getIntent().getIntExtra(TIME, 120) * 1000L;
        getViewModel().setRoomId(roomId);
        String moodIc = getIntent().getStringExtra(BundleConstants.BUNDLE_MOOD_IC);
        if (!TextUtils.isEmpty(moodIc)) {
            getViewModel().setModeIcon(moodIc);
        }
        mHandler = new MatchingTemporaryChatHandler(this);

        User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
        if (user != null) {
            int gender = user.getGender();
            getViewModel().setGender(gender);
            if (gender == Constants.MALE) {
                getDataBinding().llSelfAvatar.setBackgroundResource(R.drawable.common_bg_circle_d4d5ff);
                getDataBinding().llFriendAvatar.setBackgroundResource(R.drawable.common_bg_circle_ffe9f4);
            } else {
                getDataBinding().llFriendAvatar.setBackgroundResource(R.drawable.common_bg_circle_d4d5ff);
                getDataBinding().llSelfAvatar.setBackgroundResource(R.drawable.common_bg_circle_ffe9f4);
            }
        }
        initView(time);
        initSocket(roomId);
        RxBus.getInstance().subscribe(this, Constants.LIFECYCLE_CHANGE, new RxBus.Callback<Boolean>() {
            @Override
            public void onEvent(Boolean foreground) {
                if (webSocket != null) {
                    if (!foreground) {
                        webSocket.close(3001, "background");
                    } else {
                        webSocket = client.newWebSocket(webSocket.request(), mListener);
                    }
                }
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        leaveRoom();
        if (webSocket != null) {
            webSocket.close(3002, "close");
            webSocket = null;
        }
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler = null;
        }
        RxBus.getInstance().unregister(this);
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }

    private void initView(long time) {
        mLeftMargin = (QMUIDisplayHelper.getScreenWidth(this) - QMUIDisplayHelper.dp2px(this, 310)) / 2 - QMUIDisplayHelper.dp2px(this, 16);
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) getDataBinding().llTemporaryChat.getLayoutParams();
        params.topMargin = StatusBarUtil.getStatusBarHeight(this);
        RelativeLayout.LayoutParams llModeParams = (RelativeLayout.LayoutParams) getDataBinding().llModeIcon.getLayoutParams();
        llModeParams.topMargin = StatusBarUtil.getStatusBarHeight(this);
        mTVCountDownMarginParams = (LinearLayout.LayoutParams) getDataBinding().tvCountDown.getLayoutParams();

        initCountDownTimer(time);
        getDataBinding().llEditInput.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getDataBinding().editInput.requestFocus();
                if (!QMUIKeyboardHelper.isKeyboardVisible(MatchingTemporaryChatActivity.this)) {
                    QMUIKeyboardHelper.showKeyboard(getDataBinding().editInput, true);
                }
                if (TextUtils.isEmpty(getDataBinding().editInput.getText())) {
                    getDataBinding().editInput.setSelection(0);
                }
            }
        });
        getDataBinding().editInput.requestFocus();

        final int marginTop = QMUIDisplayHelper.dp2px(this, 80);
        final int minMarginTop = QMUIDisplayHelper.dp2px(this, 20);
        mIconTranslateY = QMUIDisplayHelper.dp2px(this, 40);

        animatorSet = new AnimatorSet();
        animatorSet.setDuration(200);

        valueAnimator = new ValueAnimator();
        valueAnimator.setDuration(200);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                getDataBinding().ivMode1.setAlpha(0.4f * value);
                getDataBinding().ivMode2.setAlpha(0.1f * value);
                getDataBinding().ivMode3.setAlpha(0.6f + (1f - value) * 0.4f);
                getDataBinding().ivMode4.setAlpha(0.2f * value);
                getDataBinding().ivMode5.setAlpha(0.2f * value);
                getDataBinding().ivMode3.setTranslationY(-mIconTranslateY * (1f - value));
                int margin = (int) (marginTop * value);
                if (margin <= minMarginTop) {
                    margin = minMarginTop;
                }
                mTVCountDownMarginParams.topMargin = margin;
            }
        });

        getDataBinding().editInput.setCustomSelectionActionModeCallback(new ActionMode.Callback2() {
            @Override
            public boolean onCreateActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            @Override
            public boolean onPrepareActionMode(ActionMode mode, Menu menu) {
                return false;
            }

            @Override
            public boolean onActionItemClicked(ActionMode mode, MenuItem item) {
                return false;
            }

            @Override
            public void onDestroyActionMode(ActionMode mode) {

            }

            @Override
            public void onGetContentRect(ActionMode mode, View view, Rect outRect) {
                super.onGetContentRect(mode, view, outRect);
            }
        });

        getDataBinding().editInput.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return true;
            }
        });

        QMUIKeyboardHelper.setVisibilityEventListener(this, new QMUIKeyboardHelper.KeyboardVisibilityEventListener() {
            @Override
            public boolean onVisibilityChanged(boolean isOpen, int heightDiff) {
                ObjectAnimator objectAnimatorTextSize;
                ObjectAnimator objectAnimatorTranslationX;
                if (isOpen) {
                    valueAnimator.setFloatValues(1f, 0);
                    objectAnimatorTextSize = ObjectAnimator.ofFloat(getDataBinding().tvCountDown, "textSize", 68, 29);
                    objectAnimatorTranslationX = ObjectAnimator.ofFloat(getDataBinding().tvCountDown, "TranslationX", 0, -mLeftMargin);
                } else {
                    valueAnimator.setFloatValues(0, 1f);
                    objectAnimatorTextSize = ObjectAnimator.ofFloat(getDataBinding().tvCountDown, "textSize", 29, 68);
                    objectAnimatorTranslationX = ObjectAnimator.ofFloat(getDataBinding().tvCountDown, "TranslationX", -mLeftMargin, 0);
                }
                animatorSet.playTogether(objectAnimatorTextSize, objectAnimatorTranslationX);
                animatorSet.start();
                valueAnimator.start();
                return false;
            }
        });
    }

    private void initSocket(String roomId) {
        if (webSocket == null) {
            String url = SettingBuilder.getInstance().getTemporaryUrl();
            client = OkHttpClientFactory.getInstance().getGeneralClient();
            Request.Builder builder = new Request.Builder();
            Request request = builder.url(url + "/mgsocket/shake?roomId=" + roomId).build();
            mListener = new TemporaryChatWebSocketListener();
            webSocket = client.newWebSocket(request, mListener);
        }
    }

    private void initCountDownTimer(long time) {
        SimpleDateFormat sdf = new SimpleDateFormat("mm:ss.SS", Locale.CHINA);
        mTimer = new CountDownTimer(time, 120) {
            @Override
            public void onTick(long millisUntilFinished) {
                String time = sdf.format(millisUntilFinished);
                getDataBinding().tvCountDown.setText(time);
            }

            @Override
            public void onFinish() {
                getDataBinding().tvCountDown.setText("00:00.00");
                leaveRoom();
                if (!isFinishing() && getViewModel().getMakeFriend() == 0) {
                    //TODO 显示未成为好友弹窗，退出页面
                    showMakeFriendFailDialog();
                }
            }
        };
        mTimer.start();
    }

    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        return true;
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public void hideKeyboard() {
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().editInput);
    }

    private static class MatchingTemporaryChatHandler extends Handler {
        WeakReference<Activity> reference;

        public MatchingTemporaryChatHandler(Activity activity) {
            reference = new WeakReference<>(activity);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (null != reference) {
                MatchingTemporaryChatActivity activity = (MatchingTemporaryChatActivity) reference.get();
                if (null != activity) {
                    switch (msg.what) {
                        case MSG_USER_INFO_CHANGE:
                            try {
                                String content = (String) msg.obj;
                                Log.e("XXXXX", "content = " + content);
                                JSONObject jsonObject = new JSONObject(content);
                                String userId = jsonObject.optString("userId");
                                long time = jsonObject.optLong("ts");
                                String avatar = jsonObject.optString("tinyAvatar");
                                int makeFriend = jsonObject.optInt("makeFriend");
                                int linkStatus = jsonObject.optInt("linkStatus");//链接状态，可空，1-初始化，2-在线，3-离线，4-退出
                                if (TextUtils.equals(userId, AccountHelper.getInstance().getUserId())) {
                                    if (!TextUtils.isEmpty(avatar) && !TextUtils.equals(avatar, activity.getViewModel().getSelfAvatar().get())) {
                                        activity.getViewModel().setSelfAvatar(avatar);
                                        if (activity.getViewModel().getGender() == Constants.MALE) {
                                            activity.getDataBinding().llEditInput.setBackgroundResource(R.drawable.matching_bg_temporar_chat_male);
                                        } else {
                                            activity.getDataBinding().llEditInput.setBackgroundResource(R.drawable.matching_bg_temporar_chat_female);
                                        }
                                    }
                                    if (linkStatus == 3) {
                                        activity.getDataBinding().ivSelfLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_online);
                                    } else {
                                        activity.getDataBinding().ivSelfLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_offline);
                                    }
                                } else {
                                    if (!TextUtils.isEmpty(avatar) && !TextUtils.equals(avatar, activity.getViewModel().getFriendAvatar().get())) {
                                        activity.getViewModel().setFriendAvatar(avatar);
                                        if (activity.getViewModel().getGender() == Constants.MALE) {
                                            activity.getDataBinding().llFriendInput.setBackgroundResource(R.drawable.matching_bg_temporar_chat_female);
                                        } else {
                                            activity.getDataBinding().llFriendInput.setBackgroundResource(R.drawable.matching_bg_temporar_chat_male);
                                        }
                                    }
                                    activity.getViewModel().setFriendId(userId);
                                    if (linkStatus > 0) {
                                        if (linkStatus == 3) {
                                            activity.getDataBinding().ivFriendLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_online);
                                        } else {
                                            activity.getDataBinding().ivFriendLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_offline);
                                        }
                                    }
                                }
                                if (makeFriend == 1 && makeFriend != activity.getViewModel().getMakeFriend()) {
                                    if (!TextUtils.isEmpty(activity.getViewModel().getFriendId())) {
                                        activity.getViewModel().setMakeFriend(makeFriend);
                                        if (activity.mTimer != null) {
                                            activity.mTimer.cancel();
                                        }
                                        if (activity.quiteDialog != null && activity.quiteDialog.isShowing()) {
                                            activity.quiteDialog.dismiss();
                                        }
                                        if (activity.failDialog != null && activity.failDialog.isShowing()) {
                                            activity.failDialog.dismiss();
                                        }
                                        activity.leaveRoom();
                                        activity.successDialog =
                                                EmpathyMakeFriendDialog
                                                        .newInstance(activity, activity.getViewModel().getFriendId(), activity.getViewModel().getFriendAvatar().get(), activity.getViewModel().getSelfAvatar().get());
                                        activity.successDialog.show((FragmentActivity) activity);
                                    }
                                }
                            } catch (Exception e) {

                            }
                            break;
                        case MSG_SEND_MESSAGE:
                            try {
                                String content = (String) msg.obj;
                                JSONObject jsonObject = new JSONObject(content);
                                String userId = jsonObject.optString("userId");
                                long time = jsonObject.optLong("ts");
                                String text = jsonObject.optString("text");
                                if (TextUtils.equals(userId, AccountHelper.getInstance().getUserId())) {
                                    if (time > activity.getViewModel().getSelfUserInfoTs() && !TextUtils.equals(activity.getDataBinding().editInput.getText(), text)) {
                                        int selection = activity.getDataBinding().editInput.getSelectionStart();
                                        if (selection < 0) {
                                            selection = 0;
                                        }
                                        activity.getDataBinding().editInput.setText(text);
                                        if (!TextUtils.isEmpty(text)) {
                                            if (selection > text.length()) {
                                                selection = text.length();
                                            }
                                            activity.getDataBinding().editInput.setSelection(selection);
                                        }
                                    }
                                } else {
                                    if (time > activity.getViewModel().getSelfUserInfoTs() && !TextUtils.equals(activity.getDataBinding().editFriend.getText(), text)) {
                                        activity.getDataBinding().editFriend.setText(text);
                                    }
                                }
                            } catch (Exception e) {

                            }
                            break;
                        case MSG_ALERT:
                            try {
                                String content = (String) msg.obj;
                                JSONObject jsonObject = new JSONObject(content);
                                String toast = jsonObject.optString("toast");
                                int type = jsonObject.optInt("alertType");
                                if (type == 2) {
                                    //TODO  封禁弹窗
//                                    activity.showFailDialog();
                                    if (activity != null) {
                                        AppUtil.startActivity(activity, EmpathyActivity.createIntent(activity, activity.remainTimes - 1).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP));
                                    }
                                    AppUtil.finishActivity(activity);
                                }
                                T.ss(toast);
                            } catch (Exception e) {

                            }
                            break;
                        case MSG_ONLINE:
                            activity.getDataBinding().ivSelfLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_online);
                            break;
                        case MSG_OFFLINE:
                            activity.getDataBinding().ivSelfLinkStatus.setImageResource(R.drawable.matching_ic_temporary_chat_offline);
                            break;
                        case MSG_DISCONNECT:
//                            String content = (String) msg.obj;
//                            Log.e("XXXXX", "error = " + content);
                            break;
                        case MSG_SEND_INPUT:
                            activity.sendChat();
                            break;
                        case MSG_EXIST_TO_SEARCH://强制退出，并跳转到心情共鸣主界面
                            if (activity != null) {
                                AppUtil.startActivity(activity, EmpathyActivity.createIntent(activity, activity.remainTimes - 1).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP));
                            }
                            AppUtil.finishActivity(activity);
                            break;
                        case MSG_EXIST_TO_TAB://被举报强制退出到，匹配tab界面
                            RxBus.getInstance().post(0, Constants.POST_TAG_GO_MAIN_TAB);
                            break;
                    }
                }
            }
        }
    }

    private void sendChat() {
        if (webSocket != null) {
            JSONObject jsonObject = new JSONObject();
            try {
                Editable editable = getDataBinding().editInput.getText();
                String text = null;
                if (editable != null) {
                    text = editable.toString();
                }
                if (TextUtils.equals(text, mPreText)) {
                    if (mHandler != null) {
                        mHandler.sendEmptyMessageDelayed(MSG_SEND_INPUT, 1000);
                    }
                    return;
                }
                jsonObject.put("roomId", getViewModel().getRoomId());
                jsonObject.put("text", text);
                jsonObject.put("event", SEND_MESSAGE);
                boolean success = webSocket.send(jsonObject.toString());
                if (success) {
                    mPreText = text;
                }
                if (mHandler != null) {
                    mHandler.sendEmptyMessageDelayed(MSG_SEND_INPUT, 1000);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void leaveRoom() {
        if (webSocket != null) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("roomId", getViewModel().getRoomId());
                jsonObject.put("event", LEAVE_ROOM);
                webSocket.send(jsonObject.toString());
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void showAvatar() {
        if (webSocket != null) {
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("roomId", getViewModel().getRoomId());
                jsonObject.put("event", SHOW_AVATAR);
                boolean hasSend = webSocket.send(jsonObject.toString());
                if (hasSend) {
                    Drawable drawable = getResources().getDrawable(R.mipmap.matching_ic_temporary_agreed, null);
                    drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
                    getDataBinding().tvShowAvatar.setCompoundDrawables(null, drawable, null, null);
                    getDataBinding().tvShowAvatar.setEnabled(false);
                    getDataBinding().tvShowAvatar.setText(R.string.matching_temporary_has_show_avatar);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void quit() {
        //TODO 确认是否有二次弹窗
        showQuitDialog();
    }

    @Override
    public void report() {
        MePageRouter.jumpToUserReportActivity(MatchingTemporaryChatActivity.this, getViewModel().getFriendId(),-1, UserReportSource.SOURCE_TEMPORARY_CHAT, getViewModel().getRoomId(), RequestCodeConstants.REQUEST_CODE_0);
    }

    @Override
    public void clearInput() {
        getDataBinding().editInput.setText("");
        sendChat();
    }

    private void sendHandlerMessage(int msgWhat, String content) {
        if (mHandler != null) {
            Log.e("XXXXX", "msgWhat = " + msgWhat + ", content = " + content);
            if (TextUtils.isEmpty(content)) {
                mHandler.sendEmptyMessage(msgWhat);
            } else {
                try {
                    JSONObject jsonObject = new JSONObject(content);
                    if (mHandler != null && TextUtils.equals(jsonObject.optString("roomId"), getViewModel().getRoomId())) {
                        Message message = new Message();
                        message.what = msgWhat;
                        message.obj = content;
                        mHandler.sendMessage(message);
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void showMakeFriendFailDialog() {
        if (quiteDialog != null && quiteDialog.isShowing()) {
            quiteDialog.dismiss();
        }
        if (successDialog != null && successDialog.isShowing()) {
            successDialog.dismiss();
        }
        if (failDialog == null) {
            CommonLayoutDialog.Builder builder = new CommonLayoutDialog.Builder(this)
                    .setLayoutId(R.layout.matching_dialog_empathy_make_friend_fail)
                    .add(R.id.tv_positive)
                    .setPadding(30, 30)
                    .setCancelable(false)
                    .setCanceledOnTouchOutside(false)
                    .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
                        @Override
                        public void onItemClick(Dialog dialog, View view) {
                            dialog.dismiss();
                            int id = view.getId();
                            if (id == R.id.tv_positive) {
                                AppUtil.startActivity(MatchingTemporaryChatActivity.this, EmpathyActivity.createIntent(MatchingTemporaryChatActivity.this, remainTimes - 1).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP));
                                AppUtil.finishActivity(MatchingTemporaryChatActivity.this);
                            }
                        }
                    });
            failDialog = builder.create();
        }
        failDialog.show();
    }

//    private void showFailDialog() {
//        CommonLayoutDialog.Builder builder = new CommonLayoutDialog.Builder(this)
//                .setLayoutId(R.layout.matching_dialog_empathy_match_fail)
//                .setDisplayTextById(R.id.tv_title, getResources().getString(R.string.matching_friend_has_quit))
//                .add(R.id.tv_positive)
//                .add(R.id.tv_negative)
//                .setPadding(33, 33)
//                .setCancelable(false)
//                .setCanceledOnTouchOutside(false)
//                .setOnItemClickListener(new CommonBaseDialog.OnItemClickListener() {
//                    @Override
//                    public void onItemClick(Dialog dialog, View view) {
//                        dialog.dismiss();
//                        int id = view.getId();
//                        if (id == R.id.tv_positive) {
//                            AppUtil.startActivity(MatchingTemporaryChatActivity.this, EmpathyActivity.createIntent(MatchingTemporaryChatActivity.this, remainTimes - 1));
//                            AppUtil.finishActivity(MatchingTemporaryChatActivity.this);
//                        } else if (id == R.id.tv_negative) {//退出返回到主界面，切换到匹配tab
//                            RxBus.getInstance().post(0, Constants.POST_TAG_GO_MAIN_TAB);
//                        }
//                    }
//                });
//        builder.create().show();
//    }

    private void showQuitDialog() {
        if (quiteDialog == null) {
            CommonSystemCenterDialog.Builder builder = new CommonSystemCenterDialog.Builder(this)
                    .setResId(R.drawable.matching_ic_empathy_chat_exist)
                    .setTitle(getString(R.string.matching_sure_quit))
                    .setContent(getString(R.string.matching_temporary_chat_quit_assist))
                    .setPositiveText(getString(R.string.matching_not_quit))
                    .setNegativeText(getString(R.string.common_exist))
                    .setButtonClickListener(new CommonSystemCenterDialog.OnClickListener() {
                        @Override
                        public void onPositiveClick(Dialog dialog, View view) {

                        }

                        @Override
                        public void onNegativeClick(Dialog dialog, View view) {
                            AppUtil.startActivity(MatchingTemporaryChatActivity.this, EmpathyActivity.createIntent(MatchingTemporaryChatActivity.this, remainTimes - 1).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP));
                            AppUtil.finishActivity(MatchingTemporaryChatActivity.this);
                        }
                    });
            quiteDialog = builder.create();
        }
        quiteDialog.show();
    }

    private class TemporaryChatWebSocketListener extends WebSocketListener {
        @Override
        public void onOpen(@NonNull WebSocket webSocket, @NonNull Response response) {
            super.onOpen(webSocket, response);
            Log.e("XXXXX", "onOpen = ");
            sendChat();
            sendHandlerMessage(MSG_ONLINE, "");
        }

        @Override
        public void onMessage(@NonNull WebSocket webSocket, @NonNull String text) {
            super.onMessage(webSocket, text);
            Log.e("XXXXX", "text = " + text);
            try {
                JSONObject object = new JSONObject(text);
                int what = -1;
                switch (object.optString("event")) {
                    case USER_INFO:
                        what = MSG_USER_INFO_CHANGE;
                        break;
                    case ALERT:
                        what = MSG_ALERT;
                        break;
                    case SEND_MESSAGE:
                        what = MSG_SEND_MESSAGE;
                        break;
                    case DISCONNECT:
                        what = MSG_DISCONNECT;
                        break;

                }
                if (what >= 0) {
                    sendHandlerMessage(what, text);
                }
            } catch (Exception e) {

            }
        }

        @Override
        public void onClosed(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
            super.onClosed(webSocket, code, reason);
            Log.e("XXXXX", "onClosed = " + reason);
        }

        @Override
        public void onClosing(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
            super.onClosing(webSocket, code, reason);
            Log.e("XXXXX", "onClosing = " + reason);
            sendHandlerMessage(MSG_OFFLINE, "");
            webSocket.close(3002, "close");
            if (code == 3511) {
                sendHandlerMessage(MSG_EXIST_TO_SEARCH, "");
            }
            if (code == 3512) {
                sendHandlerMessage(MSG_EXIST_TO_TAB, "");
            }
        }

        @Override
        public void onFailure(@NonNull WebSocket webSocket, @NonNull Throwable t, @Nullable Response response) {
            super.onFailure(webSocket, t, response);
            Log.e("XXXXX", "onFailure = " + t.toString());
            sendHandlerMessage(MSG_OFFLINE, "");
        }
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case RequestCodeConstants.REQUEST_CODE_0:
                if (data != null) {
                    String id = data.getStringExtra(BundleConstants.BUNDLE_REPORT_RESOURCE_ID);
                    if (!TextUtils.isEmpty(id)) {
                        Intent intent = EmpathyActivity.createIntent(this, remainTimes - 1);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        AppUtil.startActivity(this, intent);
                        AppUtil.finishActivity(this);
                        T.ss(R.string.matching_report_success_quit_chat);
                    }
                }
        }
    }
}