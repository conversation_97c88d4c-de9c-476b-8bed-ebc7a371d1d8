package com.kanzhun.marry.matching.fragment.interact.perform

import androidx.lifecycle.LifecycleOwner
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.marry.matching.fragment.interact.ILikeListFragment
import com.kanzhun.common.kotlin.ui.onClick

/**
 * 我喜欢的列表头部的逻辑处理
 */
class InteractILikeHeaderPerformance(val fragment: ILikeListFragment):AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        //v2.1.1 新手引导需求：互动（我喜欢的）增加曝光提示文案
        fragment.mViewModel.showNoviceTaskEvent.observe(owner){
            if(it.blockInfo?.block == true){
                fragment.mBinding.stvNoviceTask.text = getText(it.blockInfo!!)
                if(getText(it.blockInfo!!).isEmpty()){
                    fragment.mBinding.idTipBar.gone()
                }else{
                    fragment.mBinding.idTipBar.visible()
                }
                fragment.mBinding.idWarnBtn.onClick { v->
                    if(it.blockInfo!!.block){
                        if(it.blockInfo!!.faceCertBlock){
                            MePageRouter.jumpToCertificationActivity(fragment.requireActivity(), CertificationIdentifySource.F2,it.blockInfo!!)
                        }else if(it.blockInfo!!.avatarCertBlock ){
                            MePageRouter.jumpToMeAvatarAuthActivity(fragment.requireActivity(), pageSource = PageSource.CHILD_F1_RECOMMEND_TOP_GUIDE)
                        }else if(it.blockInfo!!.avatarCertStatus == 2){
                            MePageRouter.jumpToMeAvatarAuthActivity(fragment.requireActivity(), pageSource = PageSource.CHILD_F1_RECOMMEND_TOP_GUIDE)
                        }
                    }
                }
                if(it.blockInfo!!.faceCertBlock){
                    fragment.mBinding.idWarnBtn.visible()
                } else if(it.blockInfo!!.avatarCertBlock){
                    fragment.mBinding.idWarnBtn.visible()
                } else if (it.blockInfo!!.avatarCertStatus == 1){
                    fragment.mBinding.idWarnBtn.gone()
                } else if(it.blockInfo!!.avatarCertStatus == 2){
                    fragment.mBinding.idWarnBtn.visible()
                }
            }else{
                fragment.mBinding.idTipBar.gone()
            }
        }
    }

    fun getText(blockInfo: UserGuideBlockInfoBean):String{
        if(blockInfo.faceCertBlock){
            return "暂未完成实名认证及头像认证，认证后对方才能收到你的喜欢"
        }else if(blockInfo.avatarCertBlock){
            return "暂未完成头像认证，认证后你的喜欢才能被Ta收到"
        }else if(blockInfo.avatarCertStatus == 1){
            return "头像认证审核中，通过后你的喜欢才能被Ta收到"
        }else if(blockInfo.avatarCertStatus == 2){
            return "暂未完成头像认证，认证后你的喜欢才能被Ta收到"
        }
        return ""
    }
}