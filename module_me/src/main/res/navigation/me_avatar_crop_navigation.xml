<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/me_avatar_crop_navigation"
    app:startDestination="@+id/avatarCropFragment">

    <fragment
        android:id="@+id/avatarCropFragment"
        android:name="com.kanzhun.marry.me.info.fragment.MeAvatarUploadCropFragment"
        android:label="fragment_avatar_crop"
        tools:layout="@layout/me_fragment_me_avatar_upload_crop">
        <action
            android:id="@+id/action_avatarCropFragment_to_avatarCircleCropFragment"
            app:destination="@id/avatarCircleCropFragment"
            app:enterAnim="@anim/nav_enter"
            app:exitAnim="@anim/nav_exit"
            app:popEnterAnim="@anim/nav_pop_enter"
            app:popExitAnim="@anim/nav_pop_exit" />

    </fragment>

    <fragment
        android:id="@+id/avatarCircleCropFragment"
        android:name="com.kanzhun.marry.me.info.fragment.MeAvatarUploadCircleCropFragment"
        android:label="fragment_avatar_circle_crop"
        tools:layout="@layout/me_fragment_me_avatar_upload_circle_crop" />


</navigation>