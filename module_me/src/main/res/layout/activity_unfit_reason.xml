<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".setting.UnfitReasonActivity">

    <data>
        <import type="com.kanzhun.marry.me.api.model.FeedbackReasonModel" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.UnfitReasonViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.UnfitReasonCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_F5F5F5">
        <include
            android:id="@+id/title_bar"
            layout="@layout/common_title_bar"
            app:layout_constraintTop_toTopOf="parent"
            app:callback="@{callback}" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            android:layout_marginStart="20dp"
            android:layout_marginTop="24dp"
            android:fontFamily="sans-serif-medium"
            android:text="@string/me_unfit_reason"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28" />

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="4dp"
            android:text="@string/me_unfit_reason_subtitle"
            android:textColor="@color/common_color_797979"
            android:textSize="@dimen/common_text_sp_14" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_subtitle"
            app:layout_constraintBottom_toTopOf="@+id/btn_submit"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="16dp"
            android:scrollbars="none">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerview"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="20dp"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    tools:itemCount="4"
                    tools:listitem="@layout/me_item_feedback_reason"/>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_edit"
                    android:layout_width="match_parent"
                    android:layout_height="168dp"
                    android:layout_marginLeft="20dp"
                    android:layout_marginRight="20dp"
                    app:layout_constraintTop_toBottomOf="@+id/tv_content"
                    android:paddingTop="12dp"
                    android:background="@drawable/common_bg_conor_12_color_white"
                    app:visibleInvisible="@{viewModel.selectedReasonType == FeedbackReasonModel.FEEDBACK_OTHER_REASON_TYPE}"
                    tools:visibility="visible">
                    <EditText
                        android:id="@+id/et_text"
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_marginLeft="12dp"
                        android:layout_marginRight="12dp"
                        android:paddingBottom="32dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:gravity="left|top"
                        android:hint="@string/me_unfit_reason_subtitle"
                        android:text="@={viewModel.editContent}"
                        android:textColor="@color/common_color_191919"
                        android:textColorHint="@color/common_color_B2B2B2"
                        android:textSize="@dimen/common_text_sp_14"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:gravity="right"
                        android:text="@{String.valueOf(viewModel.editContent.length())}"
                        android:textColor="@{viewModel.editContent.length()>0 ? @color/common_color_191919 : @color/common_color_B2B2B2}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_total_count"
                        tools:text="0" />

                    <TextView
                        android:id="@+id/tv_total_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:layout_marginRight="12dp"
                        android:layout_marginBottom="12dp"
                        android:gravity="right"
                        android:text="/500"
                        android:textColor="@color/common_color_B2B2B2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_submit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_marginBottom="20dp"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@string/common_submit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:enabled="@{viewModel.selectedReasonType > 0 &amp;&amp; (viewModel.selectedReasonType != FeedbackReasonModel.FEEDBACK_OTHER_REASON_TYPE || viewModel.editContent.trim().length() >= 5)}"
            app:greyDisabledStyle="@{viewModel.selectedReasonType > 0 &amp;&amp; (viewModel.selectedReasonType != FeedbackReasonModel.FEEDBACK_OTHER_REASON_TYPE || viewModel.editContent.trim().length() >= 5)}"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>