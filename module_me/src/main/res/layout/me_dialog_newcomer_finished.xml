<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/cl_task_finished"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="20dp"
        android:paddingBottom="32dp"
        android:background="@color/common_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:qmui_radius="10dp"
        android:clickable="true">
        <ImageView
            android:id="@+id/iv_title_bg"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:src="@mipmap/me_icon_newcomer_finished_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="@+id/iv_title_bg"
            app:layout_constraintRight_toRightOf="@+id/iv_title_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_title_bg"
            android:textSize="@dimen/common_text_sp_24"
            android:textColor="@color/common_color_0D0D1D"
            android:text="@string/me_newcomer_task_finish_congratulations"
            android:textStyle="bold"/>

        <TextView
            android:id="@+id/tv_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/iv_title_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="7dp"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_0D0D1D_50"
            android:text="@string/me_newcomer_task_finish_subtitle" />

        <com.qmuiteam.qmui.layout.QMUILinearLayout
            android:id="@+id/ll_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_subtitle"
            android:layout_marginTop="10dp"
            android:background="@color/common_color_F2F4FB"
            android:paddingTop="14dp"
            android:paddingBottom="24dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            app:qmui_radius="10dp"
            android:orientation="vertical">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_line"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="@+id/cl_btn"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_btn"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@+id/cl_btn"
                    android:layout_marginLeft="13dp"
                    android:layout_marginRight="-1dp">
                    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                        android:id="@+id/btn_line_gray"
                        android:layout_width="match_parent"
                        android:layout_height="4dp"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:qmui_backgroundColor="@color/me_color_DBE8FD"
                        app:qmui_radius="2dp" />

                    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                        android:id="@+id/btn_line_dark"
                        android:layout_width="0dp"
                        android:layout_height="4dp"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:qmui_backgroundColor="@color/common_color_7171FF"
                        app:qmui_radius="2dp"
                        android:visibility="gone"/>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:layout_marginRight="13dp">
                    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                        android:id="@+id/btn_gray"
                        android:layout_width="69dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:paddingTop="5dp"
                        android:paddingBottom="5dp"
                        android:paddingLeft="10dp"
                        android:paddingRight="10dp"
                        android:text="@string/me_newcomer_unlock_matching"
                        android:textColor="@color/common_color_7171FF"
                        android:textSize="@dimen/common_text_sp_12"
                        app:qmui_backgroundColor="@color/me_color_DBE8FD"
                        app:qmui_radius="18dp" />

                    <FrameLayout
                        android:id="@+id/fl_btn_dark"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:visibility="gone">
                        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                            android:id="@+id/btn_Dark"
                            android:layout_width="69dp"
                            android:layout_height="wrap_content"
                            android:paddingTop="5dp"
                            android:paddingBottom="5dp"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp"
                            android:text="@string/me_newcomer_unlock_matching"
                            android:textColor="@color/common_white"
                            android:textSize="@dimen/common_text_sp_12"
                            app:qmui_backgroundColor="@color/common_color_7171FF"
                            app:qmui_radius="18dp" />
                    </FrameLayout>
                </androidx.constraintlayout.widget.ConstraintLayout>

                <ImageView
                    android:id="@+id/iv_arrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="@+id/cl_btn"
                    app:layout_constraintBottom_toBottomOf="@+id/cl_btn"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:layout_marginLeft="5dp"
                    android:src="@mipmap/me_icon_newcomer_progressbar_arrow"
                    android:visibility="gone"
                    tools:visibility="visible"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.kanzhun.marry.me.entrance.views.MeFragmentNewcomerItem
                android:id="@+id/item_certification"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="14dp"
                android:layout_marginTop="5dp"
                app:title="@string/me_certification"/>

            <com.kanzhun.marry.me.entrance.views.MeFragmentNewcomerItem
                android:id="@+id/item_profile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="14dp"
                app:title="@string/me_info_perfect_text"/>

            <com.kanzhun.marry.me.entrance.views.MeFragmentNewcomerItem
                android:id="@+id/item_character"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="14dp"
                app:title="@string/me_newcomer_character"/>

            <com.kanzhun.marry.me.entrance.views.MeFragmentNewcomerItem
                android:id="@+id/item_education"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="14dp"
                app:title="@string/me_school_approve_text_school"/>
        </com.qmuiteam.qmui.layout.QMUILinearLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_goto_matching"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/ll_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="50dp"
            android:paddingRight="50dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="@string/me_newcomer_goto_matching"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:qmui_backgroundColor="@color/common_color_7171FF"
            app:qmui_radius="24dp" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
