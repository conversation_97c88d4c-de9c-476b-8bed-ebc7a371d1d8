<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_bg_corner_20_top_2_color_white"
    android:orientation="vertical"
    android:paddingBottom="20dp"
    app:qmui_radiusTopLeft="32dp"
    app:qmui_radiusTopRight="32dp"
    tools:ignore="SpUsage">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:textColor="@color/common_color_191919"
        android:textSize="18dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="title" />

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="22dp"
        android:gravity="center"
        android:text="@string/common_cancel"
        android:textColor="@color/common_color_7F7F7F"
        android:textSize="16dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sure"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:layout_marginStart="18dp"
        android:layout_marginTop="22dp"
        android:gravity="center"
        android:text="@string/common_sure"
        android:textColor="@color/common_color_191919"
        android:textSize="16dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.compose.ui.platform.ComposeView
        android:id="@+id/compose_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_sure"
        tools:composableName="com.kanzhun.marry.me.dialog.UserLivingPlaceEditDialogKt.PreviewLocationView" />

    <com.kanzhun.common.views.wheel.pick.LinkageView
        android:id="@+id/idLinkAge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="18dp"
        android:layout_marginRight="18dp"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/compose_view"
        tools:background="@color/common_black_70"
        tools:layout_height="128dp" />

</com.qmuiteam.qmui.layout.QMUIConstraintLayout>
