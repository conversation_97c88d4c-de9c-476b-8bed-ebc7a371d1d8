<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/common_color_F0F0F0">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:background="@color/common_white"
        android:paddingTop="16dp"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="10dp"
        app:qmui_radius="12dp">

        <ImageView
            android:visibility="gone"
            android:id="@+id/idIcon"
            android:layout_width="28dp"
            android:layout_height="29dp"
            android:src="@drawable/me_ic_verity"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            app:layout_goneMarginTop="0dp"
            android:layout_marginTop="12dp"
            android:layout_height="wrap_content"
            android:text="我的认证"
            android:textColor="@color/common_color_191919"
            android:textSize="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idIcon" />

        <com.kanzhun.foundation.views.flowlayout.AutoFlowLayout
            android:id="@+id/idAutoFlowLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle" />

        <TextView
            android:id="@+id/idTodoText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="4项待认证"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toLeftOf="@+id/idIconRight"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

        <TextView
            android:id="@+id/idTitleSub"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="为保障交友真实性，请进行信息认证"
            android:paddingBottom="10dp"
            android:textColor="@color/common_color_858585"
            android:textSize="14dp"
            app:layout_constraintLeft_toLeftOf="@+id/idTitle"
            app:layout_constraintTop_toBottomOf="@+id/idTitle" />

        <ImageView
            android:id="@+id/idIconRight"
            android:layout_width="20dp"
            android:layout_height="21dp"
            android:src="@drawable/me_my_info_ic_right"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/idTitle" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>