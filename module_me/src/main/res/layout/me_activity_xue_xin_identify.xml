<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.XueXinIdentifyVIewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.XueXinIdentifyCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_layout_only_left_title_bar"
            app:callback="@{callback}"
            app:layout_collapseMode="pin"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/app_bar_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:elevation="0dp">

                <com.google.android.material.appbar.CollapsingToolbarLayout
                    android:id="@+id/collapsing_toolbar_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed">

                    <androidx.appcompat.widget.Toolbar
                        android:id="@+id/tb_toolbar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="top"
                        app:contentInsetLeft="0dp"
                        app:contentInsetStart="0dp"
                        app:layout_collapseMode="pin">

                    </androidx.appcompat.widget.Toolbar>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/common_white">

                        <com.kanzhun.common.views.textview.BoldTextView
                            android:id="@+id/tv_xue_xin_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="18dp"
                            android:layout_marginTop="30dp"
                            android:text="@string/me_xue_xin_identify"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_28"
                            android:textStyle="bold"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tv_xue_xin_assist"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="18dp"
                            android:layout_marginTop="4dp"
                            android:text="@string/me_xue_xin_assist"
                            android:textColor="@color/common_color_7F7F7F"
                            android:textSize="@dimen/common_text_sp_14"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_xue_xin_title" />


                    </androidx.constraintlayout.widget.ConstraintLayout>



                </com.google.android.material.appbar.CollapsingToolbarLayout>

            </com.google.android.material.appbar.AppBarLayout>


            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <com.qmuiteam.qmui.layout.QMUILinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingBottom="171dp"
                    app:qmui_radius="12dp">

                    <com.qmuiteam.qmui.layout.QMUILinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="20dp"
                        android:background="@color/common_color_F5F5F5"
                        app:qmui_radius="20dp"
                        android:orientation="vertical"
                        >

                        <TextView
                            android:textStyle="bold"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="20dp"
                            android:text="第一步"
                            android:textColor="@color/common_color_191919"
                            android:textSize="18dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="20dp"
                            android:text="@string/me_xuexin_code_step_1"
                            android:textColor="@color/common_color_4C4C4C"
                            android:textSize="14dp" />

                        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                            android:id="@+id/idCopy"
                            android:layout_width="match_parent"
                            android:layout_height="46dp"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="20dp"
                            android:gravity="center"
                            android:paddingHorizontal="44dp"
                            android:text="打开学信网"
                            android:background="@color/common_color_0090FF"
                            android:textColor="@color/common_white"
                            app:qmui_backgroundColor="@color/common_color_0090FF"
                            app:qmui_radius="23dp" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="20dp"
                            android:layout_marginTop="17dp"
                            android:layout_marginBottom="13dp">

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="0dp"
                                android:scaleType="fitXY"
                                android:src="@mipmap/me_img_bg_xue_xin_step_1"
                                app:layout_constraintBottom_toBottomOf="parent"
                                app:layout_constraintDimensionRatio="614:430"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.qmuiteam.qmui.layout.QMUILinearLayout>

                    <com.qmuiteam.qmui.layout.QMUILinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical"
                        android:background="@color/common_color_F5F5F5"
                        app:qmui_radius="20dp">


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="20dp"
                            android:text="第二步"
                            android:textStyle="bold"
                            android:textColor="@color/common_color_191919"
                            android:textSize="18dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="20dp"
                            android:text="@string/me_xuexin_code_step_2"
                            android:textColor="@color/common_color_4C4C4C"
                            android:textSize="14dp" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginRight="20dp"
                            android:layout_marginBottom="10dp">

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="0dp"
                                android:scaleType="fitXY"
                                android:src="@mipmap/me_img_bg_xue_xin_step_2"
                                app:layout_constraintDimensionRatio="614:376"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.qmuiteam.qmui.layout.QMUILinearLayout>

                    <com.qmuiteam.qmui.layout.QMUILinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical"
                        android:background="@color/common_color_F5F5F5"
                        app:qmui_radius="20dp">


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="20dp"
                            android:text="第三步"
                            android:textStyle="bold"
                            android:textColor="@color/common_color_191919"
                            android:textSize="18dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="20dp"
                            android:text="@string/me_xuexin_code_step_3"
                            android:textColor="@color/common_color_4C4C4C"
                            android:textSize="14dp" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginRight="20dp"
                            android:layout_marginBottom="8dp">

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="0dp"

                                android:scaleType="fitXY"
                                android:src="@mipmap/me_img_bg_xue_xin_step_3"
                                app:layout_constraintDimensionRatio="614:304"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.qmuiteam.qmui.layout.QMUILinearLayout>

                    <com.qmuiteam.qmui.layout.QMUILinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:layout_marginTop="20dp"
                        android:orientation="vertical"
                        android:background="@color/common_color_F5F5F5"
                        app:qmui_radius="20dp"
                        >


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="16dp"
                            android:layout_marginRight="20dp"
                            android:text="第四步"
                            android:textStyle="bold"
                            android:textColor="@color/common_color_191919"
                            android:textSize="18dp" />

                        <TextView
                            android:id="@+id/idStep3Text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="4dp"
                            android:layout_marginRight="20dp"
                            android:text="@string/me_xuexin_code_step_4"
                            android:textColor="@color/common_color_4C4C4C"
                            android:textSize="14dp" />

                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginTop="20dp"
                            android:layout_marginRight="20dp"
                            android:layout_marginBottom="19dp">

                            <ImageView
                                android:layout_width="0dp"
                                android:layout_height="0dp"
                                android:scaleType="fitXY"
                                android:src="@mipmap/me_img_bg_xue_xin_step_4"
                                app:layout_constraintDimensionRatio="614:292"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintStart_toStartOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </androidx.constraintlayout.widget.ConstraintLayout>

                    </com.qmuiteam.qmui.layout.QMUILinearLayout>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="24dp"
                        android:layout_marginRight="20dp"
                        android:text="如您的毕业院校是港澳台或海外学校，请选择“港澳台或海外学历认证”提交认证"
                        android:textColor="@color/common_color_191919"
                        android:textSize="14dp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginRight="20dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="如有"
                            android:textColor="@color/common_color_191919"
                            android:textSize="14dp" />

                        <TextView
                            android:id="@+id/idHelpLink"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="其他问题，可前往反馈"
                            android:textColor="@color/common_color_191919"
                            android:textSize="14dp"
                            android:textStyle="bold" />
                    </LinearLayout>


                </com.qmuiteam.qmui.layout.QMUILinearLayout>


            </androidx.core.widget.NestedScrollView>


        </androidx.coordinatorlayout.widget.CoordinatorLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="171dp"
            android:background="@color/common_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">


            <include
                android:id="@+id/layout_xue_xin_edit_option_code"
                layout="@layout/me_layout_edit_option"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="26dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toTopOf="@+id/btn_start_certification"
                app:option="@{viewModel.code}" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_start_certification"
                style="@style/button_large_next_page_style"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="20dp"
                android:enabled="false"
                android:onClick="@{(view)->callback.clickSubmit(view)}"
                android:text="@string/me_commit_identify"
                android:layout_marginBottom="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                 />

        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>