<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".info.ABImpressionActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.ABImpressionViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.ABImpressionCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">
        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <FrameLayout
            android:id="@+id/fragment_container_edit"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>



</layout>