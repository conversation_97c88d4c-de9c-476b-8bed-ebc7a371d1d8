<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:visibility="gone">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginTop="-18dp"
        android:paddingBottom="40dp"
        android:background="@color/common_white"
        app:qmui_radius="12dp">

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tvUserName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="10dp"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="老司机带带我啊带带我"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.airbnb.lottie.LottieAnimationView
            app:lottie_autoPlay="true"
            app:lottie_loop="false"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:id="@+id/iv_icon"
            android:layout_marginTop="40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvUserName" />

        <TextView
            android:id="@+id/tv_reason"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="16dp"
            android:gravity="center_horizontal"
            android:textSize="15dp"
            android:textColor="@color/common_color_7F7F7F"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_icon"
            tools:text="common_long_placeholder" />


    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</LinearLayout>
