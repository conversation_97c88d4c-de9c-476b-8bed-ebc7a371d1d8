<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingBottom="31dp"
    android:layout_height="wrap_content">

    <com.kanzhun.common.views.image.OImageView
        android:layout_marginLeft="20dp"
        android:id="@+id/idMeTaskIcon"
        android:layout_width="40dp"
        android:layout_marginTop="3dp"
        android:layout_height="40dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
         />

    <TextView
        android:id="@+id/idTaskTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:text="头像认证"
        android:textColor="@color/common_color_292929"
        android:textSize="16dp"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@+id/idMeTaskIcon"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/idTaskContent"
        android:layout_width="0dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:layout_marginRight="4dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="文案示意文案示意文案示意"
        app:layout_constraintRight_toLeftOf="@+id/idTaskBtn"
        android:textColor="@color/common_color_5E5E5E"
        android:textSize="14dp"
        app:layout_constraintLeft_toLeftOf="@+id/idTaskTitle"
        app:layout_constraintTop_toBottomOf="@+id/idTaskTitle" />

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/idTaskBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:background="@color/common_color_292929"
        android:paddingHorizontal="12dp"
        android:paddingVertical="5dp"
        android:text="去完成"
        tools:visibility="gone"
        android:textColor="@color/common_white"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/idMeTaskIcon"
        app:layout_constraintBottom_toBottomOf="@+id/idMeTaskIcon"
        app:qmui_backgroundColor="@color/common_color_292929"
        app:qmui_radius="16dp" />

    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
        android:id="@+id/idTaskBtn2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="16dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="3dp"
        android:text="审核中"
        android:textSize="12dp"
        android:textColor="@color/common_color_5E5E5E"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@+id/idMeTaskIcon"
        app:layout_constraintBottom_toBottomOf="@+id/idMeTaskIcon"
        app:qmui_backgroundColor="@color/common_color_EBEBEB"
        app:qmui_radius="6dp" />



</androidx.constraintlayout.widget.ConstraintLayout>