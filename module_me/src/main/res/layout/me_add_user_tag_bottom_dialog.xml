<?xml version="1.0" encoding="utf-8"?>
<com.lihang.ShadowLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:clickable="false"
    app:hl_cornerRadius_leftTop="32dp"
    app:hl_cornerRadius_rightTop="32dp"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@mipmap/common_bg_theme_1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:textStyle="bold"
            android:gravity="center_vertical"
            android:id="@+id/tvParentName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="20dp"
            android:drawableStart="@drawable/me_ic_user_tag"
            android:drawablePadding="8dp"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="兴趣爱好·旅游" />

        <ImageView
            android:id="@+id/ivClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:src="@drawable/common_black_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldSuperTextView
            android:id="@+id/tvTagName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="58dp"
            android:paddingHorizontal="24dp"
            android:paddingVertical="12dp"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_20"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvParentName"
            app:stv_corner="30dp"
            app:stv_solid="@color/common_color_F5F5F5"
            tools:text="说走就走的旅行" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/btn_submit"
            android:layout_width="match_parent"
            android:layout_height="46dp"
            android:layout_marginHorizontal="20dp"
            android:layout_marginTop="57dp"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:textColor="@color/common_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTagName"
            app:stv_corner="25dp"
            app:stv_solid="@color/common_color_191919"
            android:text="添加为我的标签" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.lihang.ShadowLayout>