<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.facebook.drawee.view.SimpleDraweeView
        android:id="@+id/idSimpleDraweeView"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintDimensionRatio="1:1"
        android:layout_width="0dp"
        app:roundedCornerRadius="12dp"
        android:layout_height="0dp"/>
    
    <TextView
        android:id="@+id/idText"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintStart_toStartOf="@+id/idSimpleDraweeView"
        app:layout_constraintEnd_toEndOf="@+id/idSimpleDraweeView"
        app:layout_constraintTop_toTopOf="@+id/idSimpleDraweeView"
        app:layout_constraintBottom_toBottomOf="@+id/idSimpleDraweeView"
        android:text="+6"
        android:textSize="24dp"
        android:textColor="@color/color_white"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>