<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="com.kanzhun.foundation.utils.StringUtil" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.api.model.ProfileInfoModel.BaseInfo" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallback" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:background="@drawable/common_bg_conor_12_color_white"
        android:paddingLeft="12dp"
        android:paddingRight="12dp"
        android:paddingBottom="8dp">

        <com.kanzhun.common.views.OTextView
            android:id="@id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/me_privacy"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_privacy_notice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:onClick="@{(view)->callback.clickPrivacyNotice(view)}"
            android:src="@drawable/me_ic_privacy"
            app:layout_constraintBottom_toBottomOf="@id/tv_title"
            app:layout_constraintLeft_toRightOf="@id/tv_title"
            app:layout_constraintTop_toTopOf="@+id/tv_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_revenue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">

            <TextView
                android:id="@+id/tv_revenue_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:layout_marginTop="16dp"
                android:drawableLeft="@drawable/me_ic_revenue"
                android:drawablePadding="6dp"
                android:gravity="center_vertical"
                android:text="@string/common_revenue"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_revenue"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="4dp"
                android:gravity="right|center_vertical"
                android:hint="@string/me_go_select"
                android:singleLine="true"
                android:text="@{StringUtil.getAnnualIncome(bean.annualIncome)}"
                android:textColor="@color/common_color_707070"
                android:textColorHint="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toBottomOf="@+id/tv_revenue_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_revenue_title"
                app:layout_constraintRight_toLeftOf="@+id/iv_arrow_revenue"
                app:layout_constraintTop_toTopOf="@+id/tv_revenue_title" />

            <ImageView
                android:id="@+id/iv_arrow_revenue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/common_ic_gray_right_arrow"
                app:layout_constraintBottom_toBottomOf="@+id/tv_revenue_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_revenue"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_revenue_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_house_car"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:onClick="@{()->callback.clickHouseCar()}"
            app:layout_constraintTop_toBottomOf="@+id/cl_revenue">

            <TextView
                android:id="@+id/tv_house_car_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:drawableLeft="@drawable/me_ic_house_car"
                android:drawablePadding="6dp"
                android:gravity="center_vertical"
                android:text="@string/me_house_car"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_house_car"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="4dp"
                android:gravity="right|center_vertical"
                android:hint="@string/me_go_select"
                android:singleLine="true"
                android:textColor="@color/common_color_707070"
                android:textColorHint="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                app:carHold="@{bean.carHold}"
                app:houseHold="@{bean.houseHold}"
                app:layout_constraintBottom_toBottomOf="@+id/tv_house_car_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_house_car_title"
                app:layout_constraintRight_toLeftOf="@+id/iv_arrow_house_car"
                app:layout_constraintTop_toTopOf="@+id/tv_house_car_title" />

            <ImageView
                android:id="@+id/iv_arrow_house_car"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/common_ic_gray_right_arrow"
                app:layout_constraintBottom_toBottomOf="@+id/tv_house_car_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_house_car"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_house_car_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_residence"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/cl_house_car">

            <TextView
                android:id="@+id/tv_permanent_residence_title"
                android:layout_width="wrap_content"
                android:layout_height="54dp"
                android:drawableLeft="@drawable/me_ic_permanent_residence"
                android:drawablePadding="6dp"
                android:gravity="center_vertical"
                android:text="@string/me_permanent_residence"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_permanent_residence"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginLeft="5dp"
                android:layout_marginRight="4dp"
                android:gravity="right|center_vertical"
                android:hint="@string/me_go_select"
                android:singleLine="true"
                android:text="@{StringUtil.getAddSeparation(bean.hukouLevel1,bean.hukouLevel2,StringUtil.COMMON_INFO_DOT)}"
                android:textColor="@color/common_color_707070"
                android:textColorHint="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toBottomOf="@+id/tv_permanent_residence_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_permanent_residence_title"
                app:layout_constraintRight_toLeftOf="@+id/iv_arrow_permanent_residence"
                app:layout_constraintTop_toTopOf="@+id/tv_permanent_residence_title" />

            <ImageView
                android:id="@+id/iv_arrow_permanent_residence"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/common_ic_gray_right_arrow"
                app:layout_constraintBottom_toBottomOf="@+id/tv_permanent_residence_title"
                app:layout_constraintLeft_toRightOf="@+id/tv_permanent_residence"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_permanent_residence_title" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>