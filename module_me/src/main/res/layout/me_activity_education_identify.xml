<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.EducationIdentifyViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.EducationIdentifyCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_layout_only_left_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:visibility="gone"
            android:id="@+id/idFragmentTop"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/cl_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_color_FFD209"
            app:qmui_radius="12dp"
            android:layout_marginHorizontal="12dp"
            android:paddingHorizontal="12dp"
            android:paddingVertical="8dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/idIcon"
                android:src="@mipmap/me_icon_error_red"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="24dp"
                android:layout_height="24dp"/>

            <TextView
                android:layout_marginLeft="8dp"
                android:id="@+id/tv_error_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textColor="@color/common_color_292929"
                android:textSize="13dp"
                app:layout_constraintLeft_toRightOf="@+id/idIcon"
                android:layout_marginRight="16dp"
                android:textStyle="bold"
                app:layout_constraintRight_toRightOf="parent"
                tools:text="1哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈哈" />
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tv_education_identify_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginTop="@dimen/app_layout_page_title_margin"
            android:textStyle="bold"
            android:text="学历信息"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cl_error" />

        <TextView
            android:id="@+id/tv_education_identify_assist"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginTop="@dimen/app_layout_page_hit_title_margin"
            android:text="填写您的学历信息，为您匹配同样优秀的人"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_education_identify_title" />

        <include
            android:id="@+id/layout_education_identify_option_school"
            layout="@layout/me_layout_education_identify_option"
            android:layout_width="match_parent"
            android:layout_height="83dp"
            android:layout_marginTop="@dimen/app_layout_page_content_margin"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_education_identify_assist"
            app:option="@{viewModel.school}" />

        <include
            android:id="@+id/layout_education_identify_option_education_background"
            layout="@layout/me_layout_education_identify_option"
            android:layout_width="match_parent"
            android:layout_height="83dp"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/layout_education_identify_option_school"
            app:option="@{viewModel.educationBackGround}" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/button_large_next_page_style"
            android:layout_marginHorizontal="@dimen/app_layout_page_left_padding"
            android:layout_marginBottom="18dp"
            android:enabled="false"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@string/me_next"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>