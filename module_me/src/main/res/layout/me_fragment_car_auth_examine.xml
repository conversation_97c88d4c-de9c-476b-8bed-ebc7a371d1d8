<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_white"
    android:orientation="vertical">

    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/common_title_height" />


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="20dp"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <com.airbnb.lottie.LottieAnimationView
            app:lottie_fileName="user_info_certing/data.json"
            app:lottie_imageAssetsFolder="user_info_certing/images"
            app:lottie_autoPlay="true"
            app:lottie_loop="false"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:layout_marginTop="37dp"
             />

        <com.kanzhun.common.views.textview.BoldTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:text="@string/me_school_approve_going"
            android:textColor="@color/common_color_1D1D1F"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center_horizontal"
            android:text="@string/me_identify_common"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_14" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
           >


            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ivCarAuthPositive"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginEnd="4dp"
                android:scaleType="centerInside"
                app:common_radius="8dp"
                app:layout_constraintDimensionRatio="h,16:9"
                app:layout_constraintEnd_toStartOf="@id/ivCarAuthNegative"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@mipmap/me_car_auth_example1" />

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ivCarAuthNegative"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="4dp"
                android:scaleType="centerInside"
                app:common_radius="8dp"
                app:layout_constraintDimensionRatio="h,16:9"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivCarAuthPositive"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@mipmap/me_car_auth_example2" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/common_black_button_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="36dp"
            android:text="@string/common_me_know" />

    </LinearLayout>


</LinearLayout>