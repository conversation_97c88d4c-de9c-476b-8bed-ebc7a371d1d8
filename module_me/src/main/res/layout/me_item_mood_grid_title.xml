<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!--    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton-->
    <!--        android:id="@+id/rbTitleBg"-->
    <!--        android:layout_width="0dp"-->
    <!--        android:layout_height="7dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"-->
    <!--        app:layout_constraintLeft_toLeftOf="@+id/spaceLeft"-->
    <!--        app:layout_constraintRight_toRightOf="@+id/spaceRight"-->
    <!--        app:qmui_backgroundColor="@drawable/common_gradient_ffa8ec_ffe9fb"-->
    <!--        app:qmui_radius="3.5dp" />-->

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="5dp"
        android:textColor="@color/common_color_292929"
        android:textSize="@dimen/common_text_sp_16"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="互动" />

    <!--    <View-->
    <!--        android:id="@+id/spaceLeft"-->
    <!--        android:layout_width="1dp"-->
    <!--        android:layout_height="1dp"-->
    <!--        android:layout_marginRight="1dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"-->
    <!--        app:layout_constraintRight_toLeftOf="@+id/tvTitle" />-->

    <!--    <View-->
    <!--        android:id="@+id/spaceRight"-->
    <!--        android:layout_width="1dp"-->
    <!--        android:layout_height="1dp"-->
    <!--        android:layout_marginLeft="1dp"-->
    <!--        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"-->
    <!--        app:layout_constraintLeft_toRightOf="@+id/tvTitle" />-->


</androidx.constraintlayout.widget.ConstraintLayout>