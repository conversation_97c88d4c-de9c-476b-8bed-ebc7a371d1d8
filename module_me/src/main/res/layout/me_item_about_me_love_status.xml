<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:paddingBottom="15dp"
    android:orientation="horizontal"
    android:layout_height="wrap_content">

    <com.kanzhun.common.views.image.OImageView
        android:id="@+id/idIcon"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_width="20dp"
        android:layout_weight="0"
        android:src="@drawable/me_ic_clock"
        app:layout_constraintTop_toTopOf="@+id/idTitle"
        app:layout_constraintBottom_toBottomOf="@+id/idTitle"
        android:layout_height="20dp"/>

    <TextView
        android:layout_marginLeft="6dp"
        tools:text="3-5年有结婚计5年有结婚计"
        app:layout_constraintTop_toTopOf="parent"
        android:maxLines="1"
        android:layout_weight="1"
        android:ellipsize="end"
        android:id="@+id/idTitle"
        android:textSize="15dp"
        android:textColor="@color/common_color_292929"
        app:layout_constraintLeft_toRightOf="@+id/idIcon"
        app:layout_constraintRight_toLeftOf="@+id/idSubText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        tools:text="（仅互相喜欢的人可见）"
        android:layout_weight="0"
        android:layout_marginLeft="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/idTitle"
        app:layout_constraintRight_toRightOf="parent"
        android:textColor="@color/common_color_B8B8B8"
        android:textSize="13dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:id="@+id/idSubText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</LinearLayout>
