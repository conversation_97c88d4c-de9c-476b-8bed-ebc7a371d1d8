<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.kanzhun.foundation.model.profile.OpenScreen" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallback" />

    </data>

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:background="@color/common_white"
        android:onClick="@{()->callback.clickGoTestResult(bean)}"
        app:qmui_radius="12dp">

        <ImageView
            android:id="@+id/iv_love_personality_default"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="12dp"
            android:src="@drawable/me_ic_love_personality"
            app:layout_constraintBottom_toBottomOf="@+id/tv_love_personality_default_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_love_personality_default_title"
            app:visibleGone="@{bean.status == 0}"
            tools:visibility="gone" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_love_personality_default_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="20dp"
            android:gravity="left"
            android:text="@string/me_my_love_nature"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toRightOf="@+id/iv_love_personality_default"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{bean.status == 0}"
            tools:visibility="gone" />

        <TextView
            android:id="@+id/tv_default_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="7dp"
            android:layout_marginBottom="20dp"
            android:text="@string/me_love_nature_text_desc"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/tv_love_personality_default_title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_love_personality_default_title"
            app:visibleGone="@{bean.status == 0}"
            tools:visibility="gone" />

        <ImageView
            android:id="@+id/iv_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="12dp"
            android:onClick="@{()->callback.clickAddOpenScreen()}"
            android:paddingLeft="12dp"
            android:paddingRight="12dp"
            android:scaleType="fitStart"
            android:src="@drawable/me_info_add"
            app:layout_constraintBottom_toBottomOf="@id/tv_default_assist"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_love_personality_default_title"
            app:visibleGone="@{bean.status == 0}"
            tools:visibility="gone" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ov_head"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:scaleType="centerCrop"
            app:common_placeholder="@drawable/common_bg_placeholder_translate"
            app:common_radius="12dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{bean.status != 0}" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_love_personality_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22dp"
            android:layout_marginTop="24dp"
            android:ellipsize="end"
            android:gravity="left"
            android:singleLine="true"
            android:text="@{bean.title}"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ov"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{bean.status != 0}"
            tools:text="@string/me_my_love_nature"
            tools:visibility="visible" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22dp"
            android:layout_marginTop="6dp"
            android:ellipsize="end"
            android:gravity="left"
            android:singleLine="true"
            android:text="@{bean.mbtiCode}"
            android:textColor="@color/common_color_B7B7B7"
            android:textSize="@dimen/common_text_sp_20"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ov"
            app:layout_constraintTop_toBottomOf="@+id/tv_love_personality_title"
            app:visibleGone="@{bean.status != 0}"
            tools:text="INTJ"
            tools:visibility="visible" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22dp"
            android:ellipsize="end"
            android:gravity="left"
            android:singleLine="true"
            android:text="@{bean.mbtiName}"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_34"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ov"
            app:layout_constraintTop_toBottomOf="@+id/tv_code"
            app:visibleGone="@{bean.status != 0}"
            tools:text="猫头鹰"
            tools:visibility="visible" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_attachment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="6dp"
            android:background="@drawable/common_bg_corner_2_stroke_color_191919"
            android:gravity="left"
            android:paddingLeft="4dp"
            android:paddingTop="2dp"
            android:paddingRight="4dp"
            android:paddingBottom="2dp"
            android:text="@{bean.attachment}"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="@id/tv_name"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            app:visibleGone="@{bean.status != 0}"
            tools:text="安全型"
            tools:visibility="visible" />

        <com.kanzhun.common.views.image.OImageView
            android:id="@+id/ov"
            android:layout_width="171dp"
            android:layout_height="150dp"
            android:layout_marginTop="15dp"
            android:layout_marginRight="18dp"
            app:common_placeholder="@drawable/common_bg_placeholder_translate"
            app:imageUrl="@{bean.icon}"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:visibleGone="@{bean.status != 0}"
            tools:visibility="visible" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_keys_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="22dp"
            android:layout_marginTop="18dp"
            android:layout_marginBottom="24dp"
            android:gravity="left"
            android:paddingRight="4dp"
            android:text="@string/me_keys"
            android:textColor="@color/common_color_707070"
            android:textSize="@dimen/common_text_sp_18"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_attachment"
            app:visibleGone="@{bean.status != 0}"
            tools:visibility="visible" />

        <HorizontalScrollView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="17dp"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@+id/tv_keys_title"
            app:layout_constraintLeft_toRightOf="@+id/tv_keys_title"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_keys_title"
            app:visibleGone="@{bean.status != 0}"
            tools:visibility="visible">

            <LinearLayout
                android:id="@+id/ll_keys"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:onClick="@{()->callback.clickGoTestResult(bean)}"
                android:orientation="horizontal" />

        </HorizontalScrollView>
    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
</layout>