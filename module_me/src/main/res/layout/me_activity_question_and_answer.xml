<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_F5F5F5">

        <fragment
            android:id="@+id/fragment"
            android:name="com.kanzhun.common.navigator.NavHostFragmentHideShow"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:defaultNavHost="true"
            app:navGraph="@navigation/me_question_answer_navigation" />
    </FrameLayout>
</layout>