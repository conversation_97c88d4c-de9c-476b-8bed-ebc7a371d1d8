<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.GraduationIdentifyViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.GraduationIdentifyCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_layout_only_left_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/btn_start_certification">
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="32dp"
                    android:text="@string/me_graduation_identify_title"
                    android:textColor="@color/common_color_191919"
                    android:textSize="@dimen/common_text_sp_28"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_assist"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="18dp"
                    android:layout_marginRight="18dp"
                    android:layout_marginTop="4dp"
                    android:text="@string/me_xue_xin_assist"
                    android:textColor="@color/common_color_7F7F7F"
                    android:textSize="@dimen/common_text_sp_14"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_title" />

                <ImageView
                    android:id="@+id/iv_graduation"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="24dp"
                    android:layout_marginRight="18dp"
                    android:background="@mipmap/me_icon_graduation_identify"
                    android:scaleType="fitCenter"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_assist" />

                <include
                    android:id="@+id/layout_option_code"
                    layout="@layout/me_layout_edit_option"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_graduation"
                    app:option="@{viewModel.code}" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </ScrollView>



        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_start_certification"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/common_blue_button_style"
            android:layout_margin="18dp"
            android:enabled="@{viewModel.code.editContent.length()>=16}"
            app:greyDisabledStyle="@{viewModel.code.editContent.length()>=16}"
            android:onClick="@{(view)->callback.clickSubmit(view)}"
            android:text="@string/me_commit_identify"
            app:layout_constraintTop_toBottomOf="@+id/scroll_view"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>