<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/idLine"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/common_color_F0F0F0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabs"
        android:layout_width="match_parent"
        android:layout_height="53dp"
        android:layout_marginTop="1dp"
        android:layout_marginRight="41dp"
        android:paddingLeft="9dp"
        android:paddingRight="9dp"
        app:layout_constraintTop_toBottomOf="@+id/idLine"
        app:tabIndicator="@drawable/item_tab_layout_indicator"
        app:tabIndicatorColor="@color/common_color_141414"
        app:tabIndicatorHeight="4dp"
        app:tabGravity="fill"
        app:tabMinWidth="32dp"
        app:tabPaddingStart="11dp"
        app:tabRippleColor="@color/common_translate"
        app:tabPaddingEnd="11dp"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/common_color_141414"
        app:tabTextColor="@color/common_color_141414" />


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/idViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabs" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="71dp"
        android:layout_height="53dp"
        android:layout_marginTop="1dp"
        android:background="@mipmap/img_login_key_select_search_bg"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/idSearch"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="16dp"
            android:src="@mipmap/img_login_key_select_search"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>