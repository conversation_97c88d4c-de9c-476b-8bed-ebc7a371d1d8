<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".setting.OtherProtocolActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.setting.viewmodel.OtherProtocolViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.setting.callback.OtherProtocolCallback" />
    </data>

    <LinearLayout
        android:id="@+id/llRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">
        <include
            layout="@layout/common_title_bar"
            app:title="@{@string/me_other_protocol}"
            app:callback="@{callback}" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idFaceVerify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:onClick="@{v->callback.faceProtocol(v)}">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:text="@string/me_face_protocol"
                android:textSize="@dimen/common_text_sp_16"
                android:textColor="@color/common_color_191919"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:src="@drawable/common_ic_icon_common_setting_arrow_go" />
        </androidx.constraintlayout.widget.ConstraintLayout>



        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            android:onClick="@{v->callback.logOffProtocol(v)}">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:text="@string/me_log_off_protocol"
                android:textSize="@dimen/common_text_sp_16"
                android:textColor="@color/common_color_191919"/>

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:src="@drawable/common_ic_icon_common_setting_arrow_go" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </LinearLayout>

</layout>