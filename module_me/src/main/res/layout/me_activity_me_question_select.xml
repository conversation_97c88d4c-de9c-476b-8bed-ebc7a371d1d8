<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragmentMain"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/common_color_F5F5F5"
    tools:ignore="MissingDefaultResource">


    <com.kanzhun.common.views.AppTitleView
        android:id="@+id/title_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />


    <com.kanzhun.common.kotlin.ui.statelayout.StateLayout
        android:id="@+id/state_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <net.lucode.hackware.magicindicator.MagicIndicator
                android:id="@+id/magic_indicator"
                android:layout_width="match_parent"
                android:layout_height="82dp" />


            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/view_pager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


</LinearLayout>
