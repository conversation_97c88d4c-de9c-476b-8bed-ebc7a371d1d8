<?xml version="1.0" encoding="utf-8"?>
<com.qmuiteam.qmui.layout.QMUIConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="12dp"
    android:id="@+id/idRoot"
    android:visibility="gone"
    tools:visibility="visible"
    tools:background="@color/common_color_F0F0F0">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/idChildRoot"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:background="@color/common_white"
        android:paddingLeft="12dp"
        android:paddingTop="@dimen/me_card_top_margin"
        android:paddingRight="12dp"
        android:paddingBottom="@dimen/me_card_bottom_margin"
        app:qmui_radius="12dp">


        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/idTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="看准活动照片"
            android:textColor="@color/common_color_191919"
            android:textSize="24dp"
            app:layout_constraintLeft_toLeftOf="@+id/idIcon"
            app:layout_constraintTop_toBottomOf="@+id/idIcon"
            app:layout_goneMarginTop="0dp" />

        <TextView
            android:id="@+id/idRight"
            android:visibility="gone"
            tools:visibility="visible"
            android:text="管理"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            app:layout_constraintEnd_toEndOf="parent"
            android:textColor="@color/common_color_858585"
            android:layout_marginBottom="2dp"
            android:textSize="14dp"
            android:drawableEnd="@drawable/common_ic_gray_right_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idRightNew"
            app:layout_constraintBottom_toBottomOf="@+id/idTitle"
            android:layout_marginBottom="2dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/idIconLeft"
                android:src="@drawable/icon_activity_album_left_start"
                app:layout_constraintEnd_toStartOf="@+id/idContentText"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="14dp"
                android:layout_height="14dp"/>


            <TextView
                android:id="@+id/idContentText"
                android:text="有新的活动照片"
                android:textColor="@color/common_color_408CFF"
                app:layout_constraintEnd_toStartOf="@+id/idIconRight"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginEnd="4dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textSize="14dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                app:qmui_radius="4dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/idIconRight"
                app:qmui_backgroundColor="@color/image_color_red"
                android:layout_width="8dp"
                android:layout_height="8dp"/>

            <ImageView
                app:layout_constraintEnd_toEndOf="parent"
                android:id="@+id/idIconRight"
                android:src="@drawable/common_ic_gray_right_arrow"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/idLayout1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="17dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/idTitle">

            <TextView
                android:id="@+id/idTitle3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="照片已全部隐藏"
                android:textColor="@color/common_color_191919"
                android:textSize="16dp"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginTop="0dp" />

            <TextView
                android:id="@+id/idContent3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginRight="16dp"
                android:hint="展示活动照片，脱单概率更高"
                android:lineHeight="24dp"
                android:textColor="@color/common_color_5E5E5E"
                android:textColorHint="@color/common_color_858585"
                android:textSize="14dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/idTitle3"
                app:zpui_stv_expand_color="@color/common_color_003580"
                app:zpui_stv_expand_text="@string/common_all"
                app:zpui_stv_max_line="4"
                app:zpui_stv_support_custom_expand="false"
                app:zpui_stv_support_expand="true"
                app:zpui_stv_support_real_expand_or_collapse="false" />


            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/idBtnLayout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@color/common_black"
                android:paddingHorizontal="20dp"
                android:paddingVertical="8dp"
                app:layout_constraintBottom_toBottomOf="@+id/idContent3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@+id/idTitle3"
                app:layout_goneMarginTop="0dp"
                app:qmui_radius="20dp">

                <TextView
                    android:id="@+id/idBtn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="去展示"
                    android:textColor="@color/color_white"
                    android:textSize="14dp"
                    android:textStyle="bold" />
            </com.qmuiteam.qmui.layout.QMUIFrameLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/idRecyclerView"
            android:layout_marginTop="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toBottomOf="@+id/idLayout1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:id="@+id/idImageViewEyesShadow"
            android:visibility="gone"
            tools:visibility="visible"
            app:qmui_radius="12dp"
            android:background="@color/common_color_191919_30"
            app:layout_constraintTop_toTopOf="@+id/idRecyclerView"
            app:layout_constraintBottom_toBottomOf="@+id/idRecyclerView"
            app:layout_constraintStart_toStartOf="@+id/idRecyclerView"
            app:layout_constraintEnd_toEndOf="@+id/idRecyclerView"
            android:layout_width="0dp"
            android:layout_height="0dp"/>
        
        <ImageView
            android:id="@+id/idImageViewEyes"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="@+id/idRecyclerView"
            app:layout_constraintBottom_toBottomOf="@+id/idRecyclerView"
            app:layout_constraintStart_toStartOf="@+id/idRecyclerView"
            app:layout_constraintEnd_toEndOf="@+id/idRecyclerView"
            android:src="@mipmap/me_activity_pic_eyes"
            android:layout_width="36dp"
            android:layout_height="36dp"/>

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/idLine"
            android:layout_width="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.33"
            android:layout_height="wrap_content"/>

        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:id="@+id/idImageViewEyesShadow2"
            android:visibility="gone"
            tools:visibility="visible"
            app:qmui_radius="12dp"
            android:background="@color/common_color_191919_30"
            app:layout_constraintTop_toTopOf="@+id/idRecyclerView"
            app:layout_constraintBottom_toBottomOf="@+id/idRecyclerView"
            app:layout_constraintStart_toStartOf="@+id/idRecyclerView"
            app:layout_constraintEnd_toEndOf="@+id/idLine"
            android:layout_width="0dp"
            android:layout_height="0dp"/>

        <ImageView
            android:id="@+id/idImageViewEyes2"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintTop_toTopOf="@+id/idRecyclerView"
            app:layout_constraintBottom_toBottomOf="@+id/idRecyclerView"
            app:layout_constraintStart_toStartOf="@+id/idRecyclerView"
            app:layout_constraintEnd_toEndOf="@+id/idLine"
            android:src="@mipmap/me_activity_pic_eyes"
            android:layout_width="36dp"
            android:layout_height="36dp"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/idBottomLayout"
            app:layout_constraintTop_toBottomOf="@+id/idRecyclerView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="0dp"
            android:padding="12dp"
            app:qmui_radius="16dp"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginTop="20dp"
            android:background="@drawable/me_edit_activity_pic_open_bg"
            android:layout_height="wrap_content">

            <ImageView
                android:src="@drawable/me_edit_activity_pic_open_left_icon"
                android:id="@+id/idImageViewLeft"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="29dp"
                android:layout_height="29dp"/>

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/idBtnRight"
                android:text="开启"
                android:textSize="14dp"
                android:textColor="@color/color_white"
                android:background="@color/common_color_191919"
                app:qmui_backgroundColor="@color/common_color_191919"
                android:paddingHorizontal="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:paddingVertical="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:qmui_radius="20dp"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <TextView
                android:id="@+id/idText"
                android:layout_marginStart="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_marginEnd="12dp"
                android:textColor="@color/common_color_191919"
                android:textSize="14dp"
                android:text="开启展示，其他用户才可以看到你在线下活动的个人照片哦👉🏻"
                app:layout_constraintStart_toEndOf="@+id/idImageViewLeft"
                app:layout_constraintEnd_toStartOf="@+id/idBtnRight"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>




    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>


</com.qmuiteam.qmui.layout.QMUIConstraintLayout>