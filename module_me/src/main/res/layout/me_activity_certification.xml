<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.text.TextUtils" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.CertificationViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.CertificationCallback" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white"
        tools:ignore="SpUsage,ContentDescription,HardcodedText,UnusedAttribute">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@mipmap/me_ic_top_gradient_blue"
                app:layout_constraintDimensionRatio="H,375:227"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true">

            <FrameLayout
                android:id="@+id/title_bar"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_left"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:onClick="@{(view)->callback.clickLeft(view)}"
                    android:paddingLeft="20dp"
                    android:paddingRight="20dp"
                    android:src="@drawable/common_ic_black_back" />

                <ImageView
                    android:id="@+id/iv_right"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:onClick="@{(view)->callback.clickRight(view)}"
                    android:paddingHorizontal="20dp"
                    android:src="@mipmap/common_ic_more_qa" />

            </FrameLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title_bar">

                <FrameLayout
                    android:visibility="gone"
                    android:id="@+id/idFragmentTop"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

                <ScrollView
                    android:id="@+id/scroll_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_gravity="bottom"
                                android:layout_marginStart="18dp"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tv_certification_title"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/me_certification"
                                    android:textColor="@color/common_color_191919"
                                    android:textSize="@dimen/common_text_sp_28"
                                    android:textStyle="bold"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/title_bar" />

                                <TextView
                                    android:id="@+id/tv_certification_assist"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:text="信息仅用于进行身份验证目的并被严格加密存储，我们承诺保证您的信息安全。"
                                    android:textColor="@color/common_color_292929"
                                    android:textSize="@dimen/common_text_sp_12"
                                    app:layout_constraintLeft_toLeftOf="parent"
                                    app:layout_constraintTop_toBottomOf="@+id/tv_certification_title" />

                            </LinearLayout>

                            <ImageView
                                android:layout_width="114dp"
                                android:layout_height="114dp"
                                android:layout_marginEnd="15dp"
                                android:src="@mipmap/me_ic_auth"
                                app:layout_constraintEnd_toEndOf="parent"
                                app:layout_constraintTop_toTopOf="parent" />

                        </LinearLayout>

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="16dp"
                            android:layout_marginTop="16dp"
                            android:background="@drawable/me_bg_desc"
                            android:paddingHorizontal="16dp"
                            android:paddingVertical="12dp"
                            android:text="看准鼓励所有用户都完成实名认证，在这里你遇到的每一个Ta都和你一样优秀而真诚"
                            android:textColor="#1466CF"
                            android:textFontWeight="500"
                            android:textSize="14dp" />

                        <include
                            android:id="@+id/layout_certification_edit_option_name"
                            layout="@layout/me_layout_edit_option"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_certification_assist"
                            app:option="@{viewModel.name}" />

                        <include
                            android:id="@+id/layout_certification_edit_option_num"
                            layout="@layout/me_layout_edit_option"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="12dp"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/layout_certification_edit_option_name"
                            app:option="@{viewModel.num}" />

                    </LinearLayout>

                </ScrollView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/bottom_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/common_white"
                    android:padding="20dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    tools:background="@color/qmui_config_color_gray_9">

                    <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                        android:id="@+id/btn_start_certification"
                        style="@style/common_blue_button_style"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:enabled="@{viewModel.checked  &amp;&amp; !TextUtils.isEmpty(viewModel.name.editContent) &amp;&amp; viewModel.name.editContent.length() >= 2 &amp;&amp; !TextUtils.isEmpty(viewModel.num.editContent) &amp;&amp; viewModel.num.editContent.length() >= 18}"
                        android:onClick="@{(view)->callback.clickSubmit(view)}"
                        android:text="开始人脸识别验证"
                        app:greyDisabledStyle="@{viewModel.checked  &amp;&amp; !TextUtils.isEmpty(viewModel.name.editContent) &amp;&amp; viewModel.name.editContent.length() >= 2 &amp;&amp; !TextUtils.isEmpty(viewModel.num.editContent) &amp;&amp; viewModel.num.editContent.length() >= 18}"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent" />

                    <CheckBox
                        android:id="@+id/check_link"
                        android:layout_width="18dp"
                        android:layout_height="18dp"
                        android:layout_marginBottom="26dp"
                        android:background="@color/common_translate"
                        android:button="@null"
                        android:checked="@={viewModel.checked}"
                        android:drawableStart="@drawable/common_bg_checkbox_link"
                        android:theme="@style/CommonAppTheme"
                        app:layout_constraintBottom_toTopOf="@+id/btn_start_certification"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_protocol" />

                    <TextView
                        android:id="@+id/tv_protocol"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_12"
                        app:layout_constraintBottom_toBottomOf="@id/check_link"
                        app:layout_constraintLeft_toRightOf="@+id/check_link"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/check_link"
                        tools:text="@string/face_protocol" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </FrameLayout>

</layout>