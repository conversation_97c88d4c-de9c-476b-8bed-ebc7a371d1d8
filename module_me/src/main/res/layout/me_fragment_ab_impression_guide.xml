<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    app:defaultNavHost="false"
    tools:context=".info.ABImpressionThemeSelectFragment">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.info.viewmodel.ABImpressionGuideViewModel" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.me.info.viewmodel.ABImpressionViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.info.callback.ABImpressionGuideCallback" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/fragmentMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_color_F5F5F5"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/ll_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:onClick="@{(view)->callback.clickLeft(view)}"
                    android:paddingLeft="18dp"
                    android:paddingTop="9dp"
                    android:paddingRight="18dp"
                    android:paddingBottom="9dp"
                    android:src="@drawable/common_ic_black_cancel" />


                <ImageView
                    android:id="@+id/iv_avatar_right"
                    android:layout_width="156dp"
                    android:layout_height="167dp"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="9dp"
                    android:scaleType="fitXY"
                    android:background="@null"
                    android:src="@mipmap/me_a_b_top_tips" />

                <TextView
                    android:id="@+id/tv_a_b_impression"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/iv_back"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="24dp"
                    android:text="@string/me_show_your_side"
                    android:textColor="@color/common_color_191919"
                    android:textSize="28dp" />

                <TextView
                    android:id="@+id/tv_a_b_impression_tips"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tv_a_b_impression"
                    android:layout_marginLeft="18dp"
                    android:layout_marginTop="4dp"
                    android:text="@string/me_a_b_guide_tips"
                    android:textColor="@color/common_color_797979"
                    android:textSize="14dp" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ll_side_b"
                android:layout_width="281dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_top">

                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="374dp"
                    android:src="@mipmap/me_a_b_life" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/me_bg_a_b_guide_tips"
                    android:orientation="horizontal"
                    android:paddingLeft="6dp"
                    android:paddingRight="24dp">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="45dp"
                        android:layout_alignParentRight="true"
                        android:src="@mipmap/me_ic_a_b_guide_b" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_my_ab_impression_life"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_14"
                        android:textStyle="bold" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/fl_animation_view_b"
                    android:layout_width="92dp"
                    android:layout_height="92dp"
                    android:layout_centerHorizontal="true"
                    android:layout_gravity="center"
                    android:layout_marginTop="328dp"
                    android:background="@mipmap/me_bg_a_b"
                    android:onClick="@{()->callback.rotationYFromB()}">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animation_view_b"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:scaleType="centerCrop"
                        android:layout_gravity="center"
                        app:lottie_fileName="me_a_b_icon.json" />
                </FrameLayout>

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/ll_side_a"
                android:layout_width="281dp"
                android:layout_height="wrap_content"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_top">

                <com.kanzhun.common.views.image.OImageView
                    android:layout_width="match_parent"
                    android:layout_height="374dp"
                    android:src="@mipmap/me_a_b_work" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/me_bg_a_b_guide_tips"
                    android:orientation="horizontal"
                    android:paddingLeft="6dp"
                    android:paddingRight="24dp">

                    <ImageView
                        android:layout_width="36dp"
                        android:layout_height="45dp"
                        android:layout_alignParentRight="true"
                        android:src="@mipmap/me_ic_a_b_guide_a" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:text="@string/me_my_ab_impression_work"
                        android:textColor="@color/common_color_191919"
                        android:textSize="@dimen/common_text_sp_14"
                        android:textStyle="bold" />
                </LinearLayout>

                <FrameLayout
                    android:id="@+id/fl_animation_view_a"
                    android:layout_width="92dp"
                    android:layout_height="92dp"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="328dp"
                    android:background="@mipmap/me_bg_a_b"
                    android:onClick="@{()->callback.rotationYFromA()}"
                    app:lottie_fileName="me_a_b_icon.json">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/animation_view_a"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_gravity="center"
                        android:scaleType="centerCrop"
                        app:lottie_fileName="me_a_b_icon.json" />
                </FrameLayout>

            </RelativeLayout>

            <com.kanzhun.common.views.RoundAlphaButton
                android:id="@+id/btn_save"
                style="@style/common_blue_button_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="46dp"
                android:layout_marginRight="18dp"
                android:onClick="@{()->callback.toThemeFragment()}"
                android:text="@string/me_start_to_edit_a_b_impression"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_side_a" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>