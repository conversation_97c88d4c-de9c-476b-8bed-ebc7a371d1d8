<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.me.identify.viewmodel.EducationSchoolInputViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.me.identify.callback.EducationSchoolInputCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_white">

        <include
            android:id="@+id/title_bar"
            layout="@layout/common_layout_only_left_title_bar"
            app:callback="@{callback}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kanzhun.common.views.textview.BoldTextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/app_layout_page_left_padding"
            android:layout_marginTop="@dimen/app_layout_page_title_margin"
            android:text="@string/me_education_school_search_title"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_28"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title_bar" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginTop="@dimen/app_layout_page_content_margin"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">


            <EditText
                android:id="@+id/edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:background="@null"
                android:gravity="center_vertical"
                android:hint="@string/common_input_school_hint"
                android:singleLine="true"
                android:text="@={viewModel.schoolObservable}"
                android:textColor="@color/common_color_191919"
                android:textColorHint="@color/common_color_B2B2B2"
                android:textCursorDrawable="@drawable/common_color_black_text_cursor"
                android:textSize="@dimen/common_text_sp_24"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view_line"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="12dp"
                android:background="@{viewModel.schoolErrorObservable == 1 ? @color/common_color_FF3F4B : @color/common_black}"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/edit_text" />

            <TextView
                android:id="@+id/tv_error_desc"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@={viewModel.schoolErrorDescObservable}"
                android:textColor="@{viewModel.schoolErrorObservable == 1 ? @color/common_color_FF3F4B : @color/common_color_FFAD33}"
                android:textSize="@dimen/common_text_sp_12"
                android:visibility="@{viewModel.schoolErrorObservable > 0 ? View.VISIBLE : View.GONE}"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/view_line"
                tools:text="学校只支持汉子、大小写英文字母"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/tv_assist_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="12dp"
            android:drawablePadding="6dp"
            android:text="@string/common_school_input_assist_title"
            android:textColor="@color/common_color_7F7F7F"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl_input" />

        <TextView
            android:id="@+id/tv_assist_title_2"
            android:text="请填写学校全称如：中国人民大学，不要填写学校简称，如：人大"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="8dp"
            android:drawableLeft="@drawable/common_bg_oval_color_0046bd_size_4"
            android:drawablePadding="6dp"
            android:textColor="@color/common_color_0046BD"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_assist_title" />


        <TextView
            android:id="@+id/tv_assist"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="8dp"
            android:drawableLeft="@drawable/common_bg_oval_color_0046bd_size_4"
            android:drawablePadding="6dp"
            android:text="@string/common_input_school_assist_one"
            android:textColor="@color/common_color_0046BD"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_assist_title_2" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="8dp"
            android:drawableLeft="@drawable/common_bg_oval_color_0046bd_size_4"
            android:drawablePadding="6dp"
            android:text="@string/common_input_school_assist_tow"
            android:textColor="@color/common_color_0046BD"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_assist" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/common_white"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/btn_next"
            app:layout_constraintTop_toBottomOf="@+id/cl_input" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/common_black_button_style"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="18dp"
            android:onClick="@{()->callback.clickNext()}"
            android:text="@string/common_complete"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>