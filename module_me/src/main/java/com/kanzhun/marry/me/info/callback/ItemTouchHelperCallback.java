package com.kanzhun.marry.me.info.callback;

import android.app.Service;
import android.graphics.Color;
import android.os.Vibrator;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.kanzhun.common.views.ItemTouchChangeListener;
import com.techwolf.lib.tlog.TLog;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/14.
 */
public class ItemTouchHelperCallback extends ItemTouchHelper.Callback {
    RecyclerView.Adapter adapter;
    ItemTouchChangeListener mListener;
    boolean longPressDragEnabled = true;
    boolean enableLastDrag = true;//是否允许最后一条item参与拖动
    boolean showShadow = false;
    int count = 0;//图片or视频总个数

    public ItemTouchHelperCallback(RecyclerView.Adapter adapter) {
        this.adapter = adapter;
    }

    public interface CallBack {
        List callback();
    }

    CallBack callBack;

    public ItemTouchHelperCallback(RecyclerView.Adapter adapter, boolean enableLastDrag, int count, CallBack callBack) {
        this.adapter = adapter;
        this.enableLastDrag = enableLastDrag;
        this.count = count;
        this.callBack = callBack;
    }

    public ItemTouchHelperCallback(RecyclerView.Adapter adapter, boolean enableLastDrag, boolean showShadow) {
        this.adapter = adapter;
        this.enableLastDrag = enableLastDrag;
        this.showShadow = showShadow;
    }

    public void setEnableLastDrag(boolean enableLastDrag) {
        this.enableLastDrag = enableLastDrag;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public void setListener(ItemTouchChangeListener listener) {
        mListener = listener;
    }

    @Override
    public int getMovementFlags(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
        TLog.print("zl_log", "ItemTouchHelperCallback: getMovementFlags()");
        int dragFrlg = 0;
        int count = recyclerView.getAdapter().getItemCount();
        int position = viewHolder.getAbsoluteAdapterPosition();
        if (position >= this.count) {
            TLog.print("zl_log", "ItemTouchHelperCallback: enableLastDrag=%s, count=%s, position=%s, 真实count=%s", enableLastDrag, count, position, this.count);
            return makeMovementFlags(dragFrlg, 0);
        }
        if (recyclerView.getLayoutManager() instanceof GridLayoutManager) {
            dragFrlg = ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT;
        } else if (recyclerView.getLayoutManager() instanceof LinearLayoutManager) {
            dragFrlg = ItemTouchHelper.UP | ItemTouchHelper.DOWN;
        }
        return makeMovementFlags(dragFrlg, 0);
    }

    @Override
    public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, RecyclerView.ViewHolder target) {
        TLog.print("zl_log", "ItemTouchHelperCallback: onMove()");
        //滑动事件  下面注释的代码，滑动后数据和条目错乱，被舍弃
//            Collections.swap(datas,viewHolder.getAdapterPosition(),target.getAdapterPosition());
//            ap.notifyItemMoved(viewHolder.getAdapterPosition(),target.getAdapterPosition());
        //拿到当前拖拽到的item的viewHolder

        int toPosition = target.getAbsoluteAdapterPosition();
        /**
         * 目标位置是加号时
         */
        if (toPosition >= this.count) {
            return false;
        }
        //得到当拖拽的viewHolder的Position
        int fromPosition = viewHolder.getAbsoluteAdapterPosition();
        if (fromPosition < toPosition) {
            for (int i = fromPosition; i < toPosition; i++) {
                Collections.swap(callBack.callback(), i, i + 1);
            }
        } else {
            for (int i = fromPosition; i > toPosition; i--) {
                Collections.swap(callBack.callback(), i, i - 1);
            }
        }
        if (mListener != null) {
            mListener.touchChanged();
        }

        adapter.notifyItemMoved(fromPosition, toPosition);
        return true;
    }

    @Override
    public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {
        //侧滑删除可以使用；
    }

    @Override
    public boolean isLongPressDragEnabled() {
        TLog.print("zl_log", "ItemTouchHelperCallback: isLongPressDragEnabled()");
        return longPressDragEnabled;
    }

    /**
     * 长按选中Item的时候开始调用
     * 长按高亮
     *
     * @param viewHolder
     * @param actionState
     */
    @Override
    public void onSelectedChanged(RecyclerView.ViewHolder viewHolder, int actionState) {
        TLog.print("zl_log", "ItemTouchHelperCallback: onSelectedChanged()");
        idleStatus(viewHolder, actionState);
        super.onSelectedChanged(viewHolder, actionState);
    }

    protected void idleStatus(RecyclerView.ViewHolder viewHolder, int actionState) {
        if (actionState != ItemTouchHelper.ACTION_STATE_IDLE) {
//            viewHolder.itemView.setAlpha(0.5F);
            viewHolder.itemView.setScaleX(0.96F);
            viewHolder.itemView.setScaleY(0.96F);
            //获取系统震动服务//震动70毫秒
            Vibrator vib = (Vibrator) viewHolder.itemView.getContext().getSystemService(Service.VIBRATOR_SERVICE);
            vib.vibrate(70);
        }
    }

    /**
     * 手指松开的时候还原高亮
     *
     * @param recyclerView
     * @param viewHolder
     */
    @Override
    public void clearView(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder) {
        super.clearView(recyclerView, viewHolder);
        TLog.print("zl_log", "ItemTouchHelperCallback: clearView()");

//        viewHolder.itemView.setAlpha(1.0F);
        viewHolder.itemView.setScaleX(1.0F);
        viewHolder.itemView.setScaleY(1.0F);
        if (showShadow) {
            viewHolder.itemView.setBackgroundColor(Color.TRANSPARENT);
        }
//        adapter.notifyDataSetChanged();  //完成拖动后刷新适配器，这样拖动后删除就不会错乱
        if (mListener != null) {
            mListener.onDragFinish();
        }
    }

    public void setLongPressDragEnabled(boolean longPressDragEnabled) {
        this.longPressDragEnabled = longPressDragEnabled;
    }
}
