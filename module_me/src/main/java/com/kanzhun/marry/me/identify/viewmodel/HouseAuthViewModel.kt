package com.kanzhun.marry.me.identify.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.identify.activity.CAR_AUTH_STATUS_NONE
import com.kanzhun.marry.me.identify.model.HouseAuthDetail
import com.kanzhun.utils.T

class HouseAuthViewModel:BaseViewModel() {

    /**
     * 房产认证状态变更
     */
    val houseAuthStatusLiveData: MutableLiveData<Int> = MutableLiveData()

    var authDetail: HouseAuthDetail? = null

    /**
     * 房产认证状态
     * 0:未认证
     * 1:认证中
     * 2:认证成功
     * 3:认证失败
     */
    var houseAuthStatus:Int = 0


    fun getHouseAuthInfo() {
        val observable = RetrofitManager.getInstance().createApi<MeApi>(MeApi::class.java).certHouseDetail()
        HttpExecutor.execute<HouseAuthDetail>(observable, object : BaseRequestCallback<HouseAuthDetail?>(false) {
            override fun onSuccess(data: HouseAuthDetail?) {
                authDetail = data
                if (data == null) {
                    setAuthStatus(CAR_AUTH_STATUS_NONE)
                } else {
                    setAuthStatus(data.status)
                }

            }

            override fun dealFail(reason: ErrorReason?) {
                T.ss(reason?.errReason)
                showError(text = reason?.errReason)
            }
        })
    }

    fun setAuthStatus(status: Int) {
        houseAuthStatusLiveData.value = status
    }
}