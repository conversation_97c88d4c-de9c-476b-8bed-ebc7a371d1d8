package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.parsePageSourceFromBundle;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;

import com.common.AvoidOnResult;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.FileProviderUtils;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.bean.UserSchoolGetBean;
import com.kanzhun.foundation.permission.PermissionCallback;
import com.kanzhun.foundation.permission.PermissionData;
import com.kanzhun.foundation.permission.PermissionHelper;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityGraduationPicIdentifyBinding;
import com.kanzhun.marry.me.identify.EducationIdentifyActivityHandler;
import com.kanzhun.marry.me.identify.callback.GraduationPicIdentifyCallback;
import com.kanzhun.marry.me.identify.viewmodel.GraduationPicIdentifyViewModel;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.marry.me.util.EduSubTitleHandler;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.zhihu.matisse.Matisse;
import com.zhihu.matisse.MimeType;
import com.zhihu.matisse.internal.entity.CaptureStrategy;

import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR>
 * @date 2022/3/22.
 * 毕业证/学位证照片认证
 */
@RouterUri(path = MePageRouter.ME_Identify_PIC_ACTIVITY)
public class GraduationPicIdentifyActivity extends FoundationVMActivity<MeActivityGraduationPicIdentifyBinding, GraduationPicIdentifyViewModel> implements GraduationPicIdentifyCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_graduation_pic_identify;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }
    private long startTime;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getViewModel().getUploadSuccessLiveData().observe(this, new Observer<Uri>() {
            @Override
            public void onChanged(Uri uri) {
                setImageForUri(uri);
            }
        });
        getViewModel().getSuccessLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                EducationIdentifyActivityHandler.Companion.reset();
                AppUtil.startActivity(GraduationPicIdentifyActivity.this, new Intent(GraduationPicIdentifyActivity.this, IdentifyResultActivity.class));
                setResult(Activity.RESULT_OK);
                AppUtil.finishActivityDelay(GraduationPicIdentifyActivity.this);
            }
        });

        getViewModel().userSchoolGetBeanLiveData.observe(this, new Observer<UserSchoolGetBean>() {
            @Override
            public void onChanged(UserSchoolGetBean userSchoolGetBean) {
                EduSubTitleHandler.Companion.handlerUserSchoolGetBean(GraduationPicIdentifyActivity.this,getDataBinding().tvAssist,userSchoolGetBean,"certify-education-gradpic-click", getPageSource());
            }
        });


        startTime = System.currentTimeMillis();
    }

    private PageSource getPageSource() {
        return parsePageSourceFromBundle(getIntent());
    }

    private void setImageForUri(Uri uri) {
        getDataBinding().ivGraduationUpload.setVisibility(View.INVISIBLE);
        getDataBinding().ivGraduation.setImageURI(uri);
        getDataBinding().viewBg.setVisibility(View.VISIBLE);
    }

    @Override
    protected void onResume() {
        super.onResume();
        getViewModel().getData(this);
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADPIC_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().getEduLevelName());
                pointBean.setType("返回");
                return null;
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADPIC_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().getEduLevelName());
                pointBean.setDuration(System.currentTimeMillis() - startTime);
                return null;
            }
        });
    }

    @Override
    public void uploadGraduationPic() {
        PermissionHelper.getStorageHelper(this).setPermissionCallback(new PermissionCallback<PermissionData>() {
            @Override
            public void onResult(boolean yes, @NonNull PermissionData permission) {
                if (yes) {
                    jumpGallery();
                } else {
                    T.ss(R.string.common_no_storage_permission);
                }
            }
        }).requestPermission();
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_GRADPIC_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().getEduLevelName());
                pointBean.setType("上传证书");
                return null;
            }
        });
    }

    private void jumpGallery() {
        Matisse.from(this)
                .choose(MimeType.of(MimeType.JPEG, MimeType.PNG, MimeType.BMP, MimeType.WEBP), true)
                .showSingleMediaType(true)
                .captureStrategy(new CaptureStrategy(true, FileProviderUtils.getFileProviderAuthorities()))
                .restrictOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
                .thumbnailScale(0.85f)
                .spanCount(4)
                .countable(true)
                .maxOriginalSize(100)
                .theme(com.kanzhun.common.R.style.common_bzl_gallery)
                .maxSelectable(1)
                .forResult(101, new AvoidOnResult.Callback() {
                    @Override
                    public void onActivityResult(int requestCode, int resultCode, Intent data) {
                        if (resultCode != FragmentActivity.RESULT_OK || data == null) {
                            return;
                        }
                        List<Uri> fileUris = Matisse.obtainResult(data);
                        if (LList.isEmpty(fileUris)) {
                            return;
                        }
                        Uri uri = fileUris.get(0);
                        getViewModel().uploadFile(uri);
                    }
                });
    }

    @Override
    public void clickSubmit(View view) {
        getViewModel().requestSubmit();

    }
}
