package com.kanzhun.marry.me.identify

import android.content.Context
import android.view.Gravity
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.kotlin.ktx.saveBoolean
import com.kanzhun.foundation.kotlin.ktx.userSp
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeAuthFailBottomDialogBinding
import com.kanzhun.marry.me.util.AuthFailDialogType

class CompanyIdentifyActivityHandler {

    companion object{

        private const val AUTH_FAIL_DIALOG_FREQUENCY_KEY = "AUTH_FAIL_DIALOG_FREQUENCY_KEY_"

        fun reset(){
            userSp().saveBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+AuthFailDialogType.COMPANY_AUTH_FAIL.name,true)
        }

        /**
         * 展示认证失败的底部弹框
         */
        fun showAuthFailBottomDialog(context: Context,imgUrl:String?,rejectReason:String?) {
            userSp().getBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+ AuthFailDialogType.COMPANY_AUTH_FAIL.name,true).apply {
                if(this){
                    CommonViewBindingDialog(context,
                        mCancelable = true,
                        mCanceledOnTouchOutside = true,
                        mGravity = Gravity.BOTTOM,
                        mPaddingLeft = 0,
                        mPaddingRight = 0,
                        mAnimationStyle = R.style.common_window_bottom_to_top_anim,
                        onInflateCallback = { inflater, dialog ->
                            val binding = MeAuthFailBottomDialogBinding.inflate(inflater)
                            binding.apply {
                                if(imgUrl.isNullOrEmpty()){
                                    ivCarAuthPositive.gone()
                                }else{
                                    ivCarAuthPositive.visible()
                                    ivCarAuthPositive.load(imgUrl)
                                    ivCarAuthPositive.setOnClickListener {
                                        ivCarAuthPositive.imageViewer(imgUrl?:"")
                                    }
                                }
                                tvReason.text = rejectReason
                                btnNext.setOnClickListener {
                                    dialog.dismiss()
                                    reportPoint("certify-fail-popup-click"){
                                        actionp2 = "工作"
                                    }
                                }
                                tvTitle.text = "工作认证失败"
                            }
                            binding
                        })
                        .show()
                    reportPoint("certify-fail-popup-expo"){
                        actionp2 = "工作"
                    }
                    userSp().saveBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+AuthFailDialogType.COMPANY_AUTH_FAIL.name,false)
                }
            }


        }
    }
}