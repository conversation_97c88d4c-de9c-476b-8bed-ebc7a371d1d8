package com.kanzhun.marry.me.setting.viewmodel;

import android.app.Application;
import android.net.Uri;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.utils.StringUtils;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.core.Observable;

public class SettingHelpViewModel extends FoundationViewModel {
    private static final String TAG = "SettingHelpViewModel";
    private ObservableField<String> editContent = new ObservableField<>("");
    private MutableLiveData<Boolean> addSuccess = new MutableLiveData<>();
    private List<Uri> imageUris = new ArrayList<>();
    private List<String> picTokens = new ArrayList<>();

    public SettingHelpViewModel(Application application) {
        super(application);
    }


    public MutableLiveData<Boolean> getAddSuccess() {
        return addSuccess;
    }

    public ObservableField<String> getEditContent() {
        return editContent;
    }

    public List<Uri> getImageUris() {
        return imageUris;
    }

    public List<String> getImageUrisString() {
        List<String> data = new ArrayList<>(imageUris.size());
        for (Uri uri : imageUris) {
            data.add(uri.toString());
        }
        return data;
    }

    public void requestSubmit() {
        picTokens.clear();
        setShowProgressBar("", false);
        if (LList.isEmpty(imageUris)) {
            addFeedback();
        } else {
            List<Uri> uploadUris = new ArrayList<>(imageUris.size());
            uploadUris.addAll(imageUris);
            requestUploadPic(uploadUris);
        }
    }

    private void requestUploadPic(List<Uri> uploadUris) {
        if (LList.isEmpty(uploadUris)) {
            addFeedback();
            return;
        }
        Uri uri = uploadUris.remove(0);
        UploadFileUtil.uploadImage(UploadFileUtil.ADVICE_SOURCE, uri, new UploadRequestCallback<ImageUploadModel>() {
            @Override
            public void handleInChildThread(ImageUploadModel data) {
                super.handleInChildThread(data);

            }

            @Override
            public void onSuccess(ImageUploadModel data) {
                picTokens.add(data.token);
                requestUploadPic(uploadUris);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                T.ss(reason.getErrReason());
            }
        });
    }

    private void addFeedback() {
        String imgUrls = "";
        if (!LList.isEmpty(picTokens)) {
            imgUrls = StringUtils.listToString(picTokens);
        }
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(MeApi.class).addFeedback(editContent.get(), imgUrls, null, 10, 0);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                addSuccess.postValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }
}