package com.kanzhun.marry.me.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.RectF;
import android.net.Uri;
import android.util.AttributeSet;

import androidx.annotation.IntRange;

import com.kanzhun.marry.me.R;
import com.yalantis.ucrop.model.CropParameters;
import com.yalantis.ucrop.model.ImageState;
import com.yalantis.ucrop.task.BitmapCropTask;
import com.yalantis.ucrop.util.RectUtils;
import com.yalantis.ucrop.view.CropImageView;
import com.yalantis.ucrop.view.GestureCropImageView;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2022/5/11.
 */
public class MeGestureCropImageView extends GestureCropImageView {
    private static final String TAG = "MeGestureCropImageView";
    public static final float SCALE_CHANGE_LIMIT = 10.0F;//缩放变化最小最距离
    public static final float TRANSLATE_CHANGE_LIMIT = 5.0F;//位移变化最小距离
    float[] imageLoadedRectF = new float[4];//图片加载完成后的左上右下，四个点的坐标
    private RectF cropRect;
    int maxSizeX;
    int maxSizeY;
    private boolean canDoubleTapScale = true;
    private boolean isCompletionByAlphaTransition;

    public MeGestureCropImageView(Context context) {
        this(context, null);
    }

    public MeGestureCropImageView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public MeGestureCropImageView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.me_crop_view, defStyle, 0);
            isCompletionByAlphaTransition = a.getBoolean(R.styleable.me_crop_view_me_completion_by_alpha_transition, false);
        }
        try {
            Field cropRectFieldIns = CropImageView.class.getDeclaredField("mCropRect");
            cropRectFieldIns.setAccessible(true);
            cropRect = (RectF) cropRectFieldIns.get(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public boolean isCompletionByAlphaTransition() {
        return isCompletionByAlphaTransition;
    }


    @Override
    public float getDoubleTapTargetScale() {
        if (isCanDoubleTapScale()) {
            return super.getDoubleTapScaleSteps();
        } else {
            return getCurrentScale();
        }
    }

    public boolean isCanDoubleTapScale() {
        return canDoubleTapScale;
    }

    public void setCanDoubleTapScale(boolean canDoubleTapScale) {
        this.canDoubleTapScale = canDoubleTapScale;
    }

    @Override
    public void setMaxResultImageSizeX(@IntRange(from = 10) int maxResultImageSizeX) {
        super.setMaxResultImageSizeX(maxResultImageSizeX);
        this.maxSizeX = maxResultImageSizeX;
    }

    @Override
    public void setMaxResultImageSizeY(@IntRange(from = 10) int maxResultImageSizeY) {
        super.setMaxResultImageSizeY(maxResultImageSizeY);
        this.maxSizeY = maxResultImageSizeY;
    }

    @Override
    protected void onImageLaidOut() {
        super.onImageLaidOut();
        imageLoadedRectF[0] = mCurrentImageCorners[0];
        imageLoadedRectF[1] = mCurrentImageCorners[1];
        imageLoadedRectF[2] = mCurrentImageCorners[4];
        imageLoadedRectF[3] = mCurrentImageCorners[5];
    }

    public Uri getCropImage(Bitmap.CompressFormat mCompressFormat, int mCompressQuality) {
        cancelAllAnimations();
        setImageToWrapCropBounds(false);

        final ImageState imageState = new ImageState(
                cropRect, RectUtils.trapToRect(mCurrentImageCorners),
                getCurrentScale(), getCurrentAngle());
        final CropParameters cropParameters = new CropParameters(
                maxSizeX, maxSizeY,
                mCompressFormat, mCompressQuality,
                getImageInputPath(), getImageOutputPath(), getExifInfo());

        Bitmap mViewBitmap = getViewBitmap();
        BitmapCropTask bitmapCropTask = new BitmapCropTask(mViewBitmap, imageState, cropParameters, null, isAllowSmaller());
        if (mViewBitmap == null || mViewBitmap.isRecycled() || imageState.getCurrentImageRect().isEmpty()) {
            return null;
        }
        try {
            Method mResize = BitmapCropTask.class.getDeclaredMethod("resize");// 无参方法
            mResize.setAccessible(true);
            float resizeScale = (float) mResize.invoke(bitmapCropTask);
            Method mCrop = BitmapCropTask.class.getDeclaredMethod("crop", float.class);
            mCrop.setAccessible(true);
            mCrop.invoke(bitmapCropTask, resizeScale);
            return Uri.fromFile(new File(cropParameters.getImageOutputPath()));
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return null;
    }

    public ImageState getCropImageState() {
        return new ImageState(
                cropRect, RectUtils.trapToRect(mCurrentImageCorners),
                getCurrentScale(), getCurrentAngle());
    }

    public float[] getCurrImageLoadedRectF() {
        return mCurrentImageCorners;
    }

    public RectF getCropRect() {
        return cropRect;
    }

    public boolean isCropRectChanged() {
        if (mCurrentImageCorners != null && mCurrentImageCorners.length >= 8) {
            float left = mCurrentImageCorners[0];
            float top = mCurrentImageCorners[1];
            float right = mCurrentImageCorners[4];
            float bottom = mCurrentImageCorners[5];
            float currentWith = right - left;
            float currentHeight = bottom - top;

            float loadedLeft = imageLoadedRectF[0];
            float loadedTop = imageLoadedRectF[1];
            float loadedRight = imageLoadedRectF[2];
            float loadedBottom = imageLoadedRectF[3];
            float loadedWith = loadedRight - loadedLeft;
            float loadedHeight = loadedBottom - loadedTop;
            return Math.abs(currentWith - loadedWith) > SCALE_CHANGE_LIMIT
                    || Math.abs(currentHeight - loadedHeight) > SCALE_CHANGE_LIMIT
                    || Math.abs(left - loadedLeft) > TRANSLATE_CHANGE_LIMIT
                    || Math.abs(top - loadedTop) > TRANSLATE_CHANGE_LIMIT
                    || Math.abs(right - loadedRight) > TRANSLATE_CHANGE_LIMIT
                    || Math.abs(bottom - loadedBottom) > TRANSLATE_CHANGE_LIMIT;
        }
        return true;
    }

    public void setCompletionByAlphaTransition(boolean completionByAlphaTransition) {
        isCompletionByAlphaTransition = completionByAlphaTransition;
    }

}
