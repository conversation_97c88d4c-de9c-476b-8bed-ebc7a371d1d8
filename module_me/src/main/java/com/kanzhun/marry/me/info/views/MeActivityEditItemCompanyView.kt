package com.kanzhun.marry.me.info.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityBaseInfoEditItemCompanyBinding

class MeActivityEditItemCompanyView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {


    val binding: MeActivityBaseInfoEditItemCompanyBinding

    init {
        val rootView = LayoutInflater.from(context).inflate(R.layout.me_activity_base_info_edit_item_company,this,false)
        binding = MeActivityBaseInfoEditItemCompanyBinding.bind(rootView)
        addView(rootView)
    }
}