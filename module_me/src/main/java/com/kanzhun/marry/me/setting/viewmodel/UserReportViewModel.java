package com.kanzhun.marry.me.setting.viewmodel;

import android.app.Application;
import android.net.Uri;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.api.model.ReportReasonModel;
import com.kanzhun.utils.StringUtils;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LList;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/6/15
 */
public class UserReportViewModel extends FoundationViewModel {
    public String userId;
    public @UserReportSource.State
    int source;
    public String resourceId;
    public ReportReasonModel.ReportReasonBean selectReason;
    private MutableLiveData<ReportReasonModel> reportReasonLiveData = new MutableLiveData<>();
    public ObservableField<String> contentTextObservable = new ObservableField<>();
    public int status;
    private List<Uri> imageUris = new ArrayList<>();
    private List<String> picTokens = new ArrayList<>();
    private MutableLiveData<Boolean> successLiveData = new MutableLiveData<>();
    private String title;

    public UserReportViewModel(Application application) {
        super(application);
    }

    public void initParams(String userId, @UserReportSource.State int source, String resourceId) {
        this.userId = userId;
        this.source = source;
        this.resourceId = resourceId;
    }

    public String getTitle() {
        return title;
    }

    public void requestReportReasons() {
        Observable<BaseResponse<ReportReasonModel>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestReportReason(source, userId);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<ReportReasonModel>(true) {
            @Override
            public void onSuccess(ReportReasonModel data) {
                title = data.title;
                reportReasonLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public MutableLiveData<ReportReasonModel> getReportReasonLiveData() {
        return reportReasonLiveData;
    }

    public List<Uri> getImageUris() {
        return imageUris;
    }

    public List<String> getImageUrisString() {
        List<String> data = new ArrayList<>(imageUris.size());
        for (Uri uri : imageUris) {
            data.add(uri.toString());
        }
        return data;
    }

    public void requestSubmit() {
        picTokens.clear();
        setShowProgressBar("", false);
        if (LList.isEmpty(imageUris)) {
            requestAddReport();
        } else {
            List<Uri> uploadUris = new ArrayList<>(imageUris.size());
            uploadUris.addAll(imageUris);
            requestUploadPic(uploadUris);
        }
    }

    private void requestUploadPic(List<Uri> uploadUris) {
        if (LList.isEmpty(uploadUris)) {
            requestAddReport();
            return;
        }
        Uri uri = uploadUris.remove(0);
        UploadFileUtil.uploadImage(UploadFileUtil.ADVICE_SOURCE, uri, new UploadRequestCallback<ImageUploadModel>() {
            @Override
            public void onSuccess(ImageUploadModel data) {
                picTokens.add(data.token);
                requestUploadPic(uploadUris);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                hideShowProgressBar();
                T.ss(reason.getErrReason());
            }
        });
    }

    private void requestAddReport() {
        Map<String, Object> params = new HashMap<>();
        params.put("targetId", userId);
        params.put("reasonType", selectReason.code);
        params.put("content", contentTextObservable.get());
        params.put("source", source);
        params.put("resourceId", resourceId);
        if (!LList.isEmpty(picTokens)) {
            params.put("evidences", StringUtils.listToString(picTokens));
        }
        HttpExecutor.requestSimplePost(URLConfig.URL_REPORT_ADD, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                successLiveData.setValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getSuccessLiveData() {
        return successLiveData;
    }

    public void initSecondPage(String name, String subTitle, int code) {
        selectReason = new ReportReasonModel.ReportReasonBean();
        selectReason.name = name;
        selectReason.code = code;
        title = subTitle;
    }
}
