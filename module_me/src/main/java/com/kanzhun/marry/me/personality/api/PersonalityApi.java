package com.kanzhun.marry.me.personality.api;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.personality.api.model.SurveyQueryModel;

import io.reactivex.rxjava3.core.Observable;
import retrofit2.http.GET;
import retrofit2.http.Query;

/**
 * <AUTHOR>
 * @date 2022/3/28.
 */
public interface PersonalityApi {
    @GET(URLConfig.URL_SURVEY_QUERY)
    Observable<BaseResponse<SurveyQueryModel>> surveyQuery(@Query("templateCode") String templateCode);
}
