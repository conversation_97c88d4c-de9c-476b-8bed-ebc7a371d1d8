package com.kanzhun.marry.me.info.adapter

import android.content.Context
import android.view.View
import com.kanzhun.common.views.label.LabelView
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.marry.me.R

class ShowMoreTagAdapter( context: Context, list:List<ProfileInfoModel.Label>):TabAdapter(context,list){

    override fun updateChildAsMore(position: Int, child: View?) {
        super.updateChildAsMore(position, child)
        if(child is LabelView){
            child.setText("共${count}个")
            child.setImageResource(R.drawable.me_more_tag)
        }
    }

    override fun lastIsMore(): Boolean {
        return true
    }
}