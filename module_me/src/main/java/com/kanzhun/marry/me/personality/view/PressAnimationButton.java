package com.kanzhun.marry.me.personality.view;

import android.animation.ValueAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;

/**
 * <AUTHOR>
 * @date 2022/4/10.
 */
public class PressAnimationButton extends androidx.appcompat.widget.AppCompatTextView {
    boolean isDuringAnimation = false;//是否正在动画中

    public PressAnimationButton(@NonNull Context context) {
        this(context, null);
    }

    public PressAnimationButton(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public PressAnimationButton(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (isDuringAnimation) {
            return false;
        }
        boolean result = super.onTouchEvent(event);
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                setScaleX(0.9F);
                setScaleY(0.9F);
                setTranslationY(QMUIDisplayHelper.dpToPx(10));
                break;
            case MotionEvent.ACTION_UP:
                recoverAnimation();
                break;
            case MotionEvent.ACTION_CANCEL:
                recoverAnimation();
                break;

        }
        return result;
    }

    private void recoverAnimation() {
        isDuringAnimation = true;
        postDelayed(new Runnable() {
            @Override
            public void run() {
                isDuringAnimation = false;
            }
        }, 1000);
        int translateY = QMUIDisplayHelper.dpToPx(10);
        ValueAnimator animator = ValueAnimator.ofFloat(0.9F, 1.0F)
                .setDuration(300);
        animator.setInterpolator(InterpolatorUtil.createDefaultBezierInterpolator());
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                float value = (float) animation.getAnimatedValue();
                setScaleX(value);
                setScaleY(value);
                setTranslationY(translateY * (1.0F - value));
            }
        });
        animator.start();
    }
}
