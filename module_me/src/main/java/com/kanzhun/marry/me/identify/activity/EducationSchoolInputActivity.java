package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.common.bindadapter.CommonBindingAdapters.setGreyDisabledStyle;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.listener.OnItemClickListener;
import com.kanzhun.common.dialog.DialogExtKt;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.utils.T;
import com.kanzhun.utils.base.LText;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.api.model.SearchSchoolResponse;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityEducationSchoolInputBinding;
import com.kanzhun.marry.me.databinding.MeItemEducationSeachSchoolBinding;
import com.kanzhun.marry.me.identify.callback.EducationSchoolInputCallback;
import com.kanzhun.marry.me.identify.viewmodel.EducationSchoolInputViewModel;
import com.kanzhun.utils.ToolsUtils;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.MultiClickUtil;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;
import io.reactivex.rxjava3.subjects.PublishSubject;
import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/4/6
 * 学校输入页面
 */
public class EducationSchoolInputActivity extends FoundationVMActivity<MeActivityEducationSchoolInputBinding, EducationSchoolInputViewModel> implements EducationSchoolInputCallback {

    private BaseBinderAdapter adapter;
    private SearchSchoolItemBinder itemBinder;
    private PublishSubject<String> searchSubject;
    private Disposable searchDisposable;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
        getViewModel().getSuccessLivaData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                if (aBoolean) {
                    Intent intent = new Intent();
                    intent.putExtra("school", getViewModel().getSearchContent());
                    intent.putExtra("schoolArea", getViewModel().schoolArea);
                    setResult(RESULT_OK, intent);
                    AppUtil.finishActivity(EducationSchoolInputActivity.this);
                }
            }
        });
        getViewModel().getSearchSchoolLiveData().observe(this, new Observer<List<SearchSchoolResponse.SearchSchoolBean>>() {
            @Override
            public void onChanged(List<SearchSchoolResponse.SearchSchoolBean> schools) {
                itemBinder.setSearchContent(getDataBinding().editText.getText().toString().trim());
                adapter.setList(schools);
                getDataBinding().recyclerView.setVisibility(LList.isEmpty(schools) ? View.GONE : View.VISIBLE);
            }
        });
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_PAGE_EXPO,null);
        getDataBinding().editText.post(new Runnable() {
            @Override
            public void run() {
                QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, true);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("学校名称字段");
                        return null;
                    }
                });
            }
        });

    }

    private void initView() {
        String school = getIntent().getStringExtra("school");
        getViewModel().setSearchContent(school);
        if (!TextUtils.isEmpty(school)) {
            getDataBinding().editText.setText(school);
            getDataBinding().editText.setSelection(school.length());
        }
        initSearchSubject();
        QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, true);
//        ToolsUtils.lengthFilter(this, getDataBinding().editText, 60, getResources().getString(R.string.common_over_count_char, 60));
        adapter = new BaseBinderAdapter();
        itemBinder = new SearchSchoolItemBinder();
        adapter.addItemBinder(SearchSchoolResponse.SearchSchoolBean.class, itemBinder);
        getDataBinding().recyclerView.setLayoutManager(new LinearLayoutManager(this));
        getDataBinding().recyclerView.setAdapter(adapter);
        getDataBinding().editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                getDataBinding().btnNext.setEnabled(s.length() > 0);
                setGreyDisabledStyle(getDataBinding().btnNext,s.length() > 0);
                if(s.length() > 30){
                    T.ss(getResources().getString(R.string.common_over_count_char, 30));
                    getViewModel().schoolErrorObservable.set(0);
                    getViewModel().schoolErrorDescObservable.set("");
                    searchObservable(s.toString().substring(0,30));
                    getDataBinding().editText.setText(s.toString().substring(0,30));
                    getDataBinding().editText.setSelection(getDataBinding().editText.getText().length());
                }  else {
                    getViewModel().schoolErrorObservable.set(0);
                    getViewModel().schoolErrorDescObservable.set("");
                    searchObservable(s.toString().trim());
                }
            }
        });
        getDataBinding().editText.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setType("学校名称字段");
                        return null;
                    }
                });
            }
        });
        getDataBinding().btnNext.setEnabled(false);
        setGreyDisabledStyle(getDataBinding().btnNext,false);
        adapter.setOnItemClickListener(new OnItemClickListener() {
            @Override
            public void onItemClick(@androidx.annotation.NonNull BaseQuickAdapter<?, ?> adapter, @androidx.annotation.NonNull View view, int position) {
                SearchSchoolResponse.SearchSchoolBean item = (SearchSchoolResponse.SearchSchoolBean) adapter.getItem(position);
                getViewModel().setSearchContent(item.name);
                getDataBinding().editText.setText(item.name);
                getDataBinding().editText.setSelection(item.name.length());
                adapter.setList(null);
                getDataBinding().recyclerView.setVisibility(View.GONE);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_SUGGESTION_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setIdx(position);
                        return null;
                    }
                });
            }
        });
    }

    private void initSearchSubject() {
        searchSubject = PublishSubject.create();
        searchSubject.debounce(500, TimeUnit.MILLISECONDS)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new io.reactivex.rxjava3.core.Observer<String>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                        searchDisposable = d;
                    }

                    @Override
                    public void onNext(@NonNull String s) {
                        getViewModel().searchSchool(s);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {

                    }

                    @Override
                    public void onComplete() {

                    }
                });
    }

    private void searchObservable(String content) {
        if (TextUtils.equals(content, getViewModel().getSearchContent())) {
            return;
        }
        getViewModel().setSearchContent(content);
        if (!TextUtils.isEmpty(content)) {
            searchSubject.onNext(content);
        } else {
            getViewModel().getSearchSchoolLiveData().setValue(null);
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (searchDisposable != null) {
            searchDisposable.dispose();
        }
        QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_education_school_input;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setType("返回");
                return null;
            }
        });
    }

    @Override
    public void clickNext() {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        if(!TextUtils.isEmpty(getViewModel().getSearchContent()) && getViewModel().getSearchContent().length() < 4){
            DialogExtKt.showTwoButtonDialog(this, "温馨提示", "请检查您填写的学校名称为全称，否则可能不会通过审核", false, false,
                    "确定提交", new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            getViewModel().querySchool();
                            return null;
                        }
                    }, "取消", new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            return null;
                        }
                    },false);
        }else {
            getViewModel().querySchool();
        }
        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_SCHOOLFILL_PAGE_CLICK, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setType("完成");
                return null;
            }
        });
    }

    private static final class SearchSchoolItemBinder extends BaseDataBindingItemBinder<SearchSchoolResponse.SearchSchoolBean, MeItemEducationSeachSchoolBinding> {

        private String searchContent;

        public void setSearchContent(String searchContent) {
            this.searchContent = searchContent;
        }

        @Override
        protected int getResLayoutId() {
            return R.layout.me_item_education_seach_school;
        }

        @Override
        protected void bind(BinderDataBindingHolder<MeItemEducationSeachSchoolBinding> holder,
                            MeItemEducationSeachSchoolBinding binding, SearchSchoolResponse.SearchSchoolBean item) {
            setAllContentText(getContext(), binding.tvContent, item.name, searchContent);
        }

        private void setAllContentText(Context context, TextView textView, String text, String searchContent) {
            String lowText = "";
            if (!TextUtils.isEmpty(text)) {
                lowText = text.toLowerCase();
            }
            if (!TextUtils.isEmpty(searchContent)) {
                searchContent = searchContent.toLowerCase();
            }
            if (!TextUtils.isEmpty(searchContent) && lowText.contains(searchContent)) {
                SpannableString s = new SpannableString(lowText);
                Pattern p = Pattern.compile(searchContent);
                Matcher m = p.matcher(s);
                while (m.find()) {
                    int start = m.start();
                    int end = m.end();
                    s.setSpan(new ForegroundColorSpan(ContextCompat.getColor(context, R.color.common_color_7171FF)), start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                }
                textView.setText(s);
            } else {
                textView.setText(text);
            }

        }
    }
}
