package com.kanzhun.marry.me.info.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.model.profile.QuestionTemplateModel
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.MeApi

const val SCENE_MY_ANSWER = 1
const val SCENE_MY_VOICE = 2

class MeSelectQuestionViewModel : BaseViewModel() {

    var answerType: Int = 2 //1-语音; 2-文本

    var isSelectMode:Boolean = false

    private fun getScene(): Int {
        return if (answerType == MePageRouter.TYPE_ANSWER_VOICE) 2 else 1
    }

    fun isEditTextAnswer() = answerType == MePageRouter.TYPE_ANSWER_TEXT


    val questionTemplateLiveData = MutableLiveData<QuestionTemplateModel>()



     fun queryQuestionTemplate() {
        showLoading()
        val responseObservable = RetrofitManager.getInstance().createApi(MeApi::class.java).getQuestionTemplate(getScene())
        HttpExecutor.execute(responseObservable, object : BaseRequestCallback<QuestionTemplateModel?>() {
            override fun onSuccess(data: QuestionTemplateModel?) {
                data?.run {
                    questionTemplateLiveData.value = this
                }
                if (data != null) {
                    showContent()
                } else {
                    showError()
                }
            }

            override fun dealFail(reason: ErrorReason) {}
            override fun onComplete() {
                super.onComplete()
            }
        })
    }
}