package com.kanzhun.marry.me.info.activity.preview.item

import android.widget.LinearLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.kanzhun.common.kotlin.ext.blurry
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.unBlurry
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.BlurryedLayout
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.foundation.api.model.LoginUserCertStatusItem
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.api.model.ProfileInfoModel.QuestionAnswer
import com.kanzhun.foundation.api.model.clickEnable
import com.kanzhun.foundation.api.model.isLock
import com.kanzhun.foundation.api.model.lockTxt
import com.kanzhun.foundation.api.model.pointStr
import com.kanzhun.foundation.model.profile.ext.isMyself
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MePreviewUserQaSectionBinding
import com.kanzhun.foundation.kotlin.me.getAnswerOptionString
import com.kanzhun.marry.me.info.callback.MeInfoEditFragmentCallbackImp

class QAItemProvider(val click: MeInfoEditFragmentCallbackImp) : BaseItemProvider<UserPreviewItemBean, MePreviewUserQaSectionBinding>() {
    private var showExpandButton = true
    private val maxAnswerCount = 5
    override fun onBindItem(binding: MePreviewUserQaSectionBinding, item: UserPreviewItemBean) {
        binding.run {
            val adapter = binding.recyclerview.adapter
            if (adapter is QAAdapter) {
                adapter.setNewInstance(getRealAnswerList(item.data.textAnswerList?: listOf()).toMutableList())
                adapter.setUserPreviewItemBean(item,click)
            } else {
                binding.recyclerview.adapter = QAAdapter().also {
                    it.setNewInstance(getRealAnswerList(item.data.textAnswerList?: listOf()).toMutableList())
                    it.setUserPreviewItemBean(item,click)
                }

            }

            tvExpand.text = "展开剩余的${item.data.textAnswerList.size - maxAnswerCount}个回答"
            tvExpand.visible(showExpandButton && item.data.textAnswerList.size > maxAnswerCount)
            tvExpand.clickWithTrigger {
                showExpandButton = false
                tvExpand.gone()
                val adapter1 = binding.recyclerview.adapter

                if (adapter1 is QAAdapter) {
                    adapter1.addData(item.data.textAnswerList.subList(maxAnswerCount, item.data.textAnswerList.size))
                }
            }

        }
    }

    private fun getRealAnswerList(textAnswerList: List<QuestionAnswer>): List<QuestionAnswer> {
        return if (showExpandButton) {
            if (textAnswerList.size > maxAnswerCount) {
                textAnswerList.subList(0, maxAnswerCount)
            } else {
                textAnswerList
            }
        } else {
            textAnswerList
        }
    }


    class QAAdapter : BaseQuickAdapter<ProfileInfoModel.QuestionAnswer, BaseViewHolder>(R.layout.me_preview_user_qa_item) {
        var loginUserCertStatusItem:LoginUserCertStatusItem? = null
        var isMyself:Boolean = true
        var click: MeInfoEditFragmentCallbackImp? = null
        fun setUserPreviewItemBean(item: UserPreviewItemBean,clickP: MeInfoEditFragmentCallbackImp){
            loginUserCertStatusItem = item.data.loginUserCertStatus?.textAnswer
            isMyself = item.data.userId.isMyself()
            click = clickP
        }
        override fun convert(holder: BaseViewHolder, item: ProfileInfoModel.QuestionAnswer) {
            holder.setText(R.id.tv_question, item.question)
            holder.setText(R.id.tv_answer, item.answer)
            val option = item.getAnswerOptionString()
            holder.setText(R.id.tv_options,option)
            holder.setGone(R.id.tv_options, option.isEmpty())
            holder.setGone(R.id.tv_answer,item.answer.isNullOrEmpty())
            holder.setVisible(R.id.divider, data.lastOrNull() != item)


            if(!isMyself && loginUserCertStatusItem != null && loginUserCertStatusItem!!.isLock()){
                holder.getView<LinearLayout>(R.id.idContentLinearLayout).post {
                    val content = if (loginUserCertStatusItem?.clickEnable() == true) {
                        "添加我的问答 解锁查看对方资料"
                    } else {
                        "你的资料正在审核中 通过后可解锁查看"
                    }
                    holder.getView<LinearLayout>(R.id.idContentLinearLayout).blurry(holder.getView<BlurryedLayout>(R.id.idBlurryLayout)
                        , loginUserCertStatusItem!!.lockTxt("去添加"),content,loginUserCertStatusItem!!.clickEnable(),holder.bindingAdapterPosition != 0){
//                        ProtocolHelper.parseProtocol(loginUserCertStatusItem!!.url)
                        click?.clickAddAnswer()
                        reportPoint("user-detail-locked-module-click"){
                            type = "我的问答"
                        }
                    }
                    reportPoint("user-detail-locked-module-expo"){
                        status = loginUserCertStatusItem!!.pointStr()
                        type = "我的问答"
                    }
                }
            }else{
                holder.getView<LinearLayout>(R.id.idContentLinearLayout).unBlurry(holder.getView<BlurryedLayout>(R.id.idBlurryLayout))
            }
        }
    }
}