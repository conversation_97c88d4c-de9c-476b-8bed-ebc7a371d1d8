package com.kanzhun.marry.me.info.callback;

import android.view.View;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.callback.TitleBarCallback;
import com.kanzhun.common.views.image.OImageView;
import com.kanzhun.foundation.api.model.ABFace;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.base.LoveGoalListBean;
import com.kanzhun.foundation.bean.BaseStoryShowItem;
import com.kanzhun.foundation.model.profile.OpenScreen;
import com.kanzhun.marry.me.info.bean.MyNickAndIntroduction;
import com.kanzhun.foundation.bean.PicStoryItem;
import com.kanzhun.marry.me.info.bean.VideoStoryItem;
import com.kanzhun.marry.me.info.viewmodel.MyInfoEditViewModel;
import com.kanzhun.marry.me.info.views.MeImageChooserView;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

public interface MeInfoEditFragmentCallback extends TitleBarCallback {
    void clickAvatar(int status,
                     String rejectReason,
                     String value);

    void clickNick(String nick, int status, String rejectReason);

    void clickIntroduction(MyNickAndIntroduction nickAndIntroduction);

    void clickAddAB();

    void clickAddVoice();

    void clickVoice(ProfileInfoModel.QuestionAnswer voice);

    void clickAddAnswer();

    void clickAddOpenScreen();

    void clickGoTestResult(OpenScreen openScreen);

    void clickBirthday(ProfileInfoModel.BaseInfo baseMeta);

    void clickEducation(ProfileInfoModel.BaseInfo baseMeta, PageSource pageSource);

    void clickLivingPlace(ProfileInfoModel.BaseInfo baseMeta);

    void clickHometown(ProfileInfoModel.BaseInfo baseMeta);

    void clickHeight(ProfileInfoModel.BaseInfo baseMeta);

    void clickOccupation(ProfileInfoModel.BaseInfo baseMeta);

    void clickRevenue(ProfileInfoModel.BaseInfo baseMeta);

    void clickHouseCar();

    void clickPermanentResidence(ProfileInfoModel.BaseInfo baseMeta);

    void clickPrivacyNotice(View view);

    void clickCompany(ProfileInfoModel.BaseInfo baseMeta, PageSource pageSource);

    void clickLabel(List<ProfileInfoModel.Label> bean, int type);

    void clickPic(@NotNull BaseStoryShowItem story, int nowAllCount);

//    void clickProfilePic(@NotNull BaseStoryShowItem story, int nowAllCount,View root);


    void clickPicAdd(int max);


    void clickFamily(@Nullable String familyDesc, int familyDescStatus, String familyDescInfo);

    void clickSingle(@Nullable String singleReason, int singleReasonCertStatus, String singleReasonCertInfo);


    void clickBeauty(@Nullable String idealPartnerDesc, int idealPartnerDescStatus, @Nullable String idealPartnerDescInfo);

    void clickAB(ABFace bean);

    void clickAnswer(ProfileInfoModel.QuestionAnswer data);

    void clickLoveMarryStatus();

    void clickInfo();

    void clickWeight(ProfileInfoModel.BaseInfo baseInfo);

    void clickEthnickty(@Nullable ProfileInfoModel.BaseInfo bean);

    void clickSmoke(@Nullable ProfileInfoModel.BaseInfo bean);

    void clickDrink(@Nullable ProfileInfoModel.BaseInfo bean);

    void clickMarrayStatus(@Nullable ProfileInfoModel.BaseInfo bean);

    void clickBabyPlan(@Nullable ProfileInfoModel.BaseInfo bean);

    void clickLovePlan(@Nullable ProfileInfoModel.BaseInfo bean, LoveGoalListBean love);

    void clickDeletePic(@NotNull BaseStoryShowItem story, int nowAllCount);

    //    void clickProfileDeletePic(@NotNull BaseStoryShowItem story, int nowAllCount, @NotNull MeImageChooserView chooserView);
//    void updateProfileList(@NotNull List<BaseStoryShowItem> list, int nowAllCount, @NotNull MeImageChooserView chooserView);

    void clickRetryStory(@NotNull PicStoryItem story, int nowAllCount);

    void clickRetryStory(@NotNull VideoStoryItem story, int nowAllCount);

    void clickRetryDown(@NotNull BaseStoryShowItem story, @NotNull OImageView imageView);

    void clickLike(@Nullable String interestDesc, int interestDescStatus, String interestDescInfo);

    void clickActivityPhoto(@Nullable MyInfoEditViewModel mViewModel);
}