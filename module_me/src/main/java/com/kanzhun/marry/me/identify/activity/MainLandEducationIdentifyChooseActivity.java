package com.kanzhun.marry.me.identify.activity;

import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.parsePageSourceFromBundle;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.text.style.StyleSpan;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.chad.library.adapter.base.BaseBinderAdapter;
import com.kanzhun.common.adpter.BaseDataBindingItemBinder;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.bean.UserSchoolGetBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.CertificationIdentifySource;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityMainLandEducationIdentifyChooseBinding;
import com.kanzhun.marry.me.databinding.MeLayoutItemEducaiotnIdentifyBinding;
import com.kanzhun.marry.me.identify.EducationIdentifyActivityHandler;
import com.kanzhun.marry.me.identify.callback.MainLandEducationIdentifyChooseCallback;
import com.kanzhun.marry.me.identify.model.EducationIdentifyBean;
import com.kanzhun.marry.me.identify.viewmodel.MainLandEducationIdentifyChooseViewModel;
import com.kanzhun.marry.me.point.MePointAction;
import com.sankuai.waimai.router.annotation.RouterUri;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;
import kotlin.jvm.functions.Function1;

/**
 * <AUTHOR>
 * @date 2022/3/21.
 * 大陆选择认证方式
 */
@RouterUri(path = MePageRouter.ME_MAIN_LAND_EDUCATION_IDENTIFY_CHOOSE_ACTIVITY)
public class MainLandEducationIdentifyChooseActivity extends FoundationVMActivity<MeActivityMainLandEducationIdentifyChooseBinding, MainLandEducationIdentifyChooseViewModel> implements MainLandEducationIdentifyChooseCallback {
    BaseBinderAdapter mainLandAdapter;

    private static final int INDENTIFY_REQUEST_CODE = 110;

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_main_land_education_identify_choose;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    protected boolean shouldUseNewGrayStatusBar() {
        return true;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initData(getIntent());
        mainLandAdapter = new BaseBinderAdapter();
        mainLandAdapter.addItemBinder(EducationIdentifyBean.class, new BaseDataBindingItemBinder<EducationIdentifyBean, MeLayoutItemEducaiotnIdentifyBinding>() {
            @Override
            protected int getResLayoutId() {
                return R.layout.me_layout_item_educaiotn_identify;
            }

            @Override
            protected void bind(BinderDataBindingHolder<MeLayoutItemEducaiotnIdentifyBinding> holder, MeLayoutItemEducaiotnIdentifyBinding binding, EducationIdentifyBean item) {
                binding.setCallback(MainLandEducationIdentifyChooseActivity.this);
                binding.setBean(item);
            }
        });
        getDataBinding().rvChineseMainlandEducation.setLayoutManager(new LinearLayoutManager(getBaseContext()));
        getDataBinding().rvChineseMainlandEducation.setAdapter(mainLandAdapter);
        mainLandAdapter.setList(getViewModel().getMainLand());

        PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_EXPO, new Function1<PointBean, Unit>() {
            @Override
            public Unit invoke(PointBean pointBean) {
                pointBean.setActionp2(getViewModel().eduLevelName);
                return null;
            }
        });

        if (getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA) instanceof UserSchoolGetBean) {
            UserSchoolGetBean mUserSchoolGetBean = (UserSchoolGetBean) getIntent().getSerializableExtra(BundleConstants.BUNDLE_DATA);
            if (mUserSchoolGetBean != null) {
                if (mUserSchoolGetBean.getEduCertStatus() == 1) {
                    EducationIdentifyActivityHandler.Companion.showCerting(getDataBinding().idIncludeCerting, mUserSchoolGetBean, new Function0<Unit>() {
                        @Override
                        public Unit invoke() {
                            clickLeft(getDataBinding().idIncludeCerting.idBtn);
                            return null;
                        }
                    });
                    getDataBinding().rvChineseMainlandEducation.setVisibility(View.GONE);
                    return;
                }
                if (mUserSchoolGetBean.getEduCertStatus() == 2) {
                    EducationIdentifyActivityHandler.Companion.showAuthFailBottomDialog(MainLandEducationIdentifyChooseActivity.this, mUserSchoolGetBean, getPageSource());
                }
            }
        }
    }

    private PageSource getPageSource() {
        return parsePageSourceFromBundle(getIntent());
    }

    private void initData(Intent intent) {
        getViewModel().schoolName = intent.getStringExtra("schoolName");
        getViewModel().eduLevel = intent.getIntExtra("eduLevel", 0);
        getViewModel().certStatus = intent.getIntExtra("certStatus", 0);
        getViewModel().eduLevelName = intent.getStringExtra("eduLevelName");
        getViewModel().schoolTime = intent.getIntExtra("schoolTime",0);

        if (!TextUtils.isEmpty(getViewModel().schoolName)) {
            String content1 = "认证学历：" + getViewModel().schoolName + " " + getViewModel().eduLevelName;
            if(getViewModel().schoolTime == 1){
                content1 = content1 + "·全日制";
            }else if(getViewModel().schoolTime == 2){
                content1 = content1 + "·非全日制";
            }
            content1 = content1 + " ";
            String content2 = getViewModel().certStatus == 1 ? "审核中" : "点击查看";
            SpannableStringBuilder builder = new SpannableStringBuilder(content1 + content2);
            builder.setSpan(new StyleSpan(Typeface.NORMAL), 0, content1.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            builder.setSpan(new StyleSpan(Typeface.BOLD), content1.length(), content1.length() + content2.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            builder.setSpan(new ClickableSpan() {
                @Override
                public void updateDrawState(@NonNull TextPaint ds) {
                    super.updateDrawState(ds);
                    getDataBinding().tvDesc.setHighlightColor(Color.TRANSPARENT);
                    ds.setColor(ContextCompat.getColor(MainLandEducationIdentifyChooseActivity.this, getViewModel().certStatus == 1 ? R.color.common_color_FF653D : R.color.common_color_0046BD));
                    ds.setUnderlineText(false);
                }

                @Override
                public void onClick(@NonNull View widget) {
                    ((TextView) widget).setHighlightColor(Color.TRANSPARENT);
                    if (getViewModel().certStatus == 1) {
                        return;
                    }
                    MePageRouter.jumpToEducationIdentifyActivity(
                            MainLandEducationIdentifyChooseActivity.this,
                            CertificationIdentifySource.ME_INFO, PageSource.EDU_CHOOSE, "", "提交，去认证", true, true, new Function0<Unit>() {
                                @Override
                                public Unit invoke() {
                                    return null;
                                }
                            }
                    );
                }
            }, content1.length(), content1.length() + content2.length(), Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            getDataBinding().tvDesc.setVisibility(View.VISIBLE);
            getDataBinding().tvDesc.setMovementMethod(LinkMovementMethod.getInstance());
            getDataBinding().tvDesc.setText(builder);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        initData(intent);
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void onEducationIdentifyItemClick(EducationIdentifyBean bean) {
        switch (bean.getType()) {
            case 0:
                //学信网在线验证码
                Intent intent = new Intent(this, XueXinIdentifyActivity.class);
                intent.putExtra("schoolName", getViewModel().schoolName);
                intent.putExtra("eduLevel", getViewModel().eduLevel);
                intent.putExtra("eduLevelName", getViewModel().eduLevelName);
                intent.putExtra("schoolTime",getViewModel().schoolTime);
                AppUtil.startActivityForResult(this, intent, INDENTIFY_REQUEST_CODE);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(getViewModel().eduLevelName);
                        pointBean.setType("学信网在线验证码");
                        return null;
                    }
                });
                break;
            case 1:
                //学历认证_毕业证学位证编号
                Intent intent1 = new Intent(this, GraduationIdentifyActivity.class);
                intent1.putExtra("schoolName", getViewModel().schoolName);
                intent1.putExtra("eduLevel", getViewModel().eduLevel);
                intent1.putExtra("eduLevelName", getViewModel().eduLevelName);
                intent1.putExtra("schoolTime",getViewModel().schoolTime);
                AppUtil.startActivityForResult(this, intent1, INDENTIFY_REQUEST_CODE);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(getViewModel().eduLevelName);
                        pointBean.setType("毕业证/学位证编号");
                        return null;
                    }
                });
                break;
            case 2:
                //毕业证/学位证照片认证
                Intent intent2 = new Intent(this, GraduationPicIdentifyActivity.class);
                intent2.putExtra("schoolName", getViewModel().schoolName);
                intent2.putExtra("eduLevel", getViewModel().eduLevel);
                intent2.putExtra("eduLevelName", getViewModel().eduLevelName);
                intent2.putExtra("schoolTime",getViewModel().schoolTime);
                AppUtil.startActivityForResult(this, intent2, INDENTIFY_REQUEST_CODE);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_EDUCATION_METHOD_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(getViewModel().eduLevelName);
                        pointBean.setType("毕业证/学位证照片");
                        return null;
                    }
                });
                break;
            default:
                break;
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK && requestCode == INDENTIFY_REQUEST_CODE) {
            AppUtil.finishActivity(this);
        }
    }
}
