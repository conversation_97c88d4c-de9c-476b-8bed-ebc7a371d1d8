package com.kanzhun.marry.me.info.util

import com.kanzhun.foundation.utils.point.reportPoint
import okhttp3.internal.toNonNegativeInt

class MeUpdateIntroHandler {

    companion object{

        fun expPoint(string: String,certStatus:String?){
            reportPoint("myinfo-introduction-page-expo"){
                source = "编辑"
                status = getMeEditInfoSourceStrByStatus(string = string, status =  certStatus.toNonNegativeInt(0))
            }
        }

        fun clickPoint(string: String,certStatus:String?){
            reportPoint("myinfo-introduction-case-change "){
                source = "编辑"
                status = getMeEditInfoSourceStrByStatus(string = string, status =  certStatus.toNonNegativeInt(0))
            }
        }
    }


}