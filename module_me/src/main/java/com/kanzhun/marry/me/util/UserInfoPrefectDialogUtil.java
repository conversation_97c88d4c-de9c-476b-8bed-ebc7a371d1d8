package com.kanzhun.marry.me.util;

import android.view.View;

import androidx.fragment.app.FragmentActivity;

import com.kanzhun.common.dialog.CommonBindingDialog;
import com.kanzhun.foundation.api.bean.ProfileStatusBean;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeLayoutDialogUserInfoPerfectBinding;
import com.kanzhun.utils.views.OnMultiClickListener;

import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/8/8
 */
public class UserInfoPrefectDialogUtil {
    private FragmentActivity activity;
    private List<ProfileStatusBean> profileStatusBeans;
    private CommonBindingDialog.Builder<MeLayoutDialogUserInfoPerfectBinding> builder;
    boolean showAudit = false;

    public UserInfoPrefectDialogUtil(FragmentActivity activity, List<ProfileStatusBean> profileStatusBeans) {
        this.activity = activity;
        this.profileStatusBeans = profileStatusBeans;
        builder = getDialogBuilder();
    }

    private CommonBindingDialog.Builder<MeLayoutDialogUserInfoPerfectBinding> getDialogBuilder() {
        CommonBindingDialog.Builder<MeLayoutDialogUserInfoPerfectBinding> builder = new CommonBindingDialog.Builder<>(activity, R.layout.me_layout_dialog_user_info_perfect);
        builder.setCanceledOnTouchOutside(false);
        builder.setPadding(26, 26);

        showAudit = true;
        for (ProfileStatusBean statusBean : profileStatusBeans) {
            if (statusBean.inReview != 1) {
                showAudit = false;
                break;
            }
        }

        for (ProfileStatusBean statusBean : profileStatusBeans) {
            switch (statusBean.code) {
                case 10://头像
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icAvatar.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                case 20://昵称
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icNick.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                case 30://自我介绍
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icIntroduce.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                case 50://我的生活
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icStory.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                case 300://工作信息
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icWork2.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                case 310://学历信息
                    if (showAudit || statusBean.inReview != 1) {
                        builder.getBinding().icEdu.getRoot().setVisibility(View.VISIBLE);
                    }
                    break;
                default:
                    break;
            }
        }
        builder.getBinding().setShowAudit(showAudit);
        if (showAudit) {
            builder.getBinding().setBtnDesc(activity.getString(R.string.me_ok));
            builder.getBinding().setTitle(activity.getString(R.string.me_user_info_perfect_title_2));
        } else {
            builder.getBinding().setBtnDesc(activity.getString(R.string.me_info_perfect_go));
            builder.getBinding().setTitle(activity.getString(R.string.me_user_info_perfect_title));
        }

        if (showAudit) {
            builder.getBinding().icEdu.ivIcon.setImageResource(R.drawable.common_bg_circle_ffad33_50);
            builder.getBinding().icAvatar.ivIcon.setImageResource(R.drawable.common_bg_circle_ffad33_50);
            builder.getBinding().icNick.ivIcon.setImageResource(R.drawable.common_bg_circle_ffad33_50);
            builder.getBinding().icStory.ivIcon.setImageResource(R.drawable.common_bg_circle_ffad33_50);
            builder.getBinding().icIntroduce.ivIcon.setImageResource(R.drawable.common_bg_circle_ffad33_50);
        }

        builder.getBinding().btnGoto.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                if (showAudit) {
                    builder.getDialog().dismiss();
                } else {
                    builder.getDialog().dismiss();
                    MePageRouter.jumpToMeInfoEditActivity(activity,"");
                }
            }
        });
        return builder;
    }

    public void show() {
        builder.createDialog().show();
    }
}
