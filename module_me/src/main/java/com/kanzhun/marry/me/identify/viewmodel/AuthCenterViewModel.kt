package com.kanzhun.marry.me.identify.viewmodel

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.CertificationIdentifySource
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.kotlin.flow.requestFlow
import com.kanzhun.http.kotlin.manager.ApiManager
import com.kanzhun.marry.me.api.model.CarCertStatus
import com.kanzhun.marry.me.api.model.CompanyCertStatus
import com.kanzhun.marry.me.api.model.EduCertStatus
import com.kanzhun.marry.me.api.model.FaceCertStatus
import com.kanzhun.marry.me.api.model.HouseCertStatus
import com.kanzhun.marry.me.api.model.MarryCertStatus
import com.kanzhun.marry.me.api.model.PhotoCertStatus
import com.kanzhun.marry.me.api.model.RevenueCertStatus
import com.kanzhun.marry.me.api.model.UserCertListModelK
import com.kanzhun.marry.me.identify.API
import com.kanzhun.marry.me.identify.activity.AvatarAuthActivity
import com.kanzhun.marry.me.identify.activity.CarAuthActivity
import com.kanzhun.marry.me.identify.activity.HouseAuthActivity
import com.kanzhun.marry.me.identify.activity.RevenueAuthActivity
import com.kanzhun.marry.me.point.MePointAction
import com.kanzhun.utils.T
import kotlinx.coroutines.launch

class AuthCenterViewModel : BaseViewModel() {
    val queryUserCertListLiveData = MutableLiveData<UserCertListModelK?>()

    companion object {
        const val NO_WRITE = "未填写"
        const val CERT_ING = "认证中"
        const val CERT_FAILD = "认证驳回"
        const val CERT_SUCCESS = "认证通过"
    }

    fun queryUserCertList(source: String): LiveData<UserCertListModelK?> {
        viewModelScope.launch {
            queryUserCertListLiveData.value = requestFlow(requestCall = {
                ApiManager.create(API::class.java).queryUserCertList(source)
            }, errorBlock = { code, error ->
                T.bSS(error)
                queryUserCertListLiveData.value = null
            })
        }
        return queryUserCertListLiveData
    }

    fun clApproveClick(context: Context, pageSource: PageSource) {
        if (queryUserCertListLiveData.value?.faceCert?.status != FaceCertStatus.TYPE_APPROVE_FACED_SUCCESS) {
            //实名认证页面
            MePageRouter.jumpToCertificationActivity(
                context,
                CertificationIdentifySource.ME_AUTH,
                pageSource = pageSource
            )
            reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                actionp2 = NO_WRITE
                type = "实名认证"
                status = AccountHelper.getInstance().phase.toString()
            }
        } else {
            reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                actionp2 = CERT_SUCCESS
                type = "实名认证"
                status = AccountHelper.getInstance().phase.toString()
            }
        }

    }

    private fun checkFaceCert(): Boolean {
        return if (queryUserCertListLiveData.value?.faceCert?.status == FaceCertStatus.TYPE_APPROVE_FACED_SUCCESS) {
            true
        } else {
            T.bSS("请先完成实名认证")
            false
        }
    }

    fun clickMarry(context: Context) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.marriageCert?.status) {
                MarryCertStatus.TYPE_APPROVE_FACED_ING -> {
                    MePageRouter.jumpToMarryActivity(context, PageSource.AUTH_CENTER_ACTIVITY)
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "婚姻状态认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                MarryCertStatus.TYPE_APPROVE_FACE_IDLE -> {
                    MePageRouter.jumpToMarryActivity(context, PageSource.AUTH_CENTER_ACTIVITY)
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "婚姻状态认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                MarryCertStatus.TYPE_APPROVE_FACE_NO -> {
                    MePageRouter.jumpToMarryActivity(context, PageSource.AUTH_CENTER_ACTIVITY)
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "婚姻状态认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                MarryCertStatus.TYPE_APPROVE_FACED_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "婚姻状态认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clPhotoApproveClick(context: Context) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.avatarCert?.status) {
                PhotoCertStatus.TYPE_APPROVE_PHOTO_ING_ROBOT, PhotoCertStatus.TYPE_APPROVE_PHOTO_ING_PEOPLE -> {
                    AvatarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.avatarCert,
                        PageSource.AUTH_CENTER_ACTIVITY
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "头像认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                PhotoCertStatus.TYPE_APPROVE_PHOTO_NO -> {
                    AvatarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.avatarCert,
                        PageSource.AUTH_CENTER_ACTIVITY
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "头像认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                PhotoCertStatus.TYPE_APPROVE_PHOTO_FAILED -> {
                    AvatarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.avatarCert,
                        PageSource.AUTH_CENTER_ACTIVITY
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "头像认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                PhotoCertStatus.TYPE_APPROVE_PHOTO_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "头像认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clSchoolApproveClick(context: Context, clickResult: () -> Unit) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.eduCert?.status) {
                EduCertStatus.TYPE_APPROVE_SCHOOL_ING -> {
                    MePageRouter.jumpToEducationIdentifyActivity(
                        context,
                        CertificationIdentifySource.ME_AUTH,
                        PageSource.AUTH_CENTER_ACTIVITY,
                        btnText = "提交，去认证",
                        certSuccess = clickResult
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "学历认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                EduCertStatus.TYPE_APPROVE_SCHOOL_FAILED -> {
                    //学校学历输入页
                    MePageRouter.jumpToEducationIdentifyActivity(
                        context,
                        CertificationIdentifySource.ME_AUTH,
                        PageSource.AUTH_CENTER_ACTIVITY,
                        btnText = "提交，去认证",
                        certSuccess = clickResult
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "学历认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                EduCertStatus.TYPE_APPROVE_SCHOOL_NO_DEPENDENCE,
                EduCertStatus.TYPE_APPROVE_SCHOOL_NO_DEPENDENCE_RESET,
                EduCertStatus.TYPE_APPROVE_SCHOOL_NO -> {
                    //学校学历输入页
                    MePageRouter.jumpToEducationIdentifyActivity(
                        context,
                        CertificationIdentifySource.ME_AUTH,
                        PageSource.AUTH_CENTER_ACTIVITY,
                        btnText = "提交，去认证",
                        certSuccess = clickResult
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "学历认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                EduCertStatus.TYPE_APPROVE_SCHOOL_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "学历认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clCompanyApproveClick(context: Context, bean: UserCertListModelK) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.companyCert?.status) {
                CompanyCertStatus.TYPE_APPROVE_COMPANY_ING -> {
                    if (bean.companyCert != null && bean.companyCert!!.certType == 3) {
                        MePageRouter.jumpToResetEmailActivity(
                            context,
                            PageSource.AUTH_CENTER_ACTIVITY
                        )
                    } else {
                        MePageRouter.jumpToMeCompanyAuthActivity(
                            context,
                            PageSource.AUTH_CENTER_ACTIVITY,
                            btnText = "提交，去认证"
                        )
                    }
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "工作认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                CompanyCertStatus.TYPE_APPROVE_COMPANY_FAILED -> {
                    MePageRouter.jumpToMeCompanyAuthActivity(
                        context,
                        PageSource.AUTH_CENTER_ACTIVITY,
                        btnText = "提交，去认证"
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "工作认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                CompanyCertStatus.TYPE_APPROVE_COMPANY_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "工作认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                else -> {
                    MePageRouter.jumpToMeCompanyAuthActivity(
                        context,
                        PageSource.AUTH_CENTER_ACTIVITY,
                        btnText = "提交，去认证"
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "工作认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clRevenueClick(context: Context) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.incomeCert?.status) {
                RevenueCertStatus.TYPE_APPROVE_REVENUE_NO -> {
                    RevenueAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.incomeCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "收入认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                RevenueCertStatus.TYPE_APPROVE_REVENUE_FAILED -> {
                    RevenueAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.incomeCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "收入认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                RevenueCertStatus.TYPE_APPROVE_REVENUE_ING -> {
                    RevenueAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.incomeCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "收入认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                RevenueCertStatus.TYPE_APPROVE_REVENUE_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "收入认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clCarApproveClick(context: Context) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.carCert?.status) {
                CarCertStatus.TYPE_APPROVE_CAR_NO -> {
                    CarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.carCert?.status!!
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "车产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                CarCertStatus.TYPE_APPROVE_CAR_FAILED -> {
                    CarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.carCert?.status!!
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "车产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                CarCertStatus.TYPE_APPROVE_CAR_ING -> {
                    CarAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.carCert?.status!!
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "车产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                CarCertStatus.TYPE_APPROVE_CAR_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "车产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

    fun clHouseApproveClick(context: Context) {
        if (checkFaceCert()) {
            when (queryUserCertListLiveData.value?.houseCert?.status) {
                HouseCertStatus.TYPE_APPROVE_HOUSE_NO -> {
                    HouseAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.houseCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = NO_WRITE
                        type = "房产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                HouseCertStatus.TYPE_APPROVE_HOUSE_FAILED -> {
                    HouseAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.houseCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_FAILD
                        type = "房产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                HouseCertStatus.TYPE_APPROVE_HOUSE_ING -> {
                    HouseAuthActivity.intent(
                        context,
                        queryUserCertListLiveData.value?.houseCert?.status ?: 0
                    )
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_ING
                        type = "房产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }

                HouseCertStatus.TYPE_APPROVE_HOUSE_SUCCESS -> {
                    reportPoint(MePointAction.CERTIFY_PAGE_CLICK) {
                        actionp2 = CERT_SUCCESS
                        type = "房产认证"
                        status = AccountHelper.getInstance().phase.toString()
                    }
                }
            }
        }
    }

}