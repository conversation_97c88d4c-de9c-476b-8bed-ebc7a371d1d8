package com.kanzhun.marry.me.identify.fragment.house

import android.app.Activity
import android.os.Bundle
import androidx.fragment.app.Fragment
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.base.fragment.BaseBindingFragment
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.databinding.MeFragmentHouseAuthExamineBinding
import com.kanzhun.marry.me.identify.model.HouseAuthDetail
import com.kanzhun.common.kotlin.ui.onClick

/**
 * <AUTHOR>
 * 房产认证审核页
 */
class HouseAuthExamineFragment : BaseBindingFragment<MeFragmentHouseAuthExamineBinding, HouseAuthExamineViewModel>() {
    override fun preInit(arguments: Bundle) {
        mViewModel.authDetail = arguments.getSerializable(BundleConstants.BUNDLE_DATA) as HouseAuthDetail

    }

    override fun initView() {
        mBinding.apply {
            titleBar.asBackButton()
            btnNext.onClick {
                val context = context
                if (context is Activity) {
                    context.onBackPressed()
                }
                reportPoint("certify-inreview-page-click"){
                    actionp2 = "房产"
                }
            }
            mViewModel.authDetail?.run {
                ivHouseAuthPositive.load(imgUrl)
            }
            ivHouseAuthPositive.setOnClickListener {
                mViewModel.authDetail?.imgUrl?.run {
                    ivHouseAuthPositive.imageViewer(this)
                }
            }
        }
    }

    override fun initData() {
    }

    override fun onRetry() {
    }

    override fun getStateLayout() = null

    companion object{
        fun newInstance(detail: HouseAuthDetail): Fragment {
            val args = Bundle()
            val fragment = HouseAuthExamineFragment()
            args.putSerializable(BundleConstants.BUNDLE_DATA,detail)
            fragment.arguments = args
            return fragment
        }
    }
}

class HouseAuthExamineViewModel : BaseViewModel() {
    var authDetail: HouseAuthDetail? = null

}
