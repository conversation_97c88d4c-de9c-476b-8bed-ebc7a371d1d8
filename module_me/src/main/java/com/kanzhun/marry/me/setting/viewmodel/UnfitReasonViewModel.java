package com.kanzhun.marry.me.setting.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableField;
import androidx.databinding.ObservableInt;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.api.model.FeedbackReasonModel;
import com.kanzhun.marry.me.point.MePointReporter;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class UnfitReasonViewModel extends FoundationViewModel {

    public String targetId;
    private MutableLiveData<FeedbackReasonModel> reasonListLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> submitSuccess = new MutableLiveData<>();
    private ObservableField<String> editContent = new ObservableField<>("");
    private ObservableInt selectedReasonType = new ObservableInt();
    public String selectedReason;

    public UnfitReasonViewModel(Application application) {
        super(application);
    }

    public void requestReportReasons() {
        Observable<BaseResponse<FeedbackReasonModel>> responseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestFeedbackReason(targetId);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<FeedbackReasonModel>(true) {
            @Override
            public void onSuccess(FeedbackReasonModel data) {
                reasonListLiveData.setValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public void addFeedback() {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(MeApi.class).addFeedback(editContent.get().trim(), null, targetId, 20, selectedReasonType.get());
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback(true) {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                setShowProgressBar();
            }

            @Override
            public void onSuccess() {
                submitSuccess.postValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
        MePointReporter.Companion.reportFeedbackDialogSubmit(targetId, selectedReason);
    }

    public MutableLiveData<FeedbackReasonModel> getReasonListLiveData() {
        return reasonListLiveData;
    }

    public MutableLiveData<Boolean> getSubmitSuccess() {
        return submitSuccess;
    }

    public ObservableField<String> getEditContent() {
        return editContent;
    }

    public void setEditContent(ObservableField<String> editContent) {
        this.editContent = editContent;
    }

    public ObservableInt getSelectedReasonType() {
        return selectedReasonType;
    }

    public void setSelectedReasonType(ObservableInt selectedReasonType) {
        this.selectedReasonType = selectedReasonType;
    }
}