package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.text.TextUtils;
import android.util.ArrayMap;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.base.PageSource;
import com.kanzhun.foundation.api.FoundationApi;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.UpdateComInfoBean;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.api.bean.EmailSuffixResponseBean;
import com.kanzhun.marry.me.api.model.CompanyCertInfoModelNew;
import com.kanzhun.marry.me.api.model.CompanyShortModel;
import com.kanzhun.marry.me.api.model.WorkOtherUserTipsBean;
import com.kanzhun.marry.me.api.model.WorkOtherUserTipsBeanTip;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.configuration.UserSettingConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/5
 */
public class CompanyAuthViewModel extends FoundationViewModel {

    public ObservableField<String> companyNameObservable = new ObservableField<>();
    public MutableLiveData<CompanyCertInfoModelNew> companyCertLiveData = new MutableLiveData<>();
    public ObservableField<String> companyShortObservable = new ObservableField<>();
    public boolean editEnterCer = true;
    public boolean mustEdit = false;
    private MutableLiveData<Integer> cancelLiveData = new MutableLiveData<>();

    public MutableLiveData<String> industryLiveData = new MutableLiveData<>();
    public MutableLiveData<String> careerLiveData = new MutableLiveData<>();
    public String industryCode = "";
    public String careerCode;
    public String careerBak;
    public boolean success;
    public PageSource pageSource = PageSource.NONE;
    public String btnStr = "提交";
    public String protocol = "";

    public Boolean isFirstCompanyAuthFragmentExp = true;


    public CompanyAuthViewModel(Application application) {
        super(application);
        requestCompanyCertInfo();
    }

    public MutableLiveData<CompanyCertInfoModelNew> getCompanyCertLiveData() {
        return companyCertLiveData;
    }

    public MutableLiveData<EmailSuffixResponseBean> emailSuffixResponseBeanLiveData = new MutableLiveData<>();
    public MutableLiveData<WorkOtherUserTipsBean> workOtherUserTipsBeanLiveData = new MutableLiveData<>();

    public void requestEmailSuffix(String company,CompanyCertInfoModelNew mCompanyCertInfoModelNew) {
        Observable<BaseResponse<EmailSuffixResponseBean>> observable = RetrofitManager.getInstance().createApi(MeApi.class).getEmailSuffix(company);
        HttpExecutor.execute(observable, new BaseRequestCallback<EmailSuffixResponseBean>() {
            @Override
            public void onSuccess(EmailSuffixResponseBean data) {
                emailSuffixResponseBeanLiveData.postValue(data);
                companyCertLiveData.setValue(mCompanyCertInfoModelNew);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }
        });
    }

    public void requestCompanyCertInfo() {
        showEmptyLoading();

        Observable<BaseResponse<WorkOtherUserTipsBean>> workOtherUserTipsBeanResponse =
                RetrofitManager.getInstance().createApi(MeApi.class).getCompanyOtherTips();
        HttpExecutor.execute(workOtherUserTipsBeanResponse, new BaseRequestCallback<WorkOtherUserTipsBean>() {
            @Override
            public void onSuccess(WorkOtherUserTipsBean data) {
                workOtherUserTipsBeanLiveData.postValue(data);
            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });

        Observable<BaseResponse<CompanyCertInfoModelNew>> responseObservable =
                RetrofitManager.getInstance().createApi(MeApi.class).getCompanyCertInfo();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<CompanyCertInfoModelNew>(true) {
            @Override
            public void onSuccess(CompanyCertInfoModelNew data) {
                if (data == null) {
                    showEmptyError();
                    return;
                }
                showEmptySuccess();
                if (!TextUtils.isEmpty(data.getIndustry())) {
                    industryLiveData.postValue(data.getIndustry());
                }
                if (!TextUtils.isEmpty(data.getIndustryCode())) {
                    industryCode = data.getIndustryCode();
                }
                careerCode = data.getCareerCode();
                careerBak = data.getCareer();
                careerLiveData.postValue(data.getCareer());
                if (!TextUtils.isEmpty(data.getCompanyName())) {
                    companyNameObservable.set(data.getCompanyName());
                    if(!mustEdit){
                        requestEmailSuffix(data.getCompanyName(),data);
                    }else {
                        companyCertLiveData.setValue(data);
                    }
                    companyShortObservable.set(data.getCompanyNameShort());
                }else {
                    companyCertLiveData.setValue(data);
                }

            }

            @Override
            public void dealFail(ErrorReason reason) {
                showEmptyError();
            }
        });
    }

    public void requestCompanyShort() {
        Observable<BaseResponse<CompanyShortModel>> responseObservable =
                RetrofitManager.getInstance().createApi(MeApi.class).getCompanyShort(companyNameObservable.get());
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<CompanyShortModel>() {
            @Override
            public void onSuccess(CompanyShortModel data) {
                companyShortObservable.set(data.companyNameShort);
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
            }
        });
    }

    public MutableLiveData<Integer> getCancelLiveData() {
        return cancelLiveData;
    }

    public interface Error{
        void onError(String str);
    }

    public void saveWorkInfo(Runnable callback,Error error) {
        setShowProgressBar();
        ArrayMap params = new ArrayMap();
        params.put("industryCode", industryCode);
        params.put("careerCode", TextUtils.isEmpty(careerCode)?"":careerCode);
        params.put("career", TextUtils.isEmpty(careerLiveData.getValue())?"":careerLiveData.getValue());
        params.put("company", companyNameObservable.get());
        params.put("scene", "2");
        Observable<BaseResponse<UpdateComInfoBean>> responseObservable =
                RetrofitManager.getInstance().createApi(MeApi.class).updateComInfo(params);
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<UpdateComInfoBean>(true) {
            @Override
            public void onSuccess(UpdateComInfoBean data) {
                if(data.getStatus() == 2){
                    error.onError(data.getAlertText());
                    PointHelperKt.reportPoint("industry-company-notmatch-intercept", new Function1<PointBean, Unit>() {
                        @Override
                        public Unit invoke(PointBean pointBean) {
                            pointBean.setActionp2(industryLiveData.getValue());
                            pointBean.setActionp3(companyNameObservable.get());
                            return null;
                        }
                    });
                }else {
                    callback.run();
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public boolean isChange() {
        if(getCompanyCertLiveData().getValue() == null){
            return false;
        }
        String industryInit = getCompanyCertLiveData().getValue().getIndustry();
        String careerInit = getCompanyCertLiveData().getValue().getCareer();
        String companyNameInit = getCompanyCertLiveData().getValue().getCompanyName();

        String industryNow = industryLiveData.getValue();
        String careerNow = careerLiveData.getValue();
        String companyNameNow = companyNameObservable.get();

        if(TextUtils.isEmpty(industryNow) || TextUtils.isEmpty(careerNow)){
            return false;
        } else {
            if("自由职业".equals(industryNow) || "在校学生".equals(industryNow)){
                if(TextUtils.isEmpty(industryInit) || TextUtils.isEmpty(careerInit)){
                    //新表单自由职业
                    return true;
                }
                if(industryNow.equals(industryInit)){
                    if(!TextUtils.isEmpty(careerNow) && !careerNow.equals(careerInit)){
                        return true;
                    }else if(!TextUtils.isEmpty(companyNameInit) && !TextUtils.isEmpty(companyNameNow) && !companyNameNow.equals(companyNameInit)){
                        //老的为自由职业 新的还是 没有改变 判断公司有没有改变
                        //公司改了
                        return true;
                    }else if(!TextUtils.isEmpty(companyNameInit) && TextUtils.isEmpty(companyNameNow)){
                        //删了公司
                        return true;
                    }else if(TextUtils.isEmpty(companyNameInit) && !TextUtils.isEmpty(companyNameNow)){
                        //新填写的公司
                        return true;
                    }else {
                        return false;
                    }
                }else {
                    //从别的职业改到自由职业
                    return true;
                }
            }else {
                if(TextUtils.isEmpty(companyNameNow)){
                    return false;
                }
                if(industryNow.equals(industryInit) && careerNow.equals(careerInit) && companyNameNow.equals(companyNameInit)){
                    return false;
                }
                return true;
            }
        }
    }
}
