package com.kanzhun.marry.me.mood.provider

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ui.clickWithTrigger
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.kotlin.ui.recyclerview.BaseListItem
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeItemMoodGirdBinding
import com.kanzhun.marry.me.mood.viewmodel.MoodGridViewModel
import com.kanzhun.marry.me.mood.item.MoodGridItemBean

class MoodGridItemProvider(val activity: FragmentActivity, val viewModel: MoodGridViewModel, val onItemClick: (item: MoodGridItemBean) -> Unit) : BaseItemProvider<BaseListItem, MeItemMoodGirdBinding>() {
    override fun onBindItem(binding: MeItemMoodGirdBinding, item: BaseListItem) {
        if (item is MoodGridItemBean) {
            binding.run {
                val moodBean = item.moodBean
                clMood.apply {
                    if (viewModel.moodBean != null && viewModel.moodBean!!.moodItemId == moodBean.moodItemId) {
                        setBorderColor(activity.getColor(R.color.common_color_292929))
                        setBorderWidth(2.dp.toInt())
                    } else {
                        setBorderWidth(0)
                    }
                }
                ivMood.load(moodBean.moodIcon)
                tvMoodName.text = moodBean.moodTitle
                binding.root.clickWithTrigger {
                    onItemClick(item)
                }
            }
        }

    }
}

