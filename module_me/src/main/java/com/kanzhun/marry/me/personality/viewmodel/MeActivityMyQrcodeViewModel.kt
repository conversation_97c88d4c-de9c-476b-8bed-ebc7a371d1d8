package com.kanzhun.marry.me.personality.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.model.QRBean
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.KMeApi
import io.reactivex.rxjava3.disposables.Disposable

class MeActivityMyQrcodeViewModel : BaseViewModel() {
    var qrBeanLiveData: MutableLiveData<QRBean> = MutableLiveData()
    var sourceType: String? = "" // 二维码生成来源  0 默认 1 活动生成 2 用户问答 3 活动相册
    var timeOutTime: Long = 0L //过期时间
    fun getQRCode(toastErrorMsg: Boolean = true) {
        val userTipsRequest = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestQRCode(sourceType)

        HttpExecutor.execute(
            userTipsRequest,
            object : BaseRequestCallback<QRBean>(toastErrorMsg) {
                override fun onStart(disposable: Disposable?) {
                    super.onStart(disposable)
                }

                override fun onSuccess(data: QRBean) {
                    timeOutTime = data.expireTime ?: 0L
                    qrBeanLiveData.postValue(data)
                }

                override fun dealFail(reason: ErrorReason?) {
                    showError(reason?.errCode, reason?.errReason)
                }
            })
    }
}