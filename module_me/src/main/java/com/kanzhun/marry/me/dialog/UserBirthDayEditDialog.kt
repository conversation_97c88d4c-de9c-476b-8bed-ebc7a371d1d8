package com.kanzhun.marry.me.dialog

import android.text.TextUtils
import com.kanzhun.common.base.AllBaseActivity
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.dialog.CommonBottomViewBindingDialog
import com.kanzhun.common.kotlin.constract.LivedataKeyMe
import com.kanzhun.common.util.DateFormatUtils
import com.kanzhun.common.util.LDate
import com.kanzhun.common.util.liveeventbus.LiveEventBus
import com.kanzhun.common.views.wheel.pick.TimePickerOption
import com.kanzhun.foundation.api.base.URLConfig
import com.kanzhun.foundation.base.BirthYearRangeBean
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.KMeApi
import com.kanzhun.marry.me.databinding.MeBirthdayDialogBinding
import com.kanzhun.utils.T
import java.util.Calendar

class BirthDialogUtil{

    fun showBirthDayDialog(context: AllBaseActivity,initBirthday:String,successRunnable: Runnable? = null, errorRunnable: Runnable? = null) {
        getBrith(context,initBirthday,successRunnable,errorRunnable)
    }

    private fun getBrith(context: AllBaseActivity,initBirthday:String,successRunnable: Runnable? = null, errorRunnable: Runnable? = null) {
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestCommonBrithYearRange()
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<BirthYearRangeBean>(true) {

                override fun onSuccess(data: BirthYearRangeBean) {
                    if(!context.isDestroyed){
                        showDialog(context,data,initBirthday,successRunnable,errorRunnable)
                    }
                }

                override fun dealFail(reason: ErrorReason?) {
                    errorRunnable?.run()
                }

            })
    }

    private fun showDialog(context: AllBaseActivity,data: BirthYearRangeBean,initBirthday:String,successRunnable: Runnable? = null, errorRunnable: Runnable? = null){
        // 初始化出生日期
        var birthdayCalendar: Calendar = Calendar.getInstance()
        try {
            birthdayCalendar.setTime(DateFormatUtils.yMdFormat.parse(initBirthday))
        } catch (e: Exception) {
            birthdayCalendar = LDate.getTextToCalendar(data.defaultDate)
        }

        val timePickerOption = TimePickerOption()
        timePickerOption.label_year = ""
        timePickerOption.label_month = ""
        timePickerOption.label_day = ""
        timePickerOption.date = birthdayCalendar
        timePickerOption.setBirthdayRage(data.minDate,data.maxDate)

        val dialog = CommonBottomViewBindingDialog(context,
            cancelable = false,
            canceledOnTouchOutside = false,
            mOnInflateCallback = { inflater, dialog ->
                val binding = MeBirthdayDialogBinding.inflate(inflater)
                binding.apply {
                    timePickView.setPickerOption(timePickerOption)
                    tvTitle.text = "出生日期"
                    tvCancel.onClick { dialog.dismiss() }
                    tvSure.onClick {
                        val selectedBirthday = DateFormatUtils.getServerDate(timePickerOption.date)
                        if (!TextUtils.isEmpty(selectedBirthday) && !TextUtils.equals(
                                selectedBirthday,
                                initBirthday
                            )
                        ) { // 只有出生日期改变时才更新
                            updateBirthday(selectedBirthday,successRunnable,errorRunnable){
                                dialog.dismiss()
                            }
                        } else {
                            dialog.dismiss()
                        }
                    }
                }
                binding
            })

        dialog.show()
    }

    private fun updateBirthday(birthday: String,successRunnable: Runnable? = null, errorRunnable: Runnable? = null,block:()->Unit) {
        val params: MutableMap<String, Any> = HashMap()
        params["birthday"] = birthday
        HttpExecutor.requestSimplePost(
            URLConfig.URL_ME_BIRTHDAY_UPDATE,
            params,
            object : SimpleRequestCallback() {
                override fun onSuccess() {
                    LiveEventBus.post(LivedataKeyMe.USER_BIRTHDAY_UPDATE,birthday);
                    successRunnable?.run()
                    block()
                }

                override fun dealFail(reason: ErrorReason?) {
                    errorRunnable?.run()
                    T.ss(reason?.errReason)
                }

                override fun onComplete() {
                    super.onComplete()
                }

            })
    }

}



