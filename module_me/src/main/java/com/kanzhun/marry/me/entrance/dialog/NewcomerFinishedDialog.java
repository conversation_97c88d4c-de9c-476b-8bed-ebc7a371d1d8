package com.kanzhun.marry.me.entrance.dialog;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.os.Bundle;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.LinearInterpolator;
import android.widget.FrameLayout;
import android.widget.ImageView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;

import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton;
import com.kanzhun.common.animator.BaseViewAnimator;
import com.kanzhun.common.animator.YoYo;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;
import com.kanzhun.common.dialog.CommonBaseDialog;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.entrance.views.MeFragmentNewcomerItem;
import com.kanzhun.utils.views.OnMultiClickListener;

/**
 * Created by Qu Zhiyong on 2022/8/1
 */
public class NewcomerFinishedDialog extends CommonBaseDialog<NewcomerFinishedDialog.Builder> {

    private QMUIRoundButton lineDark;
    private FrameLayout flBtnDark;
    private ConstraintLayout clBtn;
    private ImageView ivArrow;

    // 线宽、按钮宽度
    private int lineWidth, btnWidth;

    protected NewcomerFinishedDialog(NewcomerFinishedDialog.Builder builder, Context context) {
        super(builder, context, R.style.common_no_title_dialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(builder.getLayoutId());
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT);
        window.getDecorView().setPadding(0, 0, 0, 0);
        QMUIStatusBarHelper.translucent(window, getContext().getResources().getColor(R.color.common_white));
        setCancelable(builder.getCancelable());
        setCanceledOnTouchOutside(builder.getCanceledOnTouchOutside());

        ConstraintLayout clContent = findViewById(R.id.cl_content);
        clContent.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                dismiss();
            }
        });

        MeFragmentNewcomerItem itemCertification = findViewById(R.id.item_certification);
        itemCertification.setDotSelected(false);
        itemCertification.setActionButtonListener(getContext().getString(R.string.me_auth_complete), false, null);

        MeFragmentNewcomerItem itemProfile = findViewById(R.id.item_profile);
        itemProfile.setDotSelected(false);
        itemProfile.setActionButtonListener(getContext().getString(R.string.me_auth_complete), false, null);

        MeFragmentNewcomerItem itemCharacter = findViewById(R.id.item_character);
        itemCharacter.setDotSelected(false);
        itemCharacter.setActionButtonListener(getContext().getString(R.string.me_auth_complete), false, null);

        MeFragmentNewcomerItem itemEducation = findViewById(R.id.item_education);
        itemEducation.setDotSelected(false);
        itemEducation.setActionButtonListener(getContext().getString(R.string.me_auth_complete), false, null);

        setViewClick();

        measureViews(clContent);
    }

    private void measureViews(View root) {
        lineDark = root.findViewById(R.id.btn_line_dark);
        flBtnDark = root.findViewById(R.id.fl_btn_dark);
        clBtn = root.findViewById(R.id.cl_btn);
        ivArrow = root.findViewById(R.id.iv_arrow);

        QMUIRoundButton lineGray = root.findViewById(R.id.btn_line_gray);
        QMUIRoundButton btnGray = root.findViewById(R.id.btn_gray);

        root.post(new Runnable() {
            @Override
            public void run() {
                lineWidth = lineGray.getMeasuredWidth();
                btnWidth = btnGray.getMeasuredWidth();
                // 初始宽度设置为0
                lineDark.setWidth(0);
                lineDark.setVisibility(View.VISIBLE);
                ivArrow.setVisibility(View.VISIBLE);

                startLineAnim();
            }
        });
    }

    private void startLineAnim() {
        ValueAnimator valueAnimator = ValueAnimator.ofFloat(0f, lineWidth);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.setDuration(500);
        valueAnimator.setStartDelay(100);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int width = Math.round((float) animation.getAnimatedValue());
                lineDark.setWidth(width);
                ConstraintLayout.LayoutParams arrowParams = (ConstraintLayout.LayoutParams) ivArrow.getLayoutParams();
                arrowParams.leftMargin = width + QMUIDisplayHelper.dpToPx(5);

                if (width >= lineWidth) {
                    ivArrow.setVisibility(View.GONE);
                    startBtnAnim();
                }
            }
        });
        valueAnimator.start();
    }

    private void startBtnAnim() {
        ConstraintLayout.LayoutParams arrowParams = (ConstraintLayout.LayoutParams) flBtnDark.getLayoutParams();
        arrowParams.width = 0;
        flBtnDark.setVisibility(View.VISIBLE);

        ValueAnimator valueAnimator = ValueAnimator.ofFloat(0f, btnWidth);
        valueAnimator.setInterpolator(new LinearInterpolator());
        valueAnimator.setDuration(300);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                int width = Math.round((float) animation.getAnimatedValue());
                ConstraintLayout.LayoutParams arrowParams = (ConstraintLayout.LayoutParams) flBtnDark.getLayoutParams();
                arrowParams.width = width;

                if (width >= btnWidth) {
                    flBtnDark.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                    startBtnScaleAnim();
                }
            }
        });
        valueAnimator.start();
    }

    private void startBtnScaleAnim() {
        BaseViewAnimator outAnim = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                AnimatorSet animatorSet = getAnimatorAgent();
                animatorSet.play(ObjectAnimator.ofFloat(target, "scaleX", 1, 1.2f, 1, 1.2f, 1, 1.1f, 1).setDuration(2500))
                        .with(ObjectAnimator.ofFloat(target, "scaleY", 1, 1.2f, 1, 1.2f, 1, 1.1f, 1).setDuration(2500));
            }
        };
        YoYo.with(outAnim)
                .interpolate(InterpolatorUtil.createDefaultOvershootInterpolator())
                .withListener(null).playOn(clBtn);
    }

    public static class Builder extends CommonBaseDialog.Builder<NewcomerFinishedDialog.Builder, NewcomerFinishedDialog> {
        protected FragmentActivity activity;
        protected int layoutId;
        protected SparseArray<String> displayTextList = new SparseArray<String>();

        public Builder(FragmentActivity context) {
            super(context);
            this.activity = context;
            this.layoutId = R.layout.me_dialog_newcomer_finished;
            height = WindowManager.LayoutParams.MATCH_PARENT;
            setGravity(Gravity.CENTER);
            setCancelable(false);
            setCanceledOnTouchOutside(false);
        }

        public NewcomerFinishedDialog.Builder setDisplayTextById(int id, String text) {
            this.displayTextList.put(id, text);
            return this;
        }

        @Override
        public NewcomerFinishedDialog createDialog() {
            return new NewcomerFinishedDialog(this, context);
        }

        public FragmentActivity getActivity() {
            return activity;
        }

        public int getLayoutId() {
            return layoutId;
        }
    }
}
