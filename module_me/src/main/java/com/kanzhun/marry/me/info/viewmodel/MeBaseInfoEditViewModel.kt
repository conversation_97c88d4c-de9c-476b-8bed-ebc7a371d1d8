package com.kanzhun.marry.me.info.viewmodel

import androidx.lifecycle.MutableLiveData
import com.kanzhun.common.kotlin.base.BaseViewModel
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.profile.ProfileMetaModel
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.KMeApi

class MeBaseInfoEditViewModel : BaseViewModel()
{

    var listData: MutableLiveData<ProfileMetaModel> = MutableLiveData()

    fun getUserInfo(){
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            KMeApi::class.java
        ).requestProfileMeta(AccountHelper.getInstance().account.userId)
        HttpExecutor.execute(
            baseResponseObservable,
            object : BaseRequestCallback<ProfileMetaModel>() {
                override fun handleInChildThread(data: ProfileMetaModel) {
                    super.handleInChildThread(data)
                }

                override fun onSuccess(data: ProfileMetaModel) {
                    listData.postValue(data)
                }

                override fun dealFail(reason: ErrorReason?) {
                    showError(text = reason?.errReason)
                }

            })

    }
}