package com.kanzhun.marry.me.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ScrollView;

public class MeABScrollView extends ScrollView {

    private boolean mScrollEnable = true;
    public MeABScrollView(Context context) {
        super(context);
    }

    public MeABScrollView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MeABScrollView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public MeABScrollView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public void setScrollEnable(boolean scrollEnable) {
        mScrollEnable = scrollEnable;
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mScrollEnable) {
            return super.onInterceptTouchEvent(ev);
        } else {
            return false;
        }

    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (mScrollEnable) {
            return super.onTouchEvent(ev);
        } else {
            return false;
        }
    }
}
