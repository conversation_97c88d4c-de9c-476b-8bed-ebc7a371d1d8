package com.kanzhun.marry.me.info.bean

import android.view.View
import androidx.annotation.DrawableRes
import com.kanzhun.marry.me.R

open class AboutMeLoveStatusBean(@DrawableRes val pic: Int, val txt: String, val type: Int = 0,var subText:String = "")


class MarryPlanStatus(pic: Int = R.drawable.me_ic_clock, txt: String = "3-5年有结婚计划") :
    AboutMeLoveStatusBean(pic, txt)

class MarryStatus(pic: Int = R.drawable.me_ic_clock, txt: String = "离异") :
    AboutMeLoveStatusBean(pic, txt)


class ChildStatus(pic: Int = R.drawable.me_ic_clock, txt: String = "丁克") :
    AboutMeLoveStatusBean(pic, txt)

class LineStatus(pic: Int = R.drawable.me_ic_clock, txt: String = "3-5年有结婚计划") :
    AboutMeLoveStatusBean(pic, txt, -1)


class MeItem(
    @DrawableRes val pic: Int,
    val leftText: String?,
    val rightText: String?,
    val type: Int = 0,  //0正常状态  1异常状态 2红点状态
    val onClickListener: View.OnClickListener,
    val leftIconUrl:String?,
)