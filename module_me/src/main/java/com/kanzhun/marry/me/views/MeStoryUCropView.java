package com.kanzhun.marry.me.views;

import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.marry.me.R;
import com.yalantis.ucrop.view.MeAvatarOverlayView;

public class MeStoryUCropView extends MeUCropView {
    float radius;
    private boolean isSetupInitialImageTypeByImageMinScale;
    public MeStoryUCropView(@NonNull Context context) {
        super(context);
    }

    public MeStoryUCropView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public MeStoryUCropView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        radius = QMUIDisplayHelper.dp2px(context, 20);
    }

    @Override
    protected void initiateRootViews(View view, int layoutId) {
        super.initiateRootViews(view, R.layout.me_story_ucrop_view);
        getGestureCropImageView().setMaxAspectRatio(new float[] {16f, 9f});
        getGestureCropImageView().setScaleByBiggerSideRatio(false);
        getGestureCropImageView().setCompletionByAlphaTransition(false);
        getGestureCropImageView().setAllowSmaller(true);
        getGestureCropImageView().setIsSetupInitialImageTypeByImageMinScale(isSetupInitialImageTypeByImageMinScale);
    }

    @Override
    protected void processOptions(@NonNull Bundle bundle) {
        super.processOptions(bundle);
        getOverlayView().setCropFrameColor(Color.TRANSPARENT);
        getOverlayView().setShowCropFrame(false);
        getOverlayView().setShowCropGrid(false);
        getOverlayView().setDimmedColor(getResources().getColor(R.color.common_translate));
        setRootViewBackgroundColor(Color.BLACK);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (!((MeAvatarOverlayView) getOverlayView()).isShowCropGrid()) {
                getOverlayView().setShowCropGrid(true);
                getOverlayView().invalidate();
            }
        } else if (event.getAction() == MotionEvent.ACTION_UP) {
            if (((MeAvatarOverlayView) getOverlayView()).isShowCropGrid()) {
                getOverlayView().setShowCropGrid(false);
                getOverlayView().invalidate();
            }
        }
        return super.onInterceptTouchEvent(event);
    }

    public void setSetupInitialImageTypeByImageMinScale(boolean setupInitialImageTypeByImageMinScale) {
        isSetupInitialImageTypeByImageMinScale = setupInitialImageTypeByImageMinScale;
    }
}
