package com.kanzhun.marry.me.util

import com.kanzhun.foundation.kotlin.ktx.saveBoolean
import com.kanzhun.foundation.kotlin.ktx.userSp

/**
 * 认证失败弹窗工具类
 * 用来控制失败提示弹窗的频率
 */
class AuthFailDialogUtil {

    companion object{
        private const val AUTH_FAIL_DIALOG_FREQUENCY_KEY = "AUTH_FAIL_DIALOG_FREQUENCY_KEY_"
        fun show(type:AuthFailDialogType,callback:()->Unit){
            userSp().getBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+type.name,true).apply {
                if(this){
                    callback()
                    userSp().saveBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+type.name,false)
                }
            }

        }

        fun reset(type:AuthFailDialogType){
            userSp().saveBoolean(AUTH_FAIL_DIALOG_FREQUENCY_KEY+type.name,true)
        }
    }


}

enum class AuthFailDialogType {
    CAR_AUTH_FAIL,
    HOUSE_AUTH_FAIL,
    AVATAR_AUTH_FAIL,
    REVENUE_AUTH_FAIL,
    EDU_AUTH_FAIL,
    COMPANY_AUTH_FAIL,
}