package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.content.res.Resources;

import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.identify.model.EducationIdentifyBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/21.
 */
public class MainLandEducationIdentifyChooseViewModel extends FoundationViewModel {
    private List<EducationIdentifyBean> mainLand = new ArrayList<>();
    public String schoolName;
    public int eduLevel;
    public int certStatus;
    public String eduLevelName;
    public int schoolTime;

    public MainLandEducationIdentifyChooseViewModel(Application application) {
        super(application);

    }

    public List<EducationIdentifyBean> getMainLand() {
        return mainLand;
    }
}
