package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.net.Uri;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/6
 */
public class CompanyAuthEmailFadeBackModel extends FoundationViewModel {
    private MutableLiveData<Uri> uploadSuccessLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> successLiveData = new MutableLiveData<>();

    private int errorCount = 0;
    public CompanyAuthEmailFadeBackModel(Application application) {
        super(application);
    }

    public MutableLiveData<Uri> getUploadSuccessLiveData() {
        return uploadSuccessLiveData;
    }

    public MutableLiveData<Boolean> getSuccessLiveData() {
        return successLiveData;
    }

    public void feedBackEmailSuffix(String companyName, String emailSuffix) {
//        setShowProgressBar();
        Map<String, Object> params = new HashMap<>();
        params.put("company", companyName);
        params.put("emailSuffix", emailSuffix);
        HttpExecutor.requestSimplePost(URLConfig.URL_CERT_COMPANY_FEEDBACK_EMAILSUFFIX, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                successLiveData.setValue(true);

            }

            @Override
            public void dealFail(ErrorReason reason) {
            }

            @Override
            public void onComplete() {
                super.onComplete();
//                hideShowProgressBar();
            }
        });
    }


}
