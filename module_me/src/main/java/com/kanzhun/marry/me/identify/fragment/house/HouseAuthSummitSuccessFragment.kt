package com.kanzhun.marry.me.identify.fragment.house

import com.kanzhun.common.kotlin.ext.asBackButton
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.identify.fragment.BaseAuthSummitSuccessFragment

/**
 * <AUTHOR>
 * 房产认证提交成功页面
 */
class HouseAuthSummitSuccessFragment : BaseAuthSummitSuccessFragment() {
    override fun initView() {
        super.initView()
        mBinding.btnNext.asBackButton{
            onClickFinish("好的")
            reportPoint("certify-submit-page-click"){
                actionp2 = "房产"
            }
        }
    }

}

