package com.kanzhun.marry.me.info.fragment;

import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.InputFilter;
import android.text.TextUtils;
import android.view.View;

import com.kanzhun.marry.me.databinding.MeActivityABImpressionContentEditBinding;
import com.kanzhun.marry.me.info.viewmodel.ABImpressionContentEditViewModel;
import com.kanzhun.marry.me.info.callback.ABImpressionContentEditCallback;
import com.kanzhun.utils.rxbus.RxBus;

public class ABImpressionContentEditActivity extends FoundationVMActivity<MeActivityABImpressionContentEditBinding, ABImpressionContentEditViewModel> implements ABImpressionContentEditCallback {
    public static final String TAG = "ABImpressionContentEditFragment";

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_a_b_impression_content_edit;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {

    }

    @Override
    public void clickRight(View view) {

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        Uri uri = intent.getParcelableExtra(ABImpressionEditFragment.AB_EDIT_URI);
        getDataBinding().ivPreview.loadRoundUri(uri);
        getDataBinding().etText.setText(intent.getStringExtra(ABImpressionEditFragment.CONTENT_EDIT_TEXT));
        getDataBinding().etText.requestFocus();
        if (!TextUtils.isEmpty(getDataBinding().etText.getText())) {
            getDataBinding().etText.setSelection(getDataBinding().etText.getText().length());
        }
        QMUIKeyboardHelper.showKeyboard(getDataBinding().etText, 300);
        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(15);
        inputFilters[0] = filter;
        filter.setLengthNoticeFilterOverLimitListener(new LengthNoticeFilter.LengthNoticeFilterOverLimitListener() {
            @Override
            public void onOverLimit() {
                getViewModel().setOverLimit(true);
            }

            @Override
            public void onUnderLimit() {
                getViewModel().setOverLimit(false);
            }
        });
        getDataBinding().etText.setFilters(inputFilters);
    }

    @Override
    public void done() {
        onBackPressed();
    }

    @Override
    public void finish() {
        RxBus.getInstance().post(getViewModel().getText().get(), ABImpressionEditFragment.TAG);
        super.finish();
    }
}