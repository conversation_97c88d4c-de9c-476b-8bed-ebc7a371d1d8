package com.kanzhun.marry.me.identify.viewmodel;

import android.app.Application;
import android.net.Uri;

import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.common.kotlin.constract.LivedataKeyMe;
import com.kanzhun.common.kotlin.ext.LiveEventBusExtKt;
import com.kanzhun.foundation.api.base.URLConfig;
import com.kanzhun.foundation.api.model.ImageUploadModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.utils.UploadFileUtil;
import com.kanzhun.foundation.utils.point.PointBean;
import com.kanzhun.foundation.utils.point.PointHelperKt;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.http.upload.UploadRequestCallback;
import com.kanzhun.marry.me.api.MeApi;
import com.kanzhun.marry.me.api.model.CompanyCertInfoModelNew;
import com.kanzhun.marry.me.point.MePointAction;
import com.kanzhun.marry.me.point.MePointReporter;
import com.kanzhun.utils.T;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/6
 */
public class CompanyAuthWorkCardViewModel extends FoundationViewModel {

    private ObservableField<String> imageUrl = new ObservableField<String>("");
    private String imageToken;
    private MutableLiveData<Uri> uploadSuccessLiveData = new MutableLiveData<>();
    private MutableLiveData<Boolean> successLiveData = new MutableLiveData<>();

    public CompanyAuthWorkCardViewModel(Application application) {
        super(application);
    }

    public MutableLiveData<Uri> getUploadSuccessLiveData() {
        return uploadSuccessLiveData;
    }

    public ObservableField<String> getImageUrl() {
        return imageUrl;
    }

    public void uploadFile(Uri uri) {
        setShowProgressBar();
        UploadFileUtil.uploadImage(UploadFileUtil.AUTH_SOURCE, uri, new UploadRequestCallback<ImageUploadModel>() {
            @Override
            public void onSuccess(ImageUploadModel data) {
                if (data != null && data.originImage != null) {
                    imageUrl.set(data.originImage.url);
                    imageToken = data.token;
                    uploadSuccessLiveData.setValue(uri);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                T.ss(reason.getErrReason());
            }

            @Override
            public void onComplete() {
                super.onComplete();
                hideShowProgressBar();
            }
        });
    }

    public MutableLiveData<Boolean> getSuccessLiveData() {
        return successLiveData;
    }

    private int errorCount;
    public void requestSubmit(String companyName) {
//        setShowProgressBar();
        showEmptyLoading();
        Map<String, Object> params = new HashMap<>();
        params.put("company", companyName);
        params.put("certImageToken", imageToken);
        params.put("type", 2); // 1 社保； 2 工卡 ；3-邮件认证；4-企微/钉钉
        HttpExecutor.requestSimplePost(URLConfig.URL_COMPANY_CERT_SUBMIT, params, new SimpleRequestCallback(true) {
            @Override
            public void onSuccess() {
                successLiveData.setValue(true);
                AccountHelper.getInstance().updateProfile();
                LiveEventBusExtKt.sendStringLiveEvent(LivedataKeyMe.AUTH_WORK_COMMIT_SUCCESS,"",true,true);
                PointHelperKt.reportPoint(MePointAction.CERTIFY_COMPANY_EMPLOYEECARD_CLICK, new Function1<PointBean, Unit>() {
                    @Override
                    public Unit invoke(PointBean pointBean) {
                        pointBean.setActionp2(companyName);
                        pointBean.setType("提交认证");
                        pointBean.setResult("提交成功");
                        return null;
                    }
                });
                showEmptySuccess();
            }

            @Override
            public void dealFail(ErrorReason reason) {
                MePointReporter.reportCertifyCompanyEmployeecardClick(companyName,++errorCount,reason.getErrReason(),"提交认证",reason.getErrReason());
                showEmptySuccess();
            }

            @Override
            public void onComplete() {
                super.onComplete();
//                hideShowProgressBar();

            }
        });
    }

    public MutableLiveData<CompanyCertInfoModelNew> companyLiveData = new MutableLiveData();

    public void getData(){
        Observable<BaseResponse<CompanyCertInfoModelNew>> responseObservable =
                RetrofitManager.getInstance().createApi(MeApi.class).getCompanyCertInfo();
        HttpExecutor.execute(responseObservable, new BaseRequestCallback<CompanyCertInfoModelNew>(true) {
            @Override
            public void onSuccess(CompanyCertInfoModelNew data) {
                companyLiveData.postValue(data);

            }

            @Override
            public void dealFail(ErrorReason reason) {
            }
        });
    }

    public String getCompanyName(){
        if(companyLiveData.getValue() == null)return "";
        return companyLiveData.getValue().getCompanyName();
    }
}
