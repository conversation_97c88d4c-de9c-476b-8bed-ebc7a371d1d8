package com.kanzhun.marry.me.identify.activity

import android.content.Context
import android.content.Intent
import android.view.Gravity
import androidx.fragment.app.Fragment
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.foundation.base.activity.BaseBindingActivity
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.imageviewer.ext.imageViewer
import com.kanzhun.marry.me.BuildConfig
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeActivityHouseAuthBinding
import com.kanzhun.marry.me.databinding.MeHouseAuthFailBottomDialogBinding
import com.kanzhun.marry.me.dialog.MeHouseAuthFailBottomDialog
import com.kanzhun.marry.me.identify.fragment.house.HouseAuthEditFragment
import com.kanzhun.marry.me.identify.fragment.house.HouseAuthExamineFragment
import com.kanzhun.marry.me.identify.fragment.house.HouseAuthSummitSuccessFragment
import com.kanzhun.marry.me.identify.viewmodel.HouseAuthViewModel
import com.kanzhun.marry.me.util.AuthFailDialogType
import com.kanzhun.marry.me.util.AuthFailDialogUtil
import com.kanzhun.marry.me.util.IBackPressFragmentDelegate
import com.techwolf.lib.tlog.TLog


const val HOUSE_AUTH_STATUS_SUBMIT = -1
const val HOUSE_AUTH_STATUS_NONE = 0
const val HOUSE_AUTH_STATUS_EXAMINING = 1
const val HOUSE_AUTH_STATUS_FAIL = 2
const val HOUSE_AUTH_STATUS_SUCCESS = 3

/**
 * <AUTHOR>
 * 房产认证初始页
 */
class HouseAuthActivity : BaseBindingActivity<MeActivityHouseAuthBinding, HouseAuthViewModel>() {

    var fragment: Fragment? = null
    override fun preInit(intent: Intent) {
        mViewModel.houseAuthStatus = intent.getIntExtra(BundleConstants.BUNDLE_DATA_INT, 0)
        //认证成功不允许进来
        if (mViewModel.houseAuthStatus == HOUSE_AUTH_STATUS_SUCCESS) {
            finish()
        }
    }

    override fun initView() {
        mViewModel.showLoading()
    }

    override fun initData() {
        mViewModel.houseAuthStatusLiveData.observe(this) {
            mViewModel.houseAuthStatus = it
            mViewModel.showContent()
            //重新加载fragment
            loadFragment()
        }
        if (mViewModel.houseAuthStatus == HOUSE_AUTH_STATUS_NONE) { //未认证过不可请求接口
            mViewModel.houseAuthStatusLiveData.value = HOUSE_AUTH_STATUS_NONE
        } else {
            mViewModel.getHouseAuthInfo()

        }
    }

    override fun onRetry() {
        mViewModel.getHouseAuthInfo()
    }

    override fun getStateLayout() = mBinding.stateLayout

    private fun loadFragment() {
        //移除原来的fragment
        fragment?.run {
            supportFragmentManager.beginTransaction().remove(this).commitNow()
        }
        //加载新的fragment
        fragment = getCurrentNeedFragment().also {
            supportFragmentManager.beginTransaction().add(R.id.fragmentContainerView, it)
                .commitNow()

        }

    }

    private fun getCurrentNeedFragment(): Fragment {
        return when (mViewModel.houseAuthStatus) {
            //提交成功
            HOUSE_AUTH_STATUS_SUBMIT -> {
                HouseAuthSummitSuccessFragment()
            }

            HOUSE_AUTH_STATUS_EXAMINING -> {
                reportPoint("certify-inreview-page-expo") {
                    actionp2 = "房产"
                }
                if (mViewModel.authDetail != null) {
                    //审核中
                    HouseAuthExamineFragment.newInstance(mViewModel.authDetail!!)
                } else {
                    HouseAuthEditFragment().apply {
                        setPageSource(intent.getSerializableExtra(BundleConstants.BUNDLE_SOURCE_ENUM) as? PageSource?)
                    }
                }
            }

            HOUSE_AUTH_STATUS_FAIL -> {
                // 认证失败
                AuthFailDialogUtil.show(AuthFailDialogType.HOUSE_AUTH_FAIL) {
                    mViewModel.authDetail?.run {
                        if (rejectCode == 30005 || rejectCode == 30007 || rejectCode == 40005) {
                            showAuthFailBottomDialog2()
                        } else {
                            showAuthFailBottomDialog()
                        }
                    }
                }
                HouseAuthEditFragment().apply {
                    setPageSource(intent.getSerializableExtra(BundleConstants.BUNDLE_SOURCE_ENUM) as? PageSource?)
                }
            }

            else -> {
                //未认证
                HouseAuthEditFragment().apply {
                    setPageSource(intent.getSerializableExtra(BundleConstants.BUNDLE_SOURCE_ENUM) as? PageSource?)
                }
            }
        }
    }


    /**
     * 展示认证失败的底部弹框
     */
    private fun showAuthFailBottomDialog() {
        mViewModel.authDetail?.run {
            CommonViewBindingDialog(
                this@HouseAuthActivity,
                mCancelable = true,
                mCanceledOnTouchOutside = true,
                mGravity = Gravity.BOTTOM,
                mPaddingLeft = 0,
                mPaddingRight = 0,
                mAnimationStyle = R.style.common_window_bottom_to_top_anim,
                onInflateCallback = { inflater, dialog ->
                    val binding = MeHouseAuthFailBottomDialogBinding.inflate(inflater)
                    binding.apply {
                        ivHouseAuthPicture.load(imgUrl)
                        tvReason.text = rejectReason
                        btnNext.setOnClickListener {
                            dialog.dismiss()
                            reportPoint("certify-fail-popup-click") {
                                actionp2 = "房产"
                            }
                        }
                        ivHouseAuthPicture.setOnClickListener {
                            ivHouseAuthPicture.imageViewer(imgUrl ?: "")
                        }
                    }
                    binding
                })
                .show()
            reportPoint("certify-fail-popup-expo") {
                actionp2 = "房产"
            }
        }

    }

    private fun showAuthFailBottomDialog2() {
        mViewModel.authDetail?.run {
            MeHouseAuthFailBottomDialog.shouldShow(
                activity = this@HouseAuthActivity,
                rejectReason = rejectReason ?: "",
                rejectCode = rejectCode ?: 0,
                onReAuth = {
                    reportPoint("certify-fail-popup-click") {
                        actionp2 = "房产"
                    }
                })

            reportPoint("certify-fail-popup-expo") {
                actionp2 = "房产"
            }
        }
    }

    @Suppress("OVERRIDE_DEPRECATION")
    override fun onBackPressed() {
        val currentFragment = fragment
        if (currentFragment is IBackPressFragmentDelegate) {
            currentFragment.onBackPress(this)
        } else {
            super.onBackPressed()
        }
    }

    companion object {
        /**
         * @param 1-待审核，2-已驳回，3-已通过，0-未认证 ,-1-提交成功(本地自定义)
         */
        fun intent(context: Context, status: Int, pageSource: PageSource = PageSource.NONE) {
            when (status) {
                HOUSE_AUTH_STATUS_EXAMINING, HOUSE_AUTH_STATUS_FAIL, HOUSE_AUTH_STATUS_NONE -> {
                    Intent(context, HouseAuthActivity::class.java).apply {
                        putExtra(BundleConstants.BUNDLE_DATA_INT, status)
                        putExtra(BundleConstants.BUNDLE_SOURCE_ENUM, pageSource)
                    }.also {
                        context.startActivity(it)
                    }
                }

                else -> {
                    if (BuildConfig.DEBUG) {
                        TLog.info(
                            "CarAuthActivity",
                            "只有审核中、认证失败、未认证状态才能跳转到认证页"
                        )
                    }
                }
            }

        }
    }

}