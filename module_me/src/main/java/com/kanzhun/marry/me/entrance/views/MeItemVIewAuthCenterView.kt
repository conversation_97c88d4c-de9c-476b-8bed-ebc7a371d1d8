package com.kanzhun.marry.me.entrance.views

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.annotation.StringRes
import com.kanzhun.common.kotlin.ext.color
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.textOrGone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MeItemAuthCenterBinding

class MeItemVIewAuthCenterView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    var binding: MeItemAuthCenterBinding

    init {
        var rootView = LayoutInflater.from(context).inflate(R.layout.me_item_auth_center, null)
        binding = MeItemAuthCenterBinding.bind(rootView)
        addView(rootView)
        setState(STATE.DIS_ENABLE)
    }

    fun setDesText(
        @StringRes resId: Int
    ) {
        binding.tvTitleSecond.setText(resId)
    }

    fun setDesText(
        str: String?
    ) {
        binding.tvTitleSecond.textOrGone(if(str.isNullOrEmpty() ) "审核失败" else str)
    }

    fun setState(state: STATE = STATE.IDLE, rejectReason: String = "") {
        binding.apply {
            when (state) {
                STATE.DIS_ENABLE -> {
                    tvTitleSecond.visible()
                    setBtnDisable()
                    btnGo.visible()
                    btnOk.gone()
                    btnOk.setText(R.string.me_cert_pass)
                    idNoApproveIv.visible()
                    idApproveIv.gone()
                    tvTitle.setTextColor(context.color(R.color.common_color_FF858585))
                    tvTitleSecond.setTextColor(context.color(R.color.common_color_FF858585))
                    ivWarn.gone()
                }

                STATE.IDLE -> {
                    tvTitleSecond.visible()
                    setBtnEnable()
                    btnGo.visible()
                    btnOk.setText(R.string.me_cert_pass)
                    btnOk.gone()
                    idNoApproveIv.visible()
                    idApproveIv.gone()
                    tvTitle.setTextColor(context.color(R.color.common_color_FF292929))
                    tvTitleSecond.setTextColor(context.color(R.color.common_color_191919))
                    ivWarn.gone()
                }

                STATE.ING -> {
                    setBtnEnable()
                    btnGo.gone()
                    btnOk.visible()
                    btnOk.setText(R.string.me_school_approve_going)
                    idNoApproveIv.visible()
                    idApproveIv.gone()
                    tvTitle.setTextColor(context.color(R.color.common_color_FF292929))
                    tvTitleSecond.setTextColor(context.color(R.color.common_color_191919))
                    tvTitleSecond.gone()
                    ivWarn.gone()
                }

                STATE.ERROR -> {
                    tvTitleSecond.visible()
                    btnGo.visible()
                    btnOk.setText(R.string.me_cert_pass)
                    btnOk.gone()
                    idNoApproveIv.gone()
                    tvTitle.setTextColor(context.color(R.color.common_color_FF292929))
                    idApproveIv.gone()
                    tvTitleSecond.setTextColor(context.color(R.color.common_color_FFF06A3A))
                    ivWarn.visible()
                    idNoApproveIv.visible()
                    setBtnEnable()
                }

                STATE.DONE -> {
                    setBtnEnable()
                    btnGo.gone()
                    btnOk.visible()
                    btnOk.setText(R.string.me_cert_pass)
                    idNoApproveIv.gone()
                    tvTitle.setTextColor(context.color(R.color.common_color_FF292929))
                    idApproveIv.visible()
                    idApproveIv.setImageResource(R.mipmap.me_img_auth_center_verifierd_done)
                    tvTitleSecond.setTextColor(context.color(R.color.common_color_191919))
                    tvTitleSecond.gone()
                    ivWarn.gone()
                }
            }
        }
    }

    private fun MeItemAuthCenterBinding.setBtnEnable() {
        btnGo.setTextColor(context.color(R.color.common_color_FF292929))
        btnGo.setBackgroundColor(context.color(R.color.common_translate))
        btnGo.setStrokeData(
            1.dp.toInt(), ColorStateList.valueOf(context.color(R.color.common_color_FF292929))
        )
    }

    fun setBtnBlock(){
        binding.apply {
            btnGo.setTextColor(context.color(R.color.common_white))
            btnGo.setBackgroundColor(context.color(R.color.common_color_FF292929))
            btnGo.setStrokeData(
                0, ColorStateList.valueOf(context.color(R.color.common_color_FF292929))
            )
        }
    }

    private fun MeItemAuthCenterBinding.setBtnDisable() {
        btnGo.setTextColor(context.color(R.color.common_color_FFB8B8B8))
        btnGo.setBackgroundColor(context.color(R.color.common_translate))
        btnGo.setStrokeData(
            1.dp.toInt(), ColorStateList.valueOf(context.color(R.color.common_color_FFB8B8B8))
        )
    }

    //初始化文案
    fun setTitleAndDes(@StringRes title: Int, @StringRes des: Int) {
        binding.tvTitle.setText(title)
        binding.tvTitleSecond.setText(des)
    }

    fun setTitleAndDes(title: String, des: String) {
        binding.tvTitle.setText(title)
        binding.tvTitleSecond.setText(des)
    }

    fun setLeftBlue() {
        binding.apply {
            idApproveIv.setImageResource(R.mipmap.me_img_auth_center_verifierd)
            idNoApproveIv.gone()
            idApproveIv.visible()
        }
    }

    enum class STATE {
        DIS_ENABLE,//不可点击的
        IDLE,//没有开始认证的
        ING,//认证中
        ERROR,//认证失败
        DONE,//认证成功
    }

}