package com.kanzhun.marry.me.info.activity;

import android.os.Bundle;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextPaint;
import android.text.method.LinkMovementMethod;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.NonNull;

import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.model.H5Model;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.CertificationIdentifySource;
import com.kanzhun.foundation.utils.ProfileHelper;
import com.kanzhun.marry.me.BR;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.databinding.MeActivityBasicInfoOverEditBinding;
import com.kanzhun.marry.me.info.callback.MeBasicInfoOverEditCallback;
import com.kanzhun.marry.me.info.viewmodel.MeBasicInfoOverEditViewModel;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.sankuai.waimai.router.annotation.RouterUri;

/**
 * 基本信息填写完成页
 *
 * Created by Qu Zhiyong on 2022/5/06
 */
@RouterUri(path = MePageRouter.ME_BASIC_INFO_OVER_ACTIVITY)
public class MeBasicInfoOverEditActivity extends FoundationVMActivity<MeActivityBasicInfoOverEditBinding, MeBasicInfoOverEditViewModel> implements MeBasicInfoOverEditCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.me_activity_basic_info_over_edit;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initView();
    }

    private void initView() {
        getViewModel().index = getIntent().getIntExtra(BundleConstants.BUNDLE_PAGE_INDEX, 0);
        setProtocolText();
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()){
            return;
        }
        ProfileHelper.getInstance().checkJump(MeBasicInfoOverEditActivity.this, getViewModel().index, true);
    }

    @Override
    public void clickLater(View view) {
        if (MultiClickUtil.isMultiClick()){
            return;
        }
        // 关闭新手流程页面
        RxBus.getInstance().post("refresh", CertificationIdentifySource.MAIN_TAB);
    }

    private void setProtocolText() {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder("您填写的信息将严格受到保护，详情查看");
        String protocolString = getResources().getString(R.string.common_profile_info_link);
        int protocolStart = spannableStringBuilder.length();
        int protocolEnd = protocolStart + protocolString.length();
        spannableStringBuilder.append(protocolString);
        spannableStringBuilder.setSpan(new ClickableSpan() {
            @Override
            public void onClick(@NonNull View widget) {
                H5Model.URL_H5_NOTICE.openWithFullScreen(MeBasicInfoOverEditActivity.this);
            }

            @Override
            public void updateDrawState(@NonNull TextPaint ds) {
                super.updateDrawState(ds);
                ds.setColor(getResources().getColor(R.color.common_color_4C9CF8));
                ds.setUnderlineText(false);
                ds.clearShadowLayer();
            }
        }, protocolStart, protocolEnd, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        getDataBinding().tvProtocol.setHighlightColor(getResources().getColor(android.R.color.transparent));
        getDataBinding().tvProtocol.setText(spannableStringBuilder);
        getDataBinding().tvProtocol.setMovementMethod(LinkMovementMethod.getInstance());
    }
}