package com.kanzhun.marry.me.info.viewmodel;

import android.app.Application;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.model.profile.ProfileMetaModel;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.me.R;
import com.kanzhun.marry.me.api.MeApi;

import io.reactivex.rxjava3.core.Observable;

public class MeLikeMeInfoPreviewViewModel extends FoundationViewModel {
    public static final int TYPE_NEXT_TYPE = 1;
    public static final int TYPE_ABOVE_TYPE = 2;
    private String currUserId;
    private int direction = TYPE_NEXT_TYPE;
    private MutableLiveData<Object> loadFinishLiveData = new MutableLiveData<>();

    public MeLikeMeInfoPreviewViewModel(Application application) {
        super(application);
    }

    public String getCurrUserId() {
        return currUserId;
    }

    public void setCurrUserId(String currUserId) {
        this.currUserId = currUserId;
    }

    public int getDirection() {
        return direction;
    }

    public void setDirection(int direction) {
        this.direction = direction;
    }

    public void loadUserInfo() {


    }

    public void loadUserInfoNextAbove() {
        Observable<BaseResponse<ProfileMetaModel>> baseResponseObservable = RetrofitManager.getInstance().createApi(MeApi.class).requestLikeUserInfo(direction, currUserId);
        HttpExecutor.execute(baseResponseObservable, new BaseRequestCallback<ProfileMetaModel>() {
            @Override
            public void onSuccess(ProfileMetaModel data) {
                if (data != null && !TextUtils.isEmpty(data.userId)) {
                    currUserId = data.userId;
                    loadFinishLiveData.setValue(data);
                } else {
                    ErrorReason reason = new ErrorReason(ErrorReason.CODE_DEFAULT, "");
                    if (direction == TYPE_NEXT_TYPE) {
                        reason.setErrReason(getResources().getString(R.string.me_refresh_footer_title_finish));
                    } else if (direction == TYPE_ABOVE_TYPE) {
                        reason.setErrReason(getResources().getString(R.string.me_refresh_header_title_finish));
                    }
                    loadFinishLiveData.setValue(reason);
                }

            }

            @Override
            public void dealFail(ErrorReason reason) {
                loadFinishLiveData.setValue(reason);
            }
        });

    }

    public MutableLiveData<Object> getLoadFinishLiveData() {
        return loadFinishLiveData;
    }

}