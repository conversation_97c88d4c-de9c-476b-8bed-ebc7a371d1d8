package com.kanzhun.marry.me.dialog

import android.view.Gravity
import android.widget.ImageView
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.dialog.CommonViewBindingDialog
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ui.click
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.model.profile.LoveAttitude
import com.kanzhun.marry.me.R
import com.kanzhun.marry.me.databinding.MePreviewLoveAttitudeDialogBinding
import com.kanzhun.marry.me.views.raderview.RadarData
import com.kanzhun.marry.me.views.raderview.RadarView

/**
 *
 * 个人预览页查看恋爱态度弹框（2.5.6 测评结果匹配展示）
 */
class MePreviewLoveAttitudeDialog(val activity: FragmentActivity) {

    val isMan = AccountHelper.getInstance().userInfo?.gender == 1

    val blueStart = R.color.common_color_0080AFFF.toResourceColor()
    val blueEnd = R.color.common_color_CC4789FB.toResourceColor()
    val pinkStart = R.color.common_color_7AFFCCE2.toResourceColor()
    val pinkEnd = R.color.common_color_80ED62A5.toResourceColor()

    fun show(loveAttitude: LoveAttitude) {
        CommonViewBindingDialog(activity,
            mCancelable = false,
            mCanceledOnTouchOutside = false,
            mGravity = Gravity.CENTER,
            mPaddingLeft = 32,
            mPaddingRight = 32,
            onInflateCallback = { inflater, dialog ->
                val binding = MePreviewLoveAttitudeDialogBinding.inflate(inflater)
                binding.apply {
                    tvPercentValue.text = loveAttitude.matchScore?.toString()
                    iconYou.setGenderStyle(isMan)
                    iconTA.setGenderStyle(!isMan)
                    loveAttitude.setRadarData(raderView)
                    ivClose.click {
                        dialog.dismiss()
                    }
                }
                binding
            })
            .show()
    }

    private fun LoveAttitude.setRadarData(radarView: RadarView) {
        val titles = mutableListOf<String>()
        val myScores = mutableListOf<Double>()
        val taScores = mutableListOf<Double>()
        for (item in typeScore.orEmpty()) {
            titles.add(item.name.orEmpty())
            myScores.add(item.myScore * 20)
            taScores.add(item.score * 20)
        }

        radarView.setTitles(titles)
        if (isMan) {
            radarView.setDataList(listOf(RadarData(blueStart, blueEnd, myScores), RadarData(pinkStart, pinkEnd, taScores)))
        } else {
            radarView.setDataList(listOf(RadarData(pinkStart, pinkEnd, myScores), RadarData(blueStart, blueEnd, taScores)))
        }
    }

    private fun ImageView.setGenderStyle(isMan: Boolean) {
        setImageResource(if (isMan) R.mipmap.me_icon_radar_man else R.mipmap.me_icon_radar_woman)
    }
}