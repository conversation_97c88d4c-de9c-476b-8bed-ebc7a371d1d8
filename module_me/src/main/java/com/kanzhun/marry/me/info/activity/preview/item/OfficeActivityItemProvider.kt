package com.kanzhun.marry.me.info.activity.preview.item

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.kotlin.ext.relationStatusStr
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.common.kotlin.ui.recyclerview.BaseItemProvider
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.me.databinding.MePreviewUserOfficeActivityBinding
import com.kanzhun.marry.me.info.viewmodel.MeUserInfoViewModel

/**
 * 官方活动
 */
class OfficeActivityItemProvider(
    val activity: FragmentActivity,
    val viewModel: MeUserInfoViewModel
) : BaseItemProvider<UserPreviewItemBean, MePreviewUserOfficeActivityBinding>() {
    override fun onBindItem(
        binding: MePreviewUserOfficeActivityBinding,
        item: UserPreviewItemBean
    ) {
        binding.run {
            idOfficeActivityText.text = item.data.sameActivity?.showText
            idBtn.onClick {
                ProtocolHelper.parseProtocol(item.data.sameActivity?.url)
                reportPoint("user-detail-same-activity-click") {
                    peer_id = item.data.userId
                    actionp2 = item.data.sameActivity?.activityId.toString()
                    status = AccountHelper.getInstance().phase.toString()
                    friend = item.data.relationStatus.relationStatusStr()
                }
            }
        }
    }
}

