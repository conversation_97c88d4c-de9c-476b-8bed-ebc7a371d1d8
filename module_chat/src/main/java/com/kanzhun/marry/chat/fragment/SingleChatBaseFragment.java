package com.kanzhun.marry.chat.fragment;

import static android.content.Context.VIBRATOR_SERVICE;
import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.getPageSource;
import static com.kanzhun.foundation.kotlin.ktx.BundleExtKt.getPageSourceBundle;
import static com.kanzhun.foundation.model.message.MessageForLikeKt.isLikeMessage;

import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Vibrator;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.CallSuper;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.res.ResourcesCompat;
import androidx.databinding.DataBindingUtil;
import androidx.databinding.ViewDataBinding;
import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.kanzhun.common.base.PageSource;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.common.dialog.CommonSystemBottomDialog;
import com.kanzhun.common.dialog.DialogExtKt;
import com.kanzhun.common.kotlin.entity.BottomListDialogItem;
import com.kanzhun.common.kpswitch.util.KeyboardUtil;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.toast.OToast;
import com.kanzhun.common.util.widget.PopupUtils;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.RequestCodeConstants;
import com.kanzhun.foundation.api.model.MatchingPageInfo;
import com.kanzhun.foundation.imageviewer.ImageViewerBean;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.ReplyBean;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.CommonCardInfo;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForCommonCard;
import com.kanzhun.foundation.model.message.MessageForPic;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.foundation.model.message.MessageForVideo;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.utils.ImageSaveUtil;
import com.kanzhun.foundation.utils.MessageUtils;
import com.kanzhun.foundation.utils.UserReportSource;
import com.kanzhun.imageviewer.ext.ImageViewerViewExtKt;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.activity.ChatBaseActivity;
import com.kanzhun.marry.chat.activity.TopicGameActivity;
import com.kanzhun.marry.chat.callback.ChatOptionCallback;
import com.kanzhun.marry.chat.callback.ItemTypeClick;
import com.kanzhun.marry.chat.callback.ListViewChangeListener;
import com.kanzhun.marry.chat.callback.SingleChatCallback;
import com.kanzhun.marry.chat.databinding.ChatPopCardUnlockBinding;
import com.kanzhun.marry.chat.databinding.ChatViewChatInputBinding;
import com.kanzhun.marry.chat.dialog.ChatDialogExtKt;
import com.kanzhun.marry.chat.dialog.ChatTopicGameDetailDialog;
import com.kanzhun.marry.chat.dialog.DeleteRelationDialog;
import com.kanzhun.marry.chat.dialog.DeleteRelationType;
import com.kanzhun.marry.chat.emotion.data.EmotionItem;
import com.kanzhun.marry.chat.emotion.helper.InnerEmotionMatchHelper;
import com.kanzhun.marry.chat.factory.ItemFactory;
import com.kanzhun.marry.chat.model.ChatClickOptionBean;
import com.kanzhun.marry.chat.model.ImageViewerResult;
import com.kanzhun.marry.chat.point.ChatPointReporter;
import com.kanzhun.marry.chat.util.ChatAudioManager;
import com.kanzhun.marry.chat.util.PlayWrapper;
import com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel;
import com.kanzhun.marry.chat.views.ChatOptionPopupWindow;
import com.kanzhun.marry.chat.voice.OVoicePopup;
import com.kanzhun.utils.SettingBuilder;
import com.kanzhun.utils.T;
import com.kanzhun.utils.ToolsUtils;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.rxbus.RxBus;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.techwolf.lib.tlog.TLog;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by guofeng
 * on 2019/3/12.
 */
public abstract class SingleChatBaseFragment<D extends ViewDataBinding, M extends ChatBaseViewModel> extends InputChatFragment<D, M>
        implements SingleChatCallback, ItemTypeClick, ListViewChangeListener {
    private static final String TAG = "SingleChatBaseFragment";

    protected int userType;
    protected String userId;
    protected String nickName;
    protected PageSource pageSource = PageSource.NONE;
    protected String avatar;
    protected String moodIcon;
    protected String moodTitle;
    protected long systemId;
    protected OVoicePopup oVoicePopup;
    protected PlayWrapper mPlayWrapper = new PlayWrapper();
    long lastVoiceDown = 0;
    protected boolean isChatOptionShow = false;

    public String getUserId() {
        return userId;
    }

    /**
     * 构建bundle
     *
     * @param userId
     * @return
     */
    public static Bundle getBundle(String userId, String nickName, String avatar, boolean friend, String moodIcon, String moodTitle, long systemId, PageSource pageSource) {
        Bundle bundle = getPageSourceBundle(pageSource);
        bundle.putString("userId", userId);
        bundle.putString("nickName", nickName);
        bundle.putString("avatar", avatar);
        bundle.putBoolean("friend", friend);
        bundle.putString("moodIcon", moodIcon);
        bundle.putString("moodTitle", moodTitle);
        bundle.putLong(BundleConstants.BUNDLE_SYSTEM_ID, systemId);
        return bundle;
    }

    private void initParams() {
        Bundle bundle = getArguments();
        userId = bundle.getString("userId", "");
        nickName = bundle.getString("nickName", "");
        avatar = bundle.getString("avatar", "");
        moodIcon = bundle.getString("moodIcon", "");
        moodTitle = bundle.getString("moodTitle", "");
        systemId = bundle.getLong(BundleConstants.BUNDLE_SYSTEM_ID, 0);
        pageSource = getPageSource(bundle);
        boolean friend = bundle.getBoolean("friend", true);
        getViewModel().setIsFriend(friend);
        if (!TextUtils.isEmpty(nickName)) {
            getViewModel().setNickName(nickName);
        }
    }

    protected abstract ChatViewChatInputBinding getViewChatInputBinding();


    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @CallSuper
    protected void onDraftSetDone() {
    }

    @Override
    protected void initFragment() {
        super.initFragment();
        initParams();
        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(500);
        inputFilters[0] = filter;
        getViewChatInputBinding().etInput.setFilters(inputFilters);
        getViewChatInputBinding().etInput.addTextChangedListener(new SingleChatTextWatcher(userId));
        singleChatAudioWatcher = new SingleChatAudioWatcher(userId);
        getViewModel().init(userId, systemId);
        Contact contactSync = ServiceManager.getInstance().getContactService().getContactById(userId);
        if (contactSync != null) {
            userType = contactSync.getUserType();
        } else {
            userType = MessageConstants.MSG_CONTACT_TYPE_USER; //未获取到数据情况下，暂定为普通用户
        }
        initKPSwitchPaneView();
        getViewModel().applyDraft(getViewChatInputBinding().etInput, this::onDraftSetDone);

        ServiceManager.getInstance().getMessageService().getUnreadMessageGetFinish().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean isFinish) {
                if (isFinish) {
                    dismissProgressDialog();
                    ServiceManager.getInstance().getMessageService().setUnreadMessageGetFinish(false);
                }
            }
        });

        ServiceManager.getInstance().getMessageService().getSendMessage().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(@Nullable Boolean send) {
                if (send) {
                    onMessageSendSuccess();
                    clickCloseReply();
                    ServiceManager.getInstance().getMessageService().setSendMessage(false);
                    smoothToBottom();
                }
            }
        });

        ServiceManager.getInstance().getSettingService().getMatchingPageInfoLiveData().observe(this, new Observer<MatchingPageInfo>() {
            @Override
            public void onChanged(MatchingPageInfo info) {
                if (info == null || info.friend == null || TextUtils.isEmpty(info.friend.userId))
                    return;
                // 判断两个人是否是建立关系的两个人
                if (TextUtils.equals(info.friend.userId, userId)) {
                    ChatPageRouter.jumpToMatchingCardActivity(activity, info);
                }
            }
        });

        ServiceManager.getInstance().getConversationService().getCardTipsLiveData().observe(this, new Observer<HashMap<String, Integer>>() {
            @Override
            public void onChanged(HashMap<String, Integer> cardHashMap) {
                if (cardHashMap == null) return;
                if (getViewModel().isMeetingPlan()) return;
                if (cardHashMap.containsKey(userId)) {
                    int cardType = cardHashMap.get(userId);// 1 相识卡，2 表白信，3 语音通话
                    showCardUnlockPop(getViewChatInputBinding().ivMore, cardType);
                    ServiceManager.getInstance().getConversationService().unregisterShowCardTips(userId);
                }
            }
        });

//        getViewModel().getImageExtraLiveData().observe(this, new Observer<ImageExtraBean>() {
//            @Override
//            public void onChanged(ImageExtraBean imageExtraBean) {
//                if (imageExtraBean == null || LList.isEmpty(imageExtraBean.getMultiViewerBeans())) {
//                    return;
//                }
//                DraggableImageViewerHelper.showImagesDraggableParamsInfo(activity, imageExtraBean.getDraggableParamsInfo(),
//                        imageExtraBean.getMultiViewerBeans(), imageExtraBean.getParams().initialIndex,
//                        imageExtraBean.getChatId(), MessageConstants.MSG_SINGLE_CHAT, false);
//
//            }
//        });

        getViewModel().getDelSuccessLiveData().observe(this, new Observer<Boolean>() {
            @Override
            public void onChanged(Boolean aBoolean) {
                AppUtil.finishActivity(activity);
            }
        });

        getViewModel().getCardProcess(null);
        getViewModel().getImagePreviewLiveData().observe(this, new Observer<ImageViewerResult>() {
            @Override
            public void onChanged(ImageViewerResult imageViewerBean) {
                ImageViewerViewExtKt.imageViewer(requireActivity(), imageViewerBean.getList(), imageViewerBean.getPosition(), false);
            }
        });

        getViewModel().getTopicGameClickLiveData().observe(this, new Observer<MessageForTopicGame>() {
            @Override
            public void onChanged(MessageForTopicGame messageForTopicGame) {
                onTopicGameClick(null, messageForTopicGame);
            }
        });

        if (SettingBuilder.getInstance().isDebug()) {
            RxBus.getInstance().subscribe(this, "send", new RxBus.Callback<String>() {
                @Override
                public void onEvent(String s) {
                    getViewChatInputBinding().etInput.setText(s);
                    clickTvSend();
                }
            });
        }
        //告诉服务器，用户进入了聊天页面
        getViewModel().sendEnterChat();
    }


    private void initKPSwitchPaneView() {
//        KeyboardUtil.attach(activity, getPanelRoot(),
//                // Add keyboard showing state callback, do like this when you want to listen in the
//                // keyboard's show/hide change.
//                new KeyboardUtil.OnKeyboardShowingListener() {
//                    @Override
//                    public void onKeyboardShowing(boolean isShowing) {
//                        if (isShowing) {
//                            smoothToBottom();
//                        }
//                    }
//                });
//
//        KPSwitchConflictUtil.attach(getPanelRoot(), getViewChatInputBinding().etInput,
//                new KPSwitchConflictUtil.SwitchClickListener() {
//                    @Override
//                    public void onClickSwitch(View v, boolean switchToPanel) {
//                        ViewGroup.LayoutParams panelRootParams = getPanelRoot().getLayoutParams();
//                        // 加号下的相识卡等布局高度略小
//                        panelRootParams.height = QMUIDisplayHelper.dp2px(activity, v.getId() == R.id.iv_more ? 240 : 255);
//
//                        showPanel(switchToPanel);
//                        if (v.getId() == R.id.iv_more && switchToPanel) {// 加号按钮，且显示控制栏
//                            getViewModel().getCardProcess(new ICommonCallback() {
//                                @Override
//                                public void onSuccess() {
//                                    funcInputMoreAdapter.setList(getViewModel().getPanelFuncs());
//                                }
//                            });
//                            getViewModel().dateLoveCardHasShown = true;
//                        }
//                    }
//                },
//                new KPSwitchConflictUtil.SubPanelAndTrigger(getSubPanelFunc(), getViewChatInputBinding().ivMore),
//                new KPSwitchConflictUtil.SubPanelAndTrigger(getEmojiPanel(), getViewChatInputBinding().ivEmoji));

        getContentRecyclerView().addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    onScrollIdle();
//                    KPSwitchConflictUtil.hidePanelAndKeyboard(getPanelRoot());
                }
                onRecyclerScrollStateChanged(recyclerView, newState);
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                onRecyclerScrolled(recyclerView, dx, dy);
            }
        });
        initFuncInputMoreView();
        initChatInputVoiceView();
    }

    protected void onScrollIdle() {

    }


    protected abstract void smoothToBottom();

    protected abstract void onRecyclerScrolled(RecyclerView recyclerView, int dx, int dy);

    protected abstract void onRecyclerScrollStateChanged(RecyclerView recyclerView, int newState);

    protected abstract ViewDataBinding getLayoutReplyTipsBinding();

    private SingleChatAudioWatcher singleChatAudioWatcher;

    public boolean handleVoiceRecordTouchEvent(MotionEvent event, Context context) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                long current = System.currentTimeMillis();
                if (current - sLastVoiceDown < 1000) {
                    return false;
                }
                if (sOVoicePopup != null && sOVoicePopup.isShowing()) {
                    sOVoicePopup.dismiss();
                }
                sLastVoiceDown = current;
                //每次都创建一个，避免话题游戏的语音干扰RecordManager的回调
                sOVoicePopup = new OVoicePopup(context);
                sOVoicePopup.setMeetingPlan(getViewModel().isMeetingPlan());
                sOVoicePopup.setOnRecordCallBack(new OVoicePopup.OnRecordCallBack() {
                    @Override
                    public void onRecordSuccessListener(String path, long totalTimeMillSecond, int[] waveArray) {
                        //发送语音消息
                        getViewModel().sendAudio(path, (int) (totalTimeMillSecond / 1000),
                                getViewModel().getChatId(), getViewModel().getType(),
                                getViewModel().getReplyBean(), waveArray);
                    }

                    @Override
                    public void onStateChanged(int status) {
                        if(singleChatAudioWatcher != null)singleChatAudioWatcher.onAudioChanged(status);
                        if (status == 0) {
                            getViewChatInputBinding().tvVoiceRecord.setAlpha(0.7f);
                            getViewChatInputBinding().tvVoiceRecord.setText(R.string.chat_record_release_to_send);
                            if (getViewModel().isMeetingPlan()) {
                                getViewChatInputBinding().clInputArea.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.chat_bg_input_dacbff, null));
                            }
                        } else if (status == 1) {
                            getViewChatInputBinding().tvVoiceRecord.setAlpha(0.7f);
                            getViewChatInputBinding().tvVoiceRecord.setText(R.string.chat_record_release_to_cancel);
                            if (getViewModel().isMeetingPlan()) {
                                getViewChatInputBinding().clInputArea.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.chat_bg_input_dacbff, null));
                            }
                        } else if (status == 2) {
                            getViewChatInputBinding().tvVoiceRecord.setAlpha(1f);
                            getViewChatInputBinding().tvVoiceRecord.setText(R.string.chat_record_voice);
                            if (getViewModel().isMeetingPlan()) {
                                getViewChatInputBinding().clInputArea.setBackground(ResourcesCompat.getDrawable(getResources(), R.drawable.chat_bg_input, null));
                            }
                        }
                    }
                });
                sOVoicePopup.responseTouchEvent(event);
                break;
            case MotionEvent.ACTION_MOVE:
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                if (sOVoicePopup != null) {
                    sOVoicePopup.responseTouchEvent(event);
                }
                break;
            default:
                break;
        }
        return true;
    }

    // Add these static variables to the class
    private OVoicePopup sOVoicePopup;
    private long sLastVoiceDown = 0;

    /**
     * 初始化输入语音相关的view
     */
    @SuppressLint("ClickableViewAccessibility")
    private void initChatInputVoiceView() {
        getViewChatInputBinding().tvVoiceRecord.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                return handleVoiceRecordTouchEvent(event, activity);
            }
        });
    }

    private void initFuncInputMoreView() {
        ServiceManager.getInstance().getConversationService().findByUid(getViewModel().getChatId(), MessageConstants.MSG_SINGLE_CHAT)
                .observe(this, new Observer<Conversation>() {
                    @Override
                    public void onChanged(Conversation conversation) {
                        if (conversation != null && conversation.getRelationStatus() > 0) {
                            TLog.print("zl_log", "SingleChatBaseFragment: conversation=%s,", "发生变化，更新好友关系等UI");
                            setUserStatusUI(conversation);
                            getViewModel().setConversation(conversation);
                            boolean isFriend = conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_LIKE_YOU
                                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_FRIEND
                                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_DATING
                                    || conversation.getRelationStatus() == MessageConstants.MATCHING_STATUS_LOVERS;
                            Contact contact = getViewModel().getContact(getViewModel().getChatId());
                            if (contact != null) {
                                isFriend = contact.getStatus() == Constants.CONTACT_STATUS_DELETED ? false : isFriend;
                            }
                            if (!isFriend) {
                                KeyboardUtil.hideKeyboard(getEditText());
                            }
                            getViewModel().setIsFriend(isFriend);
                            if (!isFriend) {
                                clickCloseReply();
                            }
                            onConversationChanged(conversation);

                        }
                    }
                });

    }

    private void startAllTopicActivity() {
        Intent intent = new Intent(activity, TopicGameActivity.class);
        intent.putExtra(BundleConstants.BUNDLE_DATA, getViewModel().getChatId());
        AppUtil.startActivity(activity, intent, ActivityAnimType.UP_GLIDE);
    }

    protected void onConversationChanged(@NonNull Conversation conversation) {

    }

    /**
     * 设置用户状态变化
     */
    protected abstract void setUserStatusUI(Conversation conversation);

    protected abstract void onMessageSendSuccess();

    @Override
    public void clickLeft(View view) {
        QMUIKeyboardHelper.hideKeyboard(getEditText());
        AppUtil.finishActivity(activity);
    }

    @Override
    public void clickRight(View view) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        QMUIKeyboardHelper.hideKeyboard(getEditText());
        showMoreDialog();
    }

    protected void showMoreDialog() {
        Conversation conversation = getViewModel().getConversation();
        int userStatus = getViewModel().getStatus();
        // 关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除
        int relationStatus = conversation != null ? conversation.getRelationStatus() : 0;
        String btnOneTitle = "";
        if (relationStatus == 20) {
            btnOneTitle = getString(R.string.chat_more_cancel_match);
        } else if (relationStatus == 30) {
            if (userStatus != Constants.CONTACT_STATUS_DELETED) {
                btnOneTitle = getString(R.string.chat_more_cancel_date_match);
            } else {
                btnOneTitle = getString(R.string.chat_more_cancel_match);
            }

        } else if (relationStatus == 40) {
            if (userStatus != Constants.CONTACT_STATUS_DELETED) {
                btnOneTitle = getString(R.string.chat_more_cancel_love_match);
            } else {
                btnOneTitle = getString(R.string.chat_more_cancel_match);
            }
        }

        String btnTwoTitle = "";
        if (userStatus != Constants.CONTACT_STATUS_DELETED) {
            btnTwoTitle = getString(R.string.chat_more_report);
        }
        List<BottomListDialogItem> options = new ArrayList<>();
        if (!getViewModel().isMeetingPlan()) {
            options.add(new BottomListDialogItem("清空聊天记录", (s, integer) -> {
                ChatDialogExtKt.showDeleteAllMessagesConfirmDialog(requireActivity(), getViewModel().getNickName(getViewModel().getChatId()), new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        getViewModel().deleteAllMessages();
                        return null;
                    }
                });
                return null;
            }));
        }


        if (!TextUtils.isEmpty(btnOneTitle)) {
            options.add(new BottomListDialogItem(btnOneTitle, (s, integer) -> {
                showSureDeleteConversationDialog(relationStatus);
                return null;
            }));
        }
        if (!getViewModel().isMeetingPlan()) {
            if (!TextUtils.isEmpty(btnTwoTitle)) {
                options.add(new BottomListDialogItem(btnTwoTitle, (s, integer) -> {
                    MePageRouter.jumpToUserReportActivity(SingleChatBaseFragment.this, getViewModel().getChatId(), relationStatus,
                            UserReportSource.SOURCE_CHAR, "", RequestCodeConstants.REQUEST_CODE_0);
                    return null;
                }));
            }
        }

        DialogExtKt.showSimpleBottomListDialog2(requireActivity(), options);
    }

    private void showSureDeleteConversationDialog(int relationStatus) {
        if (relationStatus == 30) {// 解除相识模式
            new DeleteRelationDialog(requireActivity(), DeleteRelationType.DATE).show(confirm -> {
                if (confirm) {
                    getViewModel().endDate();
                }
                return null;
            });
        } else if (relationStatus == 40) {// 解除情侣模式
            new DeleteRelationDialog(requireActivity(), DeleteRelationType.LOVE).show(confirm -> {
                if (confirm) {
                    getViewModel().endLove();
                }
                return null;
            });
        } else {
            new DeleteRelationDialog(requireActivity(), DeleteRelationType.MATCH).show(confirm -> {
                if (confirm) {
                    ChatPointReporter.Companion.unFriendConfirmClick(getViewModel().getChatId(), 1);
                    getViewModel().deleteMatchFriend();
                }
                return null;
            });
        }
    }

    @Override
    public void clickTvSend() {
        String input = getViewChatInputBinding().etInput.getText().toString().trim();
        if (!TextUtils.isEmpty(input)) {
            EmotionItem emotionItem = InnerEmotionMatchHelper.checkAndGetEmotionIfSingle(input);
            if (emotionItem == null) {//不是单个表情包
                getViewModel().sendTextMessage(input, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
            } else {//是单个表情包
                getViewModel().sendEmotionMessage(emotionItem, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
            }
            getViewChatInputBinding().etInput.setText("");
        }
    }

    @Override
    public void clickCloseReply() {
        getViewModel().setReplyBean(null);
        getViewModel().setIsReply(false);
        getLayoutReplyTipsBinding().setVariable(BR.msg, null);
    }


    @Override
    public void onTextLinkClick(String url) {

    }


    @Override
    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_SEND && !TextUtils.isEmpty(v.getText().toString())) {
            getViewModel().sendTextMessage(v.getText().toString(), getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());
            getViewChatInputBinding().etInput.setText("");
            return true;
        }
        return false;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode != Activity.RESULT_OK) {
            return;
        }
        switch (requestCode) {
            case RequestCodeConstants.REQUEST_CODE_0:
                //举报成功
                AppUtil.finishActivity(activity);
                break;
            case RequestCodeConstants.REQUEST_CODE_TAKE_CAMERA:
                String filePath = data.getStringExtra(BundleConstants.BUNDLE_FILE_PATH);
                if (!TextUtils.isEmpty(filePath)) {
                    File sendFile = new File(filePath);
                    //上传图片，视频
                    if (sendFile.exists()) {
                        List<Uri> files = new ArrayList<>();
                        files.add(Uri.fromFile(sendFile));
                        getViewModel().sendPictureMessage(files, getViewModel().getChatId(), getViewModel().getType(), getViewModel().getReplyBean());

                    }
                }
                break;
            default:
                break;
        }
    }


    @Override
    public void onPause() {
        super.onPause();
        ChatAudioManager.getInstance().pause();
        mPlayWrapper.onStop();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        ServiceManager.getInstance().getConversationService().unregisterShowCardTips(getViewModel().getChatId());
        RxBus.getInstance().unregister(this);
    }

    @Override
    public void onClick(View view, ChatMessage chatMessage) {
        ItemFactory.dispatchClick(this, view, chatMessage);
    }

    @Override
    public void onLongClick(View messageView, ChatMessage chatMessage) {
        if (isLikeMessage(chatMessage)) {
            return;
        }

        List<ChatClickOptionBean> options = getViewModel().getChatClickOptions(chatMessage);
        if (LList.isEmpty(options)) {
            return;
        }
        ChatOptionPopupWindow optionPopupWindow = new ChatOptionPopupWindow(activity, messageView);
        optionPopupWindow.setCallback(new ChatOptionCallback() {
            @Override
            public void onChatOptionClick(ChatClickOptionBean chatClickOptionBean) {
                if (chatClickOptionBean.name == R.string.chat_copy) {
                    if (chatMessage instanceof MessageForText) {
                        ToolsUtils.copyText(activity, chatMessage.getContent());
                    }
                } else if (chatClickOptionBean.name == R.string.chat_withdrawn) {
                    if (getViewModel().getIsFriend().get()) {
                        if (System.currentTimeMillis() - chatMessage.getTime() > 120 * 60 * 1000) {
                            T.ss(R.string.chat_msg_over_2_hour);
                            return;
                        }
                        getViewModel().revertMessage(chatMessage);
                        clickCloseReply();
                    }
                } else if (chatClickOptionBean.name == R.string.chat_quote2) {
                    if (getViewModel().getIsFriend().get()) {
                        showReplyTips(chatMessage);
                    }
                } else if (chatClickOptionBean.name == R.string.chat_delete) {
                    if (getViewModel().getIsFriend().get()) {
                        showDeleteConfirmDialog(chatMessage);
                    }
                }
                optionPopupWindow.dismiss();
            }
        });
        optionPopupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                isChatOptionShow = false;
                if (activity instanceof ChatBaseActivity) {
                    ((ChatBaseActivity) activity).setInterceptTouch(false);
                }
            }
        });
        optionPopupWindow.setData(options);
        final int anchorLoc[] = new int[2];
        // 获取锚点View在屏幕上的左上角坐标位置
        messageView.getLocationOnScreen(anchorLoc);
        final int anchorHeight = messageView.getHeight();
        final int anchorCenter = messageView.getWidth() / 2 + anchorLoc[0];
        boolean myMessage = TextUtils.equals(AccountHelper.getInstance().getUserId(), chatMessage.getSender());
        optionPopupWindow.show(myMessage, anchorCenter, anchorLoc[1], anchorHeight);
        Vibrator vibrator = (Vibrator) activity.getSystemService(VIBRATOR_SERVICE);
        vibrator.vibrate(100);
        isChatOptionShow = true;
        if (activity instanceof ChatBaseActivity) {
            ((ChatBaseActivity) activity).setInterceptTouch(true);
        }
    }

    @Override
    public void onResendClick(LottieAnimationView view, ChatMessage chatMessage) {
        view.setEnabled(false);
//        view.setImageResource(R.drawable.chat_ic_icon_send_message);
//        Animation rotateAnimation = AnimationUtils.loadAnimation(view.getContext(), R.anim.chat_anim_circle_rotate);
//        LinearInterpolator interpolator = new LinearInterpolator();
//        rotateAnimation.setInterpolator(interpolator);
//        view.startAnimation(rotateAnimation);
//        view.setVisibility(View.VISIBLE);
        view.setRepeatCount(ValueAnimator.INFINITE);
        view.setAnimation("chat_loading.json");
        view.playAnimation();
        getViewModel().resendMessage(chatMessage);
    }


    @Override
    public void onTextClick(View view, MessageForText messageForText) {
        // TODO: 2022/5/27
//        T.ss(messageForText.getContent());
    }

    @Override
    public void onPicClick(View view, MessageForPic messageForPic) {
//        ImageExtraIndexBean imageExtraIndexBean = new ImageExtraIndexBean("", messageForPic);
//        getViewModel().getContactImages(imageExtraIndexBean, DraggableImageViewerHelper.createImageDraggableParamsWithWHRadio(view));
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
        getViewModel().getPreviewImages(messageForPic, view);
    }

    @Override
    public void onAudio(View view, MessageForAudio messageForAudio) {
        getViewModel().onPlayAudio(messageForAudio).observe(this, status -> {
            messageForAudio.setVoiceStatus(status);
            switch (status) {
                case MessageForAudio.STATUS_VOICE_PLAYING:
                    View llVoice = view.findViewById(R.id.ll_voice);
                    if (llVoice instanceof LinearLayout) {
                        mPlayWrapper.setMessageForAudio(messageForAudio, status, (LinearLayout) llVoice);
                    }
                    break;
                case MessageForAudio.STATUS_VOICE_PAUSE:
                    mPlayWrapper.setPlay(status);
                    break;
                case MessageForAudio.STATUS_VOICE_START:
                    messageForAudio.setReadPercent(0.0f);
                    mPlayWrapper.setPlay(status);
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public void onVideo(View view, MessageForVideo messageForVideo) {
        if (MultiClickUtil.isMultiClick()) {
            return;
        }
//        getViewModel().onVideo(activity, messageForVideo);
        getViewModel().getPreviewImages(messageForVideo, view);
    }

    @Override
    public void onTopicGameClick(View view, MessageForTopicGame messageForTopicGame) {
        boolean isFriend = !MessageUtils.isAuthor(messageForTopicGame);
        if (messageForTopicGame.getTopicGameStatus() == MessageForTopicGame.TopicGameInfo.STATUS_OTHER_NO && isFriend) {
            Intent intent = new Intent(activity, TopicGameActivity.class);
            intent.putExtra(BundleConstants.BUNDLE_DATA, getViewModel().getChatId());
            AppUtil.startActivity(activity, TopicGameActivity.newInstanceForReply((FragmentActivity) activity, messageForTopicGame.getQuestion(), String.valueOf(messageForTopicGame.getMid())),
                    ActivityAnimType.UP_GLIDE);


        } else {
            ChatTopicGameDetailDialog detailDialog = ChatTopicGameDetailDialog.newInstance((FragmentActivity) activity, messageForTopicGame.getTopicGameInfo(), messageForTopicGame.getChatId(), messageForTopicGame.getSender());
            detailDialog.show((FragmentActivity) activity);
        }
    }

    @Override
    public void onTopicGameHintCardClick(View view, MessageForCommonCard messageForCommonCard) {
        if (MultiClickUtil.isMultiClick() || view == null || messageForCommonCard == null) {
            return;
        }
        if (AccountHelper.getInstance().isUserProfileLocked()) {
            T.ss(R.string.chat_user_locked_title);
            return;
        }
        if (view.getId() == R.id.tvAllTopic) {//查看全部话题
            startAllTopicActivity();
            if (messageForCommonCard.getCommonCardInfo() != null) {
                ChatPointReporter.Companion.reportChatTopicGuideClick(messageForCommonCard.getCommonCardInfo().getContent(), "全部话题");
            }
        } else if (view.getId() == R.id.tvSendTopic) {//发送话题
            if (messageForCommonCard.isTopicGameHintCard()) {
                CommonCardInfo topicGameHintCard = messageForCommonCard.getCommonCardInfo();
                if (topicGameHintCard != null) {
                    ChatPointReporter.Companion.reportChatTopicGuideClick(messageForCommonCard.getCommonCardInfo().getContent(), "发送话题");
                    TopicGameActivity.newIntentForDirectSend(requireActivity(), getViewModel().getChatId(), topicGameHintCard.getSourceId());
                }
            }
        }
    }

    @Override
    public void onItemClick(View view, ChatMessage chatMessage, int position) {
        hidePanelAndKeyboard();
    }

    public void hidePanelAndKeyboard() {

    }

    @Override
    public void clickSwitchVoice() {
        hidePanelAndKeyboard();
        if (getViewChatInputBinding().tvVoiceRecord.getVisibility() == View.VISIBLE) {
            getViewChatInputBinding().ivVoice.setImageResource(R.drawable.chat_ic_voice);
            getViewChatInputBinding().tvVoiceRecord.setVisibility(View.GONE);
            getViewChatInputBinding().etInput.setVisibility(View.VISIBLE);

            getViewModel().getVoiceMode().set(false);
        } else {
            getViewChatInputBinding().ivVoice.setImageResource(R.drawable.chat_ic_keyboard);
            getViewChatInputBinding().tvVoiceRecord.setVisibility(View.VISIBLE);
            getViewChatInputBinding().etInput.setVisibility(View.GONE);

            getViewModel().getVoiceMode().set(true);
        }
    }

    public void reversVoiceIcon() {
        getViewChatInputBinding().ivVoice.setImageResource(R.drawable.chat_ic_voice);
        getViewChatInputBinding().tvVoiceRecord.setVisibility(View.GONE);
        getViewChatInputBinding().etInput.setVisibility(View.VISIBLE);

        getViewModel().getVoiceMode().set(false);
    }

    @Override
    public void clickTakePhoto() {

    }

    @Override
    public void clickSelectPhoto() {

    }

    private void showReplyTips(ChatMessage message) {
        getViewModel().setReplyBean(new ReplyBean(message.getMid()));
        getViewModel().setIsReply(true);
        getLayoutReplyTipsBinding().setVariable(BR.msg, message);
        if (!QMUIKeyboardHelper.isKeyboardVisible(activity)) {
            QMUIKeyboardHelper.showKeyboard(getEditText(), true);
        }
    }

    /**
     * 显示删除消息确认弹窗
     *
     * @param message
     */
    private void showDeleteConfirmDialog(ChatMessage message) {
        ChatDialogExtKt.showDeleteMessageConfirmDialog(requireActivity(), new Function0<Unit>() {
            @Override
            public Unit invoke() {
                //确认后删除消息
                getViewModel().deleteMessage(message);
                return null;
            }
        });
    }

    private void showCardUnlockPop(View anchor, int type) {
        ChatPopCardUnlockBinding binding = DataBindingUtil.inflate(LayoutInflater.from(activity), R.layout.chat_pop_card_unlock, null, false);
        binding.setType(type);
        PopupUtils popupUtils = new PopupUtils.Builder(activity).setContentView(binding.getRoot())
                .setWidth(ViewGroup.LayoutParams.WRAP_CONTENT).setHeight(ViewGroup.LayoutParams.WRAP_CONTENT).build();
        View contentView = popupUtils.getPopupWindow().getContentView();
        int[] locations = new int[2];
        anchor.getLocationOnScreen(locations);
        if (locations[1] < QMUIDisplayHelper.getScreenHeight(activity) - QMUIDisplayHelper.dp2px(activity, 100)) {
            showUnlockToast(type);
            // 如果键盘弹起，则不显示
            return;
        }

        contentView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        int offsetX = contentView.getMeasuredWidth() - QMUIDisplayHelper.dp2px(activity, 54) - anchor.getWidth() / 2;
        int x = locations[0] - offsetX;
        int y = locations[1] - contentView.getMeasuredHeight() - QMUIDisplayHelper.dp2px(activity, 16);
        popupUtils.showAtLocation(anchor, Gravity.NO_GRAVITY, x, y);

        anchor.postDelayed(new Runnable() {
            @Override
            public void run() {
                popupUtils.dismiss();
            }
        }, 3000);
    }

    /**
     * @param type // 1 相识卡，2 表白信，3 语音通话
     */
    private void showUnlockToast(int type) {
        if (type == 1) {
            OToast.Companion.show(activity, getString(R.string.chat_date_card_unlock), R.mipmap.chat_ic_date_card_pop, Toast.LENGTH_LONG);
        } else if (type == 2) {
            OToast.Companion.show(activity, getString(R.string.chat_love_card_unlock), R.mipmap.chat_ic_love_card_pop, Toast.LENGTH_LONG);
        } else if (type == 3) {
            OToast.Companion.show(activity, getString(R.string.chat_voice_card_unlock), R.mipmap.chat_ic_voice_talk_pop, Toast.LENGTH_LONG);

        }
    }

    @Override
    public void onDestroyView() {
        String content = getViewChatInputBinding().etInput.getText().toString().trim();
        getViewModel().saveDraft(content);
        super.onDestroyView();
    }

    public void savePicDialog(FragmentActivity fragmentActivity, ImageViewerBean imageViewerBean, int position) {
        CommonSystemBottomDialog.Builder builder = new CommonSystemBottomDialog.Builder(fragmentActivity)
                .setData(getString(R.string.common_save_pic))
                .setOnBottomItemClickListener(new CommonSystemBottomDialog.OnBottomItemClickListener() {
                    @Override
                    public void onBottomItemClick(Dialog dialog, View view, int pos) {
                        dialog.dismiss();
                        List<String> targetImageUrls = imageViewerBean.getTargetImageUrls();
                        if (!LList.isEmpty(targetImageUrls) && targetImageUrls.size() > position) {
                            String url = targetImageUrls.get(position);
                            ImageSaveUtil.saveImageUrlToGallery(fragmentActivity, url);
                        }
                    }

                    @Override
                    public void onCancelClick(Dialog dialog) {

                    }
                });
        builder.create().show();
    }

    @Override
    public void onOpenTopicGameDetail(long msgId) {
        getViewModel().getTopicGameMessage(msgId);
    }
}
