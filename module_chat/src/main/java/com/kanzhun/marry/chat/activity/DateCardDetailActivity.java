package com.kanzhun.marry.chat.activity;

import android.os.Bundle;
import android.text.TextUtils;

import androidx.fragment.app.Fragment;

import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.sankuai.waimai.router.annotation.RouterUri;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.statelayout.StateLayoutManager;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;

import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.marry.chat.databinding.ChatActivityDateCardDetailBinding;
import com.kanzhun.marry.chat.callback.DateCardDetailCallback;
import com.kanzhun.marry.chat.fragment.DateCardFinishedFragment;
import com.kanzhun.marry.chat.fragment.DateCardReceiveFragment;
import com.kanzhun.marry.chat.viewmodel.DateCardDetailViewModel;

/**
 * 消息 - 相识卡详情
 * <p>
 * Created by Qu Zhiyong on 2022/5/26
 */
@RouterUri(path = ChatPageRouter.DATE_CARD_DETAIL_ACTIVITY)
public class DateCardDetailActivity extends FoundationVMActivity<ChatActivityDateCardDetailBinding, DateCardDetailViewModel> implements DateCardDetailCallback {

    private StateLayoutManager stateLayoutManager;

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_date_card_detail;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        QMUIStatusBarHelper.setStatusBarLightMode(this);
        stateLayoutManager = new StateLayoutManager(this, getDataBinding().fragmentContent);

        getViewModel().msgId = getIntent().getStringExtra(BundleConstants.BUNDLE_MSG_ID);
        getViewModel().getDateCard();
        String msgSenderId = getIntent().getStringExtra(BundleConstants.BUNDLE_MSG_SENDER_ID);
        getViewModel().msgSenderId.set(msgSenderId);

        getViewModel().getLoadingLiveData().observe(this, show -> {
            if (show) {
                stateLayoutManager.showLoadingView();
            } else {
                stateLayoutManager.dismiss();
            }
        });
        getViewModel().getDateCardLiveData().observe(this, dateCardStatusBean -> {
            if (dateCardStatusBean == null) return;

            Fragment fragment;
            if (TextUtils.equals(AccountHelper.getInstance().getUserId(), msgSenderId) || dateCardStatusBean.reply > 0) {// 我发的 || 对方已接受或拒绝
                fragment = new DateCardFinishedFragment();
            } else {
                fragment = new DateCardReceiveFragment();
            }
            getSupportFragmentManager().beginTransaction()
                    .replace(R.id.fragment_content, fragment)
                    .commit();
        });

        getViewModel().getFinishLiveData().observe(this, finish -> {
            if (finish) {
                AppUtil.finishActivity(DateCardDetailActivity.this, ActivityAnimType.UP_GLIDE);
            }
        });
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void finish() {
        super.finish();
        AppUtil.finishActivityAnim(this, ActivityAnimType.UP_GLIDE);
    }

}