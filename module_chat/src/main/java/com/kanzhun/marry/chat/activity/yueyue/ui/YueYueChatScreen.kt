package com.kanzhun.marry.chat.activity.yueyue.ui

import android.util.ArrayMap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.viewmodel.compose.viewModel
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.base.compose.ext.SystemBroadcastReceiver
import com.kanzhun.common.base.compose.ext.boldFontFamily
import com.kanzhun.common.base.compose.ext.conditional
import com.kanzhun.common.base.compose.ext.noRippleClickable
import com.kanzhun.common.base.compose.ui.OImageView2
import com.kanzhun.common.base.compose.ui.ptr.OPtrIndicator
import com.kanzhun.common.base.compose.ui.ptr.PullToRefresh
import com.kanzhun.common.base.compose.ui.ptr.rememberPullToRefreshState
import com.kanzhun.common.keyboard.util.UIUtil
import com.kanzhun.common.kotlin.constant.LivedataKeyCommon
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.foundation.api.model.SendLikeModel
import com.kanzhun.foundation.dialog.ComposeDialogFragment
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.Conversation
import com.kanzhun.foundation.model.matching.MatchingLikeModel
import com.kanzhun.foundation.model.message.CertTag
import com.kanzhun.foundation.model.message.MessageForRecommendOpenChat
import com.kanzhun.foundation.model.message.MessageForRecommendUserCard
import com.kanzhun.foundation.model.message.MessageForRecommendWelcome
import com.kanzhun.foundation.model.message.MessageForText
import com.kanzhun.foundation.model.message.MessageForTime
import com.kanzhun.foundation.router.ChatPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.utils.MessageUtils
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.chat.activity.Time
import com.kanzhun.marry.chat.activity.yueyue.YueYueFeedbackDialog
import com.kanzhun.marry.chat.activity.yueyue.YueYueFeedbackSelectDialog
import com.kanzhun.marry.chat.activity.yueyue.YueYueMessageState
import com.kanzhun.marry.chat.activity.yueyue.YueYueRecMsgState
import com.kanzhun.marry.chat.activity.yueyue.YueYueViewModel
import com.kanzhun.marry.chat.api.ChatApi
import com.kanzhun.marry.chat.api.response.YueYueTopCardResponse
import com.kanzhun.utils.T
import com.valentinilk.shimmer.ShimmerBounds
import com.valentinilk.shimmer.rememberShimmer
import com.valentinilk.shimmer.shimmer
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.kanzhun.marry.chat.R as ChatR

/**
 * YueYue Chat Screen - A complete chat interface for the YueYue feature
 *
 * @param yueyueChatId The chat ID for the YueYue conversation
 * @param conversation The conversation object
 * @param yueYueViewModel The YueYueViewModel instance to use for data and state management
 * @param onBackClick Callback for when the back button is clicked
 * @param onSendMessage Callback for when a message is sent
 * @param inPreview Whether the screen is being shown in a preview
 */
@Composable
fun YueYueChatScreen(
    yueyueChatId: String,
    contact: Contact,
    conversation: Conversation,
    yueYueViewModel: YueYueViewModel,
    onBackClick: () -> Unit = {},
    onSendMessage: (String) -> Unit = {},
    onRefresh: () -> Unit = {},
    inPreview: Boolean = false
) {
    // Get the current context to show the dialog
    val context = LocalContext.current
    var dialogFragment: ComposeDialogFragment? = remember { null }

    // Track text field focus state
    var isTextFieldFocused by remember { mutableStateOf(false) }

    // Get the data from the passed ViewModel
    val yueyueTopCardState by yueYueViewModel.yueyueTopCardStateFlow.collectAsState()
    val yueYueMessageState by yueYueViewModel.yueYueMessageStateFlow.collectAsState()
    val recMsgState by yueYueViewModel.recMsgStateFlow.collectAsState()

    // Fetch the YueYue top card data and recommendation messages when the screen is first displayed
    LaunchedEffect(Unit) {
        yueYueViewModel.getYueYueTopCard()
        yueYueViewModel.getYueYueRecMsg()
    }

    SystemBroadcastReceiver(
        systemAction = LivedataKeyCommon.EVENT_KEY_REQUEST_YUEYUE_CARD,
        onSystemEvent = { yueYueViewModel.getYueYueTopCard() }
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = androidx.compose.ui.graphics.Brush.verticalGradient(
                    colors = listOf(
                        Color(0xFFF8E6EE),
                        Color(0xFFFACDD3),
                    )
                )
            )
            .statusBarsPadding()
    ) {
        // Main content
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            val feedbackAction = {
                // Show the YueYueFeedbackDialog when the user clicks on "评价月月"
                dialogFragment = ComposeDialogFragment.shouldShow(
                    activity = context as FragmentActivity,
                    color = ChatR.color.common_color_191919_50,
                    canceledOnTouchOutside = true,
                    cancelable = true
                ) {
                    YueYueFeedbackDialog(
                        onDismiss = {
                            dialogFragment?.dismissAllowingStateLoss()
                        },
                        onSubmit = { overallRating, authenticityRating, communicationRating, comment ->
                            // Handle feedback submission
                            val params = HashMap<String, Any>()
                            params["score"] = overallRating
                            params["recommendSatisfaction"] = authenticityRating
                            params["communicationPleasure"] = communicationRating
                            params["suggestion"] = comment

                            // Make API call to submit feedback
                            HttpExecutor.requestSimplePost(
                                "orange/advice/addFeedBackYueyue",
                                params,
                                object : SimpleRequestCallback(true) {
                                    override fun onSuccess() {
                                        dialogFragment?.dismissAllowingStateLoss()
                                        T.ss("评价提交成功")
                                    }

                                    override fun dealFail(reason: ErrorReason) {
                                        dialogFragment?.dismissAllowingStateLoss()
                                        T.ss(
                                            reason.errReason
                                                ?: "评价提交失败，请稍后重试"
                                        )
                                    }
                                }
                            )
                        }
                    )
                }
            }

            // Toolbar
            ChatToolbar(
                contact = contact,
                onBackClick = onBackClick,
                onMenuClick = {
                    // Show the YueYueFeedbackSelectDialog when menu button is clicked
                    dialogFragment = ComposeDialogFragment.shouldShow(
                        activity = context as FragmentActivity,
                        color = ChatR.color.common_color_191919_50,
                        canceledOnTouchOutside = true,
                        cancelable = true
                    ) {
                        YueYueFeedbackSelectDialog(
                            yueyueChatId = yueyueChatId,
                            conversation = conversation,
                            onFeedback = {
                                dialogFragment?.dismissAllowingStateLoss()

                                feedbackAction()
                            },
                            onDismiss = {
                                dialogFragment?.dismissAllowingStateLoss()
                            }
                        )
                    }
                }
            )

            // Recommendation bar
            RecommendationBar(
                topCardData = yueyueTopCardState,
                isLoading = yueyueTopCardState.todayRecMaxNum == null // Show loading if data is not yet loaded
            )

            // Message list with guide card
            MessageList(
                messageState = yueYueMessageState,
                onRefresh = onRefresh,
                inPreview = inPreview,
                yueYueViewModel = yueYueViewModel,
                isTextFieldFocused = isTextFieldFocused
            )

            // Bottom bar with input and recommendations
            BottomBar(
                recMsgState = recMsgState,
                onSendMessage = onSendMessage,
                onFeedback = feedbackAction,
                onFocusChanged = { focused -> isTextFieldFocused = focused },
                yueYueViewModel = yueYueViewModel
            )
        }
    }
}

/**
 * Toolbar component for the chat screen
 */
@Composable
private fun ChatToolbar(
    contact: Contact,
    onBackClick: () -> Unit,
    onMenuClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // Back button
        Image(
            painter = painterResource(id = ChatR.drawable.common_ic_black_back),
            contentDescription = "Back",
            modifier = Modifier
                .size(24.dp)
                .noRippleClickable { onBackClick() }
        )

        Spacer(modifier = Modifier.width(12.dp))

        Image(
            painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yueyue_icon),
            contentDescription = "image description",
            modifier = Modifier.size(40.dp),
        )

        Spacer(modifier = Modifier.width(8.dp))

        // Title
        Text(
            text = contact.nickName ?: "专属小红娘月月",
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF191919),
            ),
            modifier = Modifier
                .weight(1f)
        )

        // Menu button
        Image(
            painter = painterResource(id = ChatR.drawable.common_ic_icon_chat_setting),
            contentDescription = "Menu",
            modifier = Modifier
                .size(24.dp)
                .noRippleClickable { onMenuClick() }
        )
    }
}

/**
 * Personalized recommendation section below the toolbar
 *
 * @param topCardData Data from the YueYue top card API
 * @param isLoading Whether the data is currently loading
 */
@Composable
private fun RecommendationBar(
    topCardData: YueYueTopCardResponse = YueYueTopCardResponse(),
    isLoading: Boolean = false
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        // Title with icon
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF9D2DB), shape = RoundedCornerShape(12.dp))
                .padding(horizontal = 16.dp, vertical = 14.dp)
        ) {
            // Recommendation icon
            Image(
                painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yueyue_rec),
                contentDescription = "Recommendations",
                modifier = Modifier.size(20.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Title text
            Text(
                text = "今日月月专属推荐",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF292929),
                ),
                modifier = Modifier.weight(1f)
            )

            if (isLoading) {
                // Show loading indicator
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    color = Color(0xFF292929),
                    strokeWidth = 2.dp
                )
            } else {
                // Calculate remaining recommendations
                val remainingRecs =
                    (topCardData.todayRecMaxNum ?: 3) - (topCardData.todayRecNum ?: 0)
                val displayRecs = if (remainingRecs < 0) 0 else remainingRecs

                Text(
                    text = "剩余${displayRecs}人",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF292929),
                    )
                )
            }
        }
    }
}

/**
 * Message list component with a guide card at the top
 */
@Composable
private fun ColumnScope.MessageList(
    messageState: YueYueMessageState,
    onRefresh: () -> Unit = {},
    inPreview: Boolean = false,
    yueYueViewModel: YueYueViewModel = viewModel(),
    isTextFieldFocused: Boolean = false
) {
    val listState = rememberLazyListState()

    // Track previous message size to handle scrolling after refresh
    var prevMessageSize by remember { mutableIntStateOf(0) }

    // Get keyboard information
    val imeVisible = WindowInsets.ime.getBottom(LocalDensity.current) > 0
    // Scroll to bottom when keyboard appears or when text field is focused
    LaunchedEffect(imeVisible, isTextFieldFocused) {
        if (imeVisible && isTextFieldFocused && messageState.chatMessages.isNotEmpty()) {
            // Small delay to let the keyboard animation start
            delay(300)
            listState.animateScrollToItem(messageState.chatMessages.size - 1)
        }
    }

    // Handle scrolling after refresh or new messages
    LaunchedEffect(messageState.chatMessages) {
        try {
            if (messageState.isPullToRefreshData) {
                // If pull-to-refresh, scroll to show new messages
                val scrollPosition = (messageState.chatMessages.size - prevMessageSize + 1)
                    .coerceAtMost(messageState.chatMessages.size - 1)
                    .coerceAtLeast(0)

                listState.scrollToItem(scrollPosition)
                messageState.isPullToRefreshData = false
            } else if (messageState.chatMessages.size > prevMessageSize) {
                // If new messages arrived and the list was already at the bottom, scroll to the bottom
                listState.scrollToItem(messageState.chatMessages.size - 1)
            }
            // If not at bottom, do nothing to maintain current scroll position
        } catch (_: Throwable) {
            // Handle any scrolling errors
        }
    }

    // Update previous message size
    LaunchedEffect(messageState) {
        prevMessageSize = messageState.chatMessages.size
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1f)
    ) {
        // Wrap LazyColumn with PullToRefresh
        PullToRefresh(
            state = rememberPullToRefreshState(isRefreshing = messageState.isRefreshing),
            onRefresh = onRefresh,
            indicator = { state, refreshTriggerDistance, _ ->
                // Using BLACK color for better visibility against the light background of YueYue chat
                OPtrIndicator(
                    refreshTriggerDistance = refreshTriggerDistance,
                    state = state,
                    color = android.graphics.Color.BLACK
                )
            }
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                contentPadding = PaddingValues(
                    top = 16.dp,
                    bottom = 16.dp
                ),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                reverseLayout = false
            ) {
                // Messages
                if (messageState.chatMessages.isNotEmpty()) {
                    items(messageState.chatMessages) { message ->
                        // Render different message types based on the message class
                        when (message) {
                            is MessageForRecommendWelcome -> {
                                // Render welcome message as a guide card
                                GuideCard(welcomeMessage = message)
                            }

                            is MessageForRecommendUserCard -> {
                                // Render recommended user card
                                RecommendedGuestCardContent(
                                    userCard = message,
                                    inPreview = inPreview,
                                    yueYueViewModel = yueYueViewModel
                                )
                            }

                            is MessageForRecommendOpenChat -> {
                                GreetingCard(openChatMessage = message)
                            }

                            is MessageForText -> {
                                // Render text messages as bot or user messages
                                if (!MessageUtils.isAuthor(message)) {
                                    BotMessageItem(text = message.content)
                                } else {
                                    UserMessageItem(text = message.content)
                                }
                            }

                            is MessageForTime -> {
                                Time(
                                    messageForTime = message,
                                    textColor = Color(0xFF918084),
                                    modifier = Modifier.padding(vertical = 8.dp)
                                )
                            }

                            else -> {
                                // Fallback for other message types
                                if (!MessageUtils.isAuthor(message)) {
                                    BotMessageItem(text = message.content)
                                } else {
                                    UserMessageItem(text = message.content)
                                }
                            }
                        }
                    }
                }

//                // Matching status card
//                item {
//                    MatchingStatusCard()
//                }
//
//                // Preference summary card
//                item {
//                    PreferenceSummaryCard()
//                }

//                // Recommended guest card
//                item {
//                    // In a real app, you would control the loading state with a ViewModel
//                    // For demo purposes, we're using a remembered state
//                    var isLoading by remember { mutableStateOf(true) }
//
//                    // Auto-switch to loaded state after a delay (for demo purposes)
//                    LaunchedEffect(Unit) {
//                        kotlinx.coroutines.delay(2000)
//                        isLoading = false
//                    }
//
//                    RecommendedGuestCard(inPreview = inPreview, isLoading = isLoading)
//                }

                // Bottom spacing
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }
}

/**
 * Guide card at the top of the message list
 *
 * @param welcomeMessage Optional MessageForRecommendWelcome to display custom content
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun GuideCard(welcomeMessage: MessageForRecommendWelcome? = null) {
    val recommendWelcome = welcomeMessage?.recommendWelcome
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(4.dp, 20.dp, 20.dp, 20.dp))
            .background(Color(0xFFFFFFFF))
            .padding(16.dp)
    ) {
        // Header with image and title
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            // Title text
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = "Hi~我是月月",
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF000000),
                    )
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "你的专属小红娘",
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(600),
                        fontFamily = boldFontFamily(),
                        color = Color(0xFF191919),
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                Spacer(
                    modifier = Modifier
                        .border(width = 0.5.dp, color = Color(0xFF191919))
                        .padding(1.dp)
                        .width(168.dp)
                        .height(0.dp)
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            // Intro image
            Image(
                painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yueyue_intro),
                contentDescription = "月月介绍",
                modifier = Modifier.size(97.dp)
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Description text
        val content = recommendWelcome?.content ?: ""
        Text(
            text = content.ifEmpty { "专门帮你在茫茫人海里捞那个\"对的人\"\n直接告诉我你的理想型，月月秒速启动\"丘比特模式\"！" },
            style = TextStyle(
                fontSize = 16.sp,
                lineHeight = 24.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF191919),
            )
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Use suggested messages from the welcome message if available, otherwise show default suggestions
        val suggestMsgList = recommendWelcome?.suggestMsgList
        if (!suggestMsgList.isNullOrEmpty()) {
            Text(
                text = "试试这样说：",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    fontFamily = boldFontFamily(),
                    color = Color(0xFF191919),
                )
            )

            Spacer(modifier = Modifier.height(12.dp))

            Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                suggestMsgList.forEach { suggestMsg ->
                    GuideContent(content = suggestMsg)
                }
            }
        }
    }
}

@Composable
fun GuideContent(modifier: Modifier = Modifier, content: String) {
    Row {
        Spacer(
            modifier = modifier
                .padding(top = 8.dp)
                .width(5.dp)
                .height(5.dp)
                .background(color = Color(0xFF7F7F7F), shape = CircleShape)
        )

        Spacer(modifier = Modifier.width(6.dp))

        Text(
            text = content,
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            )
        )
    }
}

/**
 * Matching status card that displays the current matching status
 */
@Suppress("unused")
@Composable
private fun MatchingStatusCard() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    ) {
        // Header with title and status
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(18.dp),
                strokeWidth = 3.dp,
                color = Color(0xFF191919),
                trackColor = Color(0xFFFCF2F7),
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Title
            Text(
                text = "好的，正在为你寻找匹配对象...🚀",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF191919),
                )
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Matching progress
        ConstraintLayout(
            modifier = Modifier.fillMaxWidth()
        ) {
            val (indicator, prompt) = createRefs()

            Spacer(
                modifier = Modifier
                    .width(2.dp)
                    .background(color = Color(0xFF292929), shape = RoundedCornerShape(4.dp))
                    .constrainAs(indicator) {
                        start.linkTo(parent.start)
                        top.linkTo(prompt.top)
                        bottom.linkTo(prompt.bottom)

                        width = Dimension.value(2.dp)
                        height = Dimension.fillToConstraints
                    })

            // Progress description
            Text(
                text = "根据你的要求，身高168左右，40岁以下，北京户口的要求，搜索平台多有符合条件的异性，筛选出20位符合条件的用户，经过匹配给你推荐用户。",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF5E5E5E),
                ),
                modifier = Modifier.constrainAs(prompt) {
                    start.linkTo(indicator.end, margin = 14.dp)
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    end.linkTo(parent.end)

                    width = Dimension.fillToConstraints
                }
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier
                .background(color = Color(0x80FFFFFF), shape = RoundedCornerShape(20.dp))
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yue_yue_stop),
                contentDescription = "image description",
                modifier = Modifier
                    .width(18.dp)
                    .height(18.dp)
            )

            Spacer(modifier = Modifier.width(4.dp))

            Text(
                text = "停止生成",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF292929),
                )
            )
        }
    }
}

/**
 * Preference summary card that displays the user's dating preferences
 */
@Suppress("unused")
@Composable
private fun PreferenceSummaryCard() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(4.dp, 20.dp, 20.dp, 20.dp))
            .background(Color(0xFFFFFFFF))
            .padding(16.dp)
    ) {
        // Title
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Image(
                painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yue_yue_pref),
                contentDescription = "image description",
                modifier = Modifier
                    .width(20.dp)
                    .height(20.dp)
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = "帮你总结一下择偶偏好哦~",
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 22.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF191919),
                )
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Your information section
        Text(
            text = "你的情况：",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF191919),
            )
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = "性别：男\n年龄：32岁\n学历：本科",
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            )
        )

        Spacer(modifier = Modifier.height(10.dp))

        // Partner preferences section
        Text(
            text = "期望对方：",
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF191919),
            )
        )

        Spacer(modifier = Modifier.height(10.dp))

        // Age range
        Text(
            text = "年龄范围：25-33岁",
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            )
        )

        Spacer(modifier = Modifier.height(10.dp))

        // Description
        Text(
            text = "看来还差一些关键信息呢~ 对TA的性格有什么偏好吗？比如阳光开朗型还是温柔体贴型？🌸或者有其他特别在意的点吗？💖",
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 22.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF191919),
            )
        )
    }
}

/**
 * Bot message item with text selection functionality
 */
@Composable
private fun BotMessageItem(text: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Start
    ) {
        // Message bubble
        Box(
            modifier = Modifier
                .weight(1f, false)
                .clip(RoundedCornerShape(4.dp, 20.dp, 20.dp, 20.dp))
                .background(Color(0xFFFFFFFF))
                .padding(12.dp)
        ) {
            // Wrap text in SelectionContainer to enable system text selection
            SelectionContainer {
                Text(
                    text = text,
                    color = Color(0xFF191919),
                    fontSize = 16.sp
                )
            }
        }

        // Space for alignment
        Spacer(modifier = Modifier.weight(0.2f))
    }
}

/**
 * User message item with text selection functionality
 */
@Composable
private fun UserMessageItem(text: String) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.End
    ) {
        // Space for alignment
        Spacer(modifier = Modifier.weight(0.2f))

        // Message bubble
        Box(
            modifier = Modifier
                .clip(
                    RoundedCornerShape(
                        topStart = 20.dp,
                        topEnd = 4.dp,
                        bottomStart = 20.dp,
                        bottomEnd = 20.dp
                    )
                )
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFFFC7D0),
                            Color(0xFFFF94A5),
                        )
                    )
                )
                .padding(start = 16.dp, top = 11.dp, end = 16.dp, bottom = 11.dp)
        ) {
            // Wrap text in SelectionContainer to enable system text selection
            SelectionContainer {
                Text(
                    text = text,
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF191919),
                        textAlign = TextAlign.Right,
                    )
                )
            }
        }
    }
}

/**
 * Bottom bar with input field and recommendation buttons
 */
@Composable
private fun BottomBar(
    recMsgState: YueYueRecMsgState,
    onSendMessage: (String) -> Unit,
    onFeedback: () -> Unit = {},
    onFocusChanged: (Boolean) -> Unit = {},
    yueYueViewModel: YueYueViewModel
) {
    var inputText by remember { mutableStateOf(TextFieldValue()) }
    val draftContent by yueYueViewModel.draftStateFlow.collectAsState()
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // Apply draft when component is first created
    LaunchedEffect(Unit) {
        yueYueViewModel.applyDraft {
            // Draft has been applied, no additional action needed
        }
    }

    // Update input text when draft content changes
    LaunchedEffect(draftContent) {
        if (draftContent.isNotEmpty() && inputText.text.isEmpty()) {
            inputText = TextFieldValue(
                text = draftContent,
                selection = androidx.compose.ui.text.TextRange(draftContent.length)
            )
        }
    }

    // Save draft when component is disposed
    DisposableEffect(Unit) {
        onDispose {
            yueYueViewModel.saveDraft(inputText.text.trim())
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .navigationBarsPadding()
            .imePadding()
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        // Recommendation phrases - horizontally scrollable
        if (recMsgState.isLoading) {
            // Show loading state
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .height(36.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    color = Color(0xFF191919),
                    strokeWidth = 2.dp
                )
            }
        } else {
            // Show recommendation phrases in a horizontally scrollable row
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth(),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(recMsgState.recMessages) { phrase ->
                    RecommendButton(
                        text = phrase,
                        modifier = Modifier,
                        onClick = {
                            // Fill input field with recommendation phrase and move cursor to end
                            inputText = TextFieldValue(
                                phrase,
                                selection = androidx.compose.ui.text.TextRange(phrase.length)
                            )
                        }
                    )
                }

                item {
                    RecommendButton(text = "评价月月") {
                        // Hide soft keyboard first, then call onFeedback with delay
                        coroutineScope.launch {
                            // Find the current focused view (TextField) and hide keyboard
                            val activity = context as? FragmentActivity
                            activity?.currentFocus?.let { focusedView ->
                                UIUtil.hideSoftInput(context, focusedView)
                            }
                            // Wait for 400ms before calling onFeedback
                            delay(400)
                            onFeedback()
                        }
                    }
                }
            }
        }

        // Input field
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
        ) {
            // Text input
            TextField(
                value = inputText,
                onValueChange = {
                    inputText = it
                    yueYueViewModel.updateDraftContent(it.text)
                },
                placeholder = {
                    Text(
                        text = "可以和月月说说你的择偶要求么",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF292929),
                        )
                    )
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 68.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .onFocusChanged { focusState ->
                        // Notify parent when focus changes
                        onFocusChanged(focusState.isFocused)
                    },
                colors = TextFieldDefaults.colors(
                    focusedContainerColor = Color(0xFFFFFFFF),
                    unfocusedContainerColor = Color(0xFFFFFFFF),
                    disabledContainerColor = Color(0xFFFFFFFF),
                    focusedIndicatorColor = Color.Transparent,
                    unfocusedIndicatorColor = Color.Transparent,
                    disabledIndicatorColor = Color.Transparent
                ),
                textStyle = TextStyle(
                    fontSize = 16.sp,
                    color = Color(0xFF191919)
                ),
                trailingIcon = {
                    IconButton(onClick = {}) {
                        // Send button Stub
                        Box(
                            modifier = Modifier.fillMaxHeight(),
                            contentAlignment = Alignment.BottomCenter
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(32.dp)
                            )
                        }
                    }
                },
            )

            IconButton(onClick = {}, modifier = Modifier.align(Alignment.BottomEnd)) {
                // Send button
                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .clip(CircleShape)
                        .background(
                            if (inputText.text.isNotEmpty()) Color(0xFFF8E6EE) else Color(
                                0xFFF5F5F5
                            )
                        )
                        .noRippleClickable {
                            if (inputText.text.isNotEmpty()) {
                                onSendMessage(inputText.text)
                                inputText = TextFieldValue()
                                yueYueViewModel.updateDraftContent("")
                            }
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yueyue_send),
                        contentDescription = "Send",
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }

        // Bottom padding for system navigation
        Spacer(modifier = Modifier.height(8.dp))
    }
}

/**
 * Recommendation button in the bottom bar
 */
@Composable
private fun RecommendButton(
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .clip(RoundedCornerShape(84.dp))
            .background(Color(0x80FFFFFF))
            .padding(horizontal = 12.dp, vertical = 8.dp)
            .noRippleClickable { onClick() },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            color = Color(0xFF191919),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

/**
 * Recommended Guest Card that displays a potential match
 */
@Suppress("unused")
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun RecommendedGuestCard(inPreview: Boolean = false, isLoading: Boolean = false) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        if (isLoading) {
            RecommendedGuestCardShimmer()
        } else {
            RecommendedGuestCardContent(inPreview = inPreview)
        }
    }
}

/**
 * Shimmer loading state for the RecommendedGuestCard
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun RecommendedGuestCardShimmer() {
    val shimmer = rememberShimmer(shimmerBounds = ShimmerBounds.View)

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(20.dp))
            .background(Color(0xFFFFFFFF))
            .padding(20.dp)
            .shimmer(shimmer)
    ) {
        // Title section shimmer
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            // Shimmer for title
            Box(
                modifier = Modifier
                    .weight(1f)
                    .height(28.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(0xFFEEEEEE))
            )

            Spacer(modifier = Modifier.width(8.dp))

            // Shimmer for "看ta主页" button
            Box(
                modifier = Modifier
                    .width(60.dp)
                    .height(20.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(0xFFEEEEEE))
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Profile section shimmer
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Shimmer for avatar
            Box(
                modifier = Modifier
                    .size(68.dp)
                    .clip(CircleShape)
                    .background(Color(0xFFEEEEEE))
            )

            Spacer(modifier = Modifier.width(12.dp))

            // Shimmer for name and description
            Column {
                Box(
                    modifier = Modifier
                        .width(120.dp)
                        .height(32.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFFEEEEEE))
                )

                Spacer(modifier = Modifier.height(8.dp))

                Box(
                    modifier = Modifier
                        .width(180.dp)
                        .height(16.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFFEEEEEE))
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Tags section shimmer
        FlowRow(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            maxItemsInEachRow = 5
        ) {
            // Create 5 shimmer tag placeholders
            repeat(5) { index ->
                Box(
                    modifier = Modifier
                        .width(if (index % 2 == 0) 100.dp else 70.dp)
                        .height(24.dp)
                        .clip(RoundedCornerShape(6.dp))
                        .background(Color(0xFFEEEEEE))
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Divider
        HorizontalDivider(
            color = Color(0xFFF5F5F5),
            thickness = 0.5.dp,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Self-introduction section shimmer
        Box(
            modifier = Modifier
                .width(80.dp)
                .height(16.dp)
                .clip(RoundedCornerShape(4.dp))
                .background(Color(0xFFEEEEEE))
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Shimmer for self-introduction content
        Column(verticalArrangement = Arrangement.spacedBy(6.dp)) {
            repeat(2) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(14.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(Color(0xFFEEEEEE))
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.7f)
                    .height(14.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .background(Color(0xFFEEEEEE))
            )
        }
    }

    // Add shimmer for greeting card when loading
    Spacer(modifier = Modifier.height(12.dp))
}

/**
 * Content of the RecommendedGuestCard when data is loaded
 *
 * @param userCard Optional MessageForRecommendUserCard to display user data
 * @param inPreview Whether the component is being shown in a preview
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun RecommendedGuestCardContent(
    userCard: MessageForRecommendUserCard? = null,
    inPreview: Boolean = false,
    yueYueViewModel: YueYueViewModel = viewModel()
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        // Guest card
        val userInfo = userCard?.recommendUserCard?.userInfo
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(20.dp))
                .background(Color(0xFFFFFFFF))
                .padding(20.dp)
        ) {
            // Title section with "推荐嘉宾" and "看ta主页" link
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "推荐嘉宾",
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontFamily = boldFontFamily(),
                        fontWeight = FontWeight(700),
                        color = Color(0xFF292929),
                    ),
                    modifier = Modifier.weight(1f)
                )

                val context = LocalContext.current
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.noRippleClickable {
                        val recommendUserCard = userCard?.recommendUserCard
                        val userId = recommendUserCard?.recommendUserId ?: ""
                        val msgId = userCard?.mid?.toString() ?: ""

                        // Call the API to refresh recommend user message
                        yueYueViewModel.refreshRecommendUserMsg(msgId)

                        MePageRouter.jumpToInfoPreviewActivity(
                            context = context,
                            userId = userId,
                            lid = "",
                            pageSource = PageSource.NONE,
                            securityId = recommendUserCard?.securityId ?: ""
                        )
                    }
                ) {
                    Text(
                        text = "看ta主页",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF0046BD),
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Profile section with avatar, name, and description
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Avatar
                OImageView2(
                    inPreview = inPreview,
                    imageUrl = userInfo?.avatar ?: "",
                    modifier = Modifier
                        .size(68.dp)
                        .clip(CircleShape)
                        .conditional(inPreview || userInfo?.avatar == null) {
                            background(color = Color.LightGray, shape = CircleShape)
                        }
                )

                Spacer(modifier = Modifier.width(12.dp))

                // Name and description
                Column {
                    Text(
                        text = userInfo?.nickName ?: "",
                        style = TextStyle(
                            fontSize = 28.sp,
                            fontFamily = boldFontFamily(),
                            fontWeight = FontWeight(700),
                            color = Color(0xFF292929),
                        )
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = buildAnnotatedString {
                            val age = userInfo?.age?.toString() ?: ""
                            if (age.isNotEmpty()) {
                                append(age)
                                append("岁")
                                withStyle(
                                    SpanStyle(
                                        color = Color(0xFFB8B8B8),
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight(400)
                                    )
                                ) {
                                    append(" | ")
                                }
                            }

                            val height =
                                userInfo?.height?.toString() ?: ""
                            if (height.isNotEmpty()) {
                                append(height)
                                append("cm")
                                withStyle(
                                    SpanStyle(
                                        color = Color(0xFFB8B8B8),
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight(400)
                                    )
                                ) {
                                    append(" | ")
                                }
                            }

                            val career = userInfo?.career ?: ""
                            if (career.isNotEmpty()) {
                                append(career)
                                withStyle(
                                    SpanStyle(
                                        color = Color(0xFFB8B8B8),
                                        fontSize = 14.sp,
                                        fontWeight = FontWeight(400)
                                    )
                                ) {
                                    append(" | ")
                                }
                            }

                            append("${userInfo?.addressLevel1 ?: ""}${userInfo?.addressLevel2 ?: ""}")
                        },
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF292929),
                        )
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Tags section using FlowRow to automatically wrap tags
            FlowRow(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                maxItemsInEachRow = 5 // Adjust based on screen size and tag length
            ) {
                // Use tags from user data if available, otherwise use default tags
                val certTags = userInfo?.certInfo?.certTagList

                if (certTags != null && certTags.isNotEmpty()) {
                    // Display certification tags from user data
                    certTags.forEach { tag ->
                        TagChip(tag = tag)
                    }
                }
            }

            // Self-introduction section
            val intro = userInfo?.intro ?: ""
            if (intro.isNotEmpty()) {
                Spacer(modifier = Modifier.height(16.dp))

                // Divider
                HorizontalDivider(
                    color = Color(0xFFF5F5F5),
                    thickness = 0.5.dp,
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = "自我介绍",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF292929),
                    )
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = intro,
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF7F7F7F),
                    ),
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

/**
 * Tag chip component for displaying user attributes
 */
@Composable
private fun TagChip(item: CertTag) {
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(6.dp))
            .background(Color(0xFFF5F5F5))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    ) {
        if (item.certType == 2 || item.certType == 3) {
            when (item.certStatus) {
                2 -> {
                    binding.ivTagIcon.setImageResource(R.drawable.me_ic_identify_fail_edit)
                }

                3 -> {
                    binding.ivTagIcon.setImageResource(R.drawable.common_icon_home_auth_new)
                }
            }
        }

        Text(
            text = item.content ?: "",
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF7F7F7F),
            )
        )
    }
}

/**
 * Greeting card that appears below the recommended guest card
 */
@Composable
private fun GreetingCard(openChatMessage: MessageForRecommendOpenChat) {
    // Card container
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .background(Color(0xFFFFFFFF))
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Icon
        Image(
            painter = painterResource(id = ChatR.mipmap.chat_ic_ai_yueyue_msg),
            contentDescription = "Chat Icon",
            modifier = Modifier.size(40.dp)
        )

        Spacer(modifier = Modifier.width(8.dp))

        // Title and greeting content
        val recommendOpenChat = openChatMessage.recommendOpenChat
        val sendLikeText = recommendOpenChat?.content ?: ""
        Column(
            modifier = Modifier.weight(1f)
        ) {
            // Title
            Text(
                text = "和Ta打个招呼吧",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF191919),
                )
            )

            Spacer(modifier = Modifier.height(4.dp))

            // Greeting content - simple text display instead of editable field
            Text(
                text = sendLikeText,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF292929),
                )
            )
        }

        Spacer(modifier = Modifier.width(12.dp))

        val context = LocalContext.current

        // Send button
        val relationStatus = recommendOpenChat?.relationStatus ?: 0
        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(18.dp))
                .background(Color(0xFF191919))
                .padding(horizontal = 16.dp, vertical = 10.dp)
                .noRippleClickable {
                    if (relationStatus == 11) {
                        T.ss("已经发送过喜欢啦~")
                    } else {
                        val userId = recommendOpenChat?.recommendUserId ?: ""
                        if (relationStatus == 20) {
                            ChatPageRouter.jumpToSingleChatActivity(
                                context,
                                userId,
                                PageSource.NONE
                            )
                        } else {
                            val sendLikeModel = SendLikeModel(
                                userId,
                                "",
                                "",
                                PageSource.CHAT,
                                emptyList(),
                                relationStatus,
                                "",
                                recommendOpenChat?.securityId ?: "",
                                true,
                                false
                            )

                            val map = ArrayMap<String, Any?>()
                            map["userId"] = sendLikeModel.userId
                            map["resourceId"] = sendLikeModel.resId ?: ""
                            map["resourceType"] = sendLikeModel.resourceType
                            map["likeReason"] = sendLikeText
                            map["subType"] = sendLikeModel.subType.toString()
                            map["pageSource"] = PageSource.CHAT
                            map["encMoodId"] = sendLikeModel.encMoodId ?: ""
                            map["securityId"] = sendLikeModel.securityId
                            val observable =
                                RetrofitManager.getInstance().createApi(ChatApi::class.java)
                                    .sendLike(map)
                            HttpExecutor.execute(
                                observable,
                                object : BaseRequestCallback<MatchingLikeModel?>() {

                                    override fun onSuccess(data: MatchingLikeModel?) {
                                        T.ss("喜欢已送出")
                                    }

                                    override fun dealFail(reason: ErrorReason?) {
                                        T.ss(reason?.errReason)
                                    }
                                })
                        }
                    }
                },
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (relationStatus == 20) "去看看" else "发送喜欢",
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color.White,
                ),
            )
        }
    }
}

/**
 * Preview of the YueYue Chat Screen
 */
@Preview(showBackground = true, heightDp = 2000)
@Composable
private fun PreviewYueYueChatScreen() {
    // Create a mock YueYueViewModel for preview
    val mockYueYueViewModel = viewModel<YueYueViewModel>()

    // Simulate messages
    mockYueYueViewModel.mockYueYueMessages()

    // Simulate recommendation phrases
    mockYueYueViewModel.mockGetYueYueRecMsg()

    // Create a mock Conversation object
    MessageUtils.setCurrentUid("1234567890")

    YueYueChatScreen(
        yueyueChatId = "",
        contact = Contact().apply {
            nickName = "月月"
        },
        conversation = Conversation().apply {
            sort = 1024
            shield = 1
        },
        yueYueViewModel = mockYueYueViewModel,
        inPreview = true
    )
}