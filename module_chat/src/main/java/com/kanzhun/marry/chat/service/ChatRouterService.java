package com.kanzhun.marry.chat.service;

import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.router.service.IChatRouterService;
import com.kanzhun.marry.chat.emotion.data.EmotionDao;
import com.sankuai.waimai.router.annotation.RouterService;

@RouterService(interfaces = IChatRouterService.class, key = ChatPageRouter.CHAT_SERVICE)
public class ChatRouterService implements IChatRouterService {
    @Override
    public void saveEmotionData(String data) {
        EmotionDao.Companion.saveAllDataString(data);
    }
}
