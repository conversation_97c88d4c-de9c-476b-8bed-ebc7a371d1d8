package com.kanzhun.marry.chat.emotion

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.kanzhun.marry.chat.R
import com.kanzhun.marry.chat.emotion.callback.OnLongTouchEmotionCallBack
import com.kanzhun.marry.chat.emotion.data.EmotionItem
import com.kanzhun.utils.base.LList

class ExtendEmotionFragment : Fragment(), OnLongTouchEmotionCallBack {
    private lateinit var mEmotionRecycleView: RecyclerView
    private val emotionList: MutableList<EmotionItem> = ArrayList()
    private var iEmotionCallBack: IEmotionCallBack? = null

    fun setiEmotionCallBack(iEmotionCallBack: IEmotionCallBack?) {
        this.iEmotionCallBack = iEmotionCallBack
    }

    fun setData(data: List<EmotionItem>?) {
        emotionList.clear()
        if (data != null) {
            emotionList.addAll(data)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.twl_ui_group_emotion_container, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mEmotionRecycleView = view.findViewById(R.id.mEmotionRecycleView)
        mEmotionRecycleView.layoutManager = GridLayoutManager(context, 4)

        mEmotionRecycleView.adapter = ExtendEmotionAdapter(context).apply {
            setiEmotionCallBack(iEmotionCallBack)
            setData(emotionList)
        }
    }

    override fun getEmotionBeanByLocation(
        touchX: Float,
        touchY: Float,
        currentItem: Int
    ): EmotionItem? {
        val childViewUnder = mEmotionRecycleView.findChildViewUnder(touchX, touchY)
        if (childViewUnder != null) {
            val position = mEmotionRecycleView.getChildLayoutPosition(childViewUnder)
            return LList.getElement(emotionList, position)
        }
        return null
    }

    override fun getTouchGifRectArea(touchX: Float, touchY: Float, currentItem: Int): Rect? {
        val childViewUnder = mEmotionRecycleView.findChildViewUnder(touchX, touchY) ?: return null
        val rect = Rect()
        val screen = IntArray(2)
        childViewUnder.getLocationOnScreen(screen)
        rect.left = screen[0]
        rect.right = rect.left + childViewUnder.measuredWidth
        rect.top = screen[1]
        rect.bottom = screen[1] + childViewUnder.measuredHeight
        return rect
    }

    companion object {
        fun newInstance(): ExtendEmotionFragment {
            return ExtendEmotionFragment()
        }
    }
}