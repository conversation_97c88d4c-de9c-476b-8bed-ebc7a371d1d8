package com.kanzhun.marry.chat.bindadapter;

import static com.kanzhun.marry.chat.views.MeetingPlanTipKt.bindMeetingPlanTip;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.Drawable;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.compose.ui.platform.ComposeView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.content.ContextCompat;
import androidx.databinding.BindingAdapter;
import androidx.databinding.BindingMethod;
import androidx.databinding.BindingMethods;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.RecyclerView;

import com.airbnb.lottie.LottieAnimationView;
import com.chad.library.BR;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder;
import com.facebook.drawee.view.SimpleDraweeView;
import com.kanzhun.common.bindadapter.CommonBindingAdapters;
import com.kanzhun.common.kotlin.ext.ResourceExtKt;
import com.kanzhun.common.util.DrawableUtil;
import com.kanzhun.common.util.ImageUtils;
import com.kanzhun.common.util.LDate;
import com.kanzhun.common.views.AudioRecorderPlayView;
import com.kanzhun.common.views.RoundProgressView;
import com.kanzhun.common.views.image.OImageView;
import com.kanzhun.common.views.image.imageloader.progress.OnRequestListener;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.bean.ChatTopicGameAnswer;
import com.kanzhun.foundation.api.bean.TopicGameOptionBean;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.kernel.account.Account;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.listener.ChatItemListener;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.foundation.model.User;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForActivityMedia;
import com.kanzhun.foundation.model.message.MessageForAudio;
import com.kanzhun.foundation.model.message.MessageForCommonCard;
import com.kanzhun.foundation.model.message.MessageForDateCard;
import com.kanzhun.foundation.model.message.MessageForEmotion;
import com.kanzhun.foundation.model.message.MessageForFeedbackReplayCard;
import com.kanzhun.foundation.model.message.MessageForHint;
import com.kanzhun.foundation.model.message.MessageForLike;
import com.kanzhun.foundation.model.message.MessageForLinkCall;
import com.kanzhun.foundation.model.message.MessageForLoveCard;
import com.kanzhun.foundation.model.message.MessageForMeetingPlanInviteCard;
import com.kanzhun.foundation.model.message.MessageForMeetingPlanTaskCard;
import com.kanzhun.foundation.model.message.MessageForMeetingProtectCard;
import com.kanzhun.foundation.model.message.MessageForMood;
import com.kanzhun.foundation.model.message.MessageForNotifyCard;
import com.kanzhun.foundation.model.message.MessageForPic;
import com.kanzhun.foundation.model.message.MessageForTacitTestCard;
import com.kanzhun.foundation.model.message.MessageForText;
import com.kanzhun.foundation.model.message.MessageForTime;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.foundation.model.message.MessageForUserCard;
import com.kanzhun.foundation.model.message.MessageForVideo;
import com.kanzhun.foundation.model.message.MoodInfo;
import com.kanzhun.foundation.utils.QMUIDisplayHelperO2;
import com.kanzhun.foundation.utils.StringUtil;
import com.kanzhun.foundation.views.MeABOImageView;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.databinding.ChatItemChatTopicGameOptionBinding;
import com.kanzhun.marry.chat.databinding.ChatViewMessageQuoteContentDeleteBinding;
import com.kanzhun.marry.chat.databinding.ChatViewMessageQuoteContentWithdrawBinding;
import com.kanzhun.marry.chat.factory.ItemFactory;
import com.kanzhun.marry.chat.model.DateCardStatusBean;
import com.kanzhun.marry.chat.point.ChatPointReporter;
import com.kanzhun.marry.chat.util.ChatUtils;
import com.kanzhun.marry.chat.views.MLinkEmotionTextView;
import com.kanzhun.utils.GsonUtils;
import com.kanzhun.utils.base.LList;
import com.kanzhun.utils.views.OnMultiClickListener;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.techwolf.lib.tlog.TLog;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/5/27.
 */
@BindingMethods({@BindingMethod(type = TextView.class, attribute = "movementMethod", method = "setMovementMethod")})
public class ChatBindingAdapter {

    private static final String TAG = "ChatBindingAdapter";

    @BindingAdapter("replyTipsName")
    public static void setReplyName(TextView textView, ChatMessage chatMessage) {
        if (chatMessage != null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("回复");
            if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
                if (user != null) {
                    stringBuilder.append(user.getNickName());
                }
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                if (contact != null) {
                    stringBuilder.append(contact.getNickName());
                }
            }
            stringBuilder.append(": ");
            textView.setText(stringBuilder.toString());
        }
    }

    @BindingAdapter("replyTipsContent")
    public static void setReplyContent(TextView textView, ChatMessage chatMessage) {
        if (chatMessage != null) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(chatMessage.getSummary());
            textView.setText(stringBuilder.toString());
        }
    }

    @BindingAdapter("setLastTime")
    public static void setConversationTime(TextView textView, long time) {
        if (time == 0) {
            textView.setText("");
            return;
        }
        if (LDate.isToday(time)) {
            textView.setText(LDate.getDate(time, "HH:mm"));
            return;
        }
        if (LDate.isYesterday(time)) {
            textView.setText("昨天 " + LDate.getDate(time, "HH:mm"));
            return;
        }
        if (LDate.isSameYear(time)) {
            textView.setText(LDate.getDate(time, "MM月dd日"));
            return;
        }
        textView.setText(LDate.getDate(time, "yyyy年MM月dd日"));
    }

    @BindingAdapter({"singleMsgStatus"})
    public static void setStatus(LottieAnimationView imageView, ChatMessage chatMessage) {
        imageView.setEnabled(false); //默认设置enabled为false，不可点击
        imageView.clearAnimation();
        switch (chatMessage.getStatus()) {
            case MessageConstants.MSG_STATE_SENDING:
                imageView.setVisibility(View.VISIBLE);
                imageView.setRepeatCount(ValueAnimator.INFINITE);
                imageView.setAnimation("chat_loading.json");
                imageView.playAnimation();
//                imageView.setImageResource(R.drawable.chat_ic_icon_send_message);
//                Animation rotateAnimation = AnimationUtils.loadAnimation(imageView.getContext(), R.anim.chat_anim_circle_rotate);
//                LinearInterpolator interpolator = new LinearInterpolator();
//                rotateAnimation.setInterpolator(interpolator);
//                imageView.startAnimation(rotateAnimation);
                break;
            case MessageConstants.MSG_STATE_FAILURE:
                imageView.pauseAnimation();
                imageView.setEnabled(true);
                imageView.setVisibility(View.VISIBLE);
                imageView.setImageResource(R.drawable.chat_ic_icon_message_failure);
                break;
            default:
                imageView.pauseAnimation();
                imageView.setVisibility(View.GONE);
                break;
        }
    }

    @BindingAdapter({"itemBackground"})
    public static void setMessageItemBackgroundWithReply(ViewGroup viewGroup, ChatMessage message) {
        if (message != null && message.getReplyId() > 0) {
            if (TextUtils.equals(message.getSender(), AccountHelper.getInstance().getUserId())) {
                viewGroup.setBackgroundResource(R.drawable.chat_bg_message_to);
            } else {
                viewGroup.setBackgroundResource(R.drawable.chat_bg_message_from);
            }
        } else {
            viewGroup.setBackground(null);
        }
    }

    @BindingAdapter({"itemPadding"})
    public static void setMessageItemPadding(ViewGroup viewGroup, ChatMessage message) {
        if (message != null && message.getReplyId() > 0) {
            int px = QMUIDisplayHelper.dp2px(viewGroup.getContext(), 12);
            viewGroup.setPadding(px, px, px, px);
        } else {
            viewGroup.setPadding(0, 0, 0, 0);
        }
    }

    @BindingAdapter("showVideoScale")
    public static void showVideoScale(OImageView view, MessageForVideo messageForVideo) {
        if (messageForVideo.getVideoInfo() != null && messageForVideo.getVideoInfo().getThumbnail() != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            int maxWidth = QMUIDisplayHelper.dp2px(view.getContext(), 160);
            ImageUtils.ImageSize imageSize = ImageUtils.initVideoImageSize(maxWidth,
                    messageForVideo.getVideoInfo().getThumbnail().getWidth(), messageForVideo.getVideoInfo().getThumbnail().getHeight());
            ViewGroup.LayoutParams layoutParams = parent.getLayoutParams();
            if (imageSize.getWidth() == 0 || imageSize.getHeight() == 0) {
                layoutParams.width = maxWidth;
                layoutParams.height = maxWidth;
            } else {
                layoutParams.width = (int) imageSize.getWidth();
                layoutParams.height = (int) imageSize.getHeight();
            }
            parent.setLayoutParams(layoutParams);
            CommonBindingAdapters.setImageUrl(view, messageForVideo.getVideoInfo().getThumbnail().url);
        }
    }

    @BindingAdapter("showPicScale")
    public static void showPicScale(OImageView view, MessageForPic messageForPic) {
        if (messageForPic.getPicinfo() != null && messageForPic.getOrigin() != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            int maxWidth = QMUIDisplayHelper.dp2px(view.getContext(), 160);
            int minWidth = QMUIDisplayHelper.dp2px(view.getContext(), 60);
            ImageUtils.ImageSize imageSize = ImageUtils.initChatImageSize(maxWidth, minWidth,
                    messageForPic.getOrigin().width, messageForPic.getOrigin().height);
            ViewGroup.LayoutParams layoutParams = parent.getLayoutParams();
            if (imageSize.getWidth() == 0 || imageSize.getHeight() == 0) {
                layoutParams.width = maxWidth;
                layoutParams.height = maxWidth;
            } else {
                layoutParams.width = (int) imageSize.getWidth();
                layoutParams.height = (int) imageSize.getHeight();
            }
            parent.setLayoutParams(layoutParams);
            View errorView = parent.findViewById(R.id.fl_error);
            view.loadImageListener(messageForPic.getTiny().url, new OnRequestListener() {
                @Override
                public void onLoadSuccess(Drawable drawable) {
                    errorView.setVisibility(View.GONE);
                }

                @Override
                public void onLoadFile() {
                    errorView.setVisibility(View.VISIBLE);
                }
            });
            errorView.setOnClickListener(new OnMultiClickListener() {
                @Override
                public void OnNoMultiClick(View v) {
                    errorView.setVisibility(View.GONE);
                    view.loadImageListener(messageForPic.getTiny().url, new OnRequestListener() {
                        @Override
                        public void onLoadSuccess(Drawable drawable) {
                            errorView.setVisibility(View.GONE);
                        }

                        @Override
                        public void onLoadFile() {
                            errorView.setVisibility(View.VISIBLE);
                        }
                    });
                }
            });
        }
    }

    @BindingAdapter("showEmotionScale")
    public static void showEmotionPicScale(OImageView view, MessageForEmotion messageForEmotion) {
        if (messageForEmotion.getOriginal() != null) {
            ViewGroup parent = (ViewGroup) view.getParent();
            float maxWidth = 160.0f;
            float minWidth = 60.0f;
            ImageUtils.ImageSize imageSize = ImageUtils.initChatImageSize(maxWidth, minWidth,
                    messageForEmotion.getOriginal().width, messageForEmotion.getOriginal().height);
            ViewGroup.LayoutParams layoutParams = parent.getLayoutParams();
            if (imageSize.getWidth() == 0 || imageSize.getHeight() == 0) {
                layoutParams.width = (int) QMUIDisplayHelperO2.dp2px(view.getContext(), maxWidth);
                layoutParams.height = (int) QMUIDisplayHelperO2.dp2px(view.getContext(), maxWidth);
            } else {
                layoutParams.width = (int) QMUIDisplayHelperO2.dp2px(view.getContext(), imageSize.getWidth());
                layoutParams.height = (int) QMUIDisplayHelperO2.dp2px(view.getContext(), imageSize.getHeight());
            }

            parent.setLayoutParams(layoutParams);
            parent.post(new Runnable() {
                @Override
                public void run() {
                    view.load(messageForEmotion.getOriginal().url);
                }
            });
        }
    }

    @BindingAdapter({"picScaleWidth", "picScaleHeight"})
    public static void setPicScale(SimpleDraweeView view, int picScaleWidth, int picScaleHeight) {
        if (picScaleWidth > 0 && picScaleHeight > 0) {
            int maxWidth = QMUIDisplayHelper.dp2px(view.getContext(), 120);
            int minWidth = QMUIDisplayHelper.dp2px(view.getContext(), 60);
            ImageUtils.ImageSize imageSize = ImageUtils.initChatImageSize(maxWidth, minWidth, picScaleWidth, picScaleHeight);
            ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
            if (imageSize.getWidth() == 0 || imageSize.getHeight() == 0) {
                layoutParams.width = maxWidth;
                layoutParams.height = maxWidth;
            } else {
                layoutParams.width = (int) imageSize.getWidth();
                layoutParams.height = (int) imageSize.getHeight();
            }
            view.setLayoutParams(layoutParams);
        }
    }

    @BindingAdapter("showMood")
    public static void showMoodPic(OImageView view, MessageForMood messageForMood) {
        view.load(messageForMood.getMoodIcon());
    }

    @BindingAdapter({"chatMessage", "chatListener"})
    public static void setChatItemListener(View view, ChatMessage chatMessage, ChatItemListener chatListener) {
        if (chatListener != null) {
            view.setOnClickListener(v -> chatListener.onClick(view, chatMessage));
            view.setOnLongClickListener(v -> {
                if (chatMessage instanceof MessageForTime || chatMessage instanceof MessageForHint
                        || chatMessage instanceof MessageForDateCard || chatMessage instanceof MessageForLoveCard
                        || chatMessage instanceof MessageForNotifyCard /*|| chatMessage instanceof MessageForLike*/
                        || chatMessage instanceof MessageForLinkCall || chatMessage instanceof MessageForTopicGame
                        || chatMessage instanceof MessageForCommonCard || chatMessage instanceof MessageForMood) {
                    return true;
                }
                chatListener.onLongClick(v, chatMessage);
                return true;
            });
        }
        view.setHapticFeedbackEnabled(false);
    }

    @BindingAdapter({"answerImages"})
    public static void setAnswerImages(@NonNull ComposeView composeView, @Nullable ProfileInfoModel.QuestionAnswer answer) {
        if (answer != null && answer.imgs != null && !answer.imgs.isEmpty()) {
            composeView.setVisibility(View.VISIBLE);
            AnswerImagesComposableKt.bindAnswerImages(composeView, answer);
        } else {
            composeView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"pathClipCorner"})
    public static void setPathClip(@NonNull MeABOImageView view, String pathClipCorner) {
        int length = (int) (view.getResources().getDimensionPixelSize(R.dimen.chat_view_message_like_card_width) / 2f * 0.133f);
        view.setPathClip(pathClipCorner, length);
    }

    @BindingAdapter({"resendChatMessage", "resendChatListener"})
    public static void setResendChatItemListener(LottieAnimationView view, ChatMessage resendChatMessage, ChatItemListener resendChatListnner) {
        view.setOnClickListener(v -> resendChatListnner.onResendClick(view, resendChatMessage));
    }

    @BindingAdapter("quoteToMsgName")
    public static void setQuoteToName(TextView textView, ChatMessage msg) {
        if (msg != null) {
            ChatMessage chatMessage = msg.getQuoteMessage();
            if (chatMessage != null) {
                if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
                    User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
                    if (user != null) {
                        textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name, user.getNickName()));
                    }
                } else {
                    Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                    if (contact != null) {
                        textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name, contact.getNickName()));
                    }
                }
            }
        }
    }

    /**
     * friend false: 自己的消息回复内容布局
     * true: 对方消息回复内容布局
     */
    @BindingAdapter({"quoteToMsg", "friend"})
    public static void setQuoteTo(FrameLayout frameLayout, ChatMessage msg, boolean friend) {
        frameLayout.removeAllViews();
        frameLayout.setVisibility(View.GONE);
        if (msg != null) {
            ChatMessage chatMessage = msg.getQuoteMessage();
            if (chatMessage != null && (chatMessage.isShow() || chatMessage.isDeleted())) {
                ViewDataBinding viewDataBinding;
                if (TextUtils.isEmpty(chatMessage.getIllegalText())) {
                    if (chatMessage.isDeleted()) {
                        viewDataBinding = ChatViewMessageQuoteContentDeleteBinding.inflate(LayoutInflater.from(frameLayout.getContext()), null, false);
                    } else {
                        viewDataBinding = ItemFactory.createViewDataBindingForQuote(LayoutInflater.from(frameLayout.getContext()), chatMessage.getMediaType(), friend);
                    }
                } else {
                    viewDataBinding = ChatViewMessageQuoteContentWithdrawBinding.inflate(LayoutInflater.from(frameLayout.getContext()), null, false);
                }
                frameLayout.setVisibility(View.VISIBLE);
                viewDataBinding.setVariable(BR.msg, chatMessage);
                viewDataBinding.setVariable(BR.friend, friend);
                frameLayout.addView(viewDataBinding.getRoot());
            }
        }
    }

    @BindingAdapter({"replyVisibleFrom"})
    public static void setReplyVisibleFrom(FrameLayout replyLayout, MessageForLike message) {
        ChatMessage quoteMessage = message.getQuoteMessage();
        MoodInfo moodInfo = null;
        if (quoteMessage != null) {
            try {
                moodInfo = GsonUtils.fromJson(quoteMessage.getContent(), MoodInfo.class);
            } catch (Exception ignored) {

            }
        }

        if (moodInfo != null && !TextUtils.isEmpty(moodInfo.getMoodTitle())) {
            replyLayout.setVisibility(View.VISIBLE);
        } else {
            replyLayout.setVisibility(View.GONE);
        }
    }

    @SuppressLint("SetTextI18n")
    @BindingAdapter({"moodTitleFrom"})
    public static void setMoodTitleFrom(TextView textView, MessageForLike message) {
        ChatMessage quoteMessage = message.getQuoteMessage();
        MoodInfo moodInfo = null;
        if (quoteMessage != null) {
            try {
                moodInfo = GsonUtils.fromJson(quoteMessage.getContent(), MoodInfo.class);
            } catch (Throwable e) {
                TLog.error("setMoodTitle", "e:" + e);
            }
        }

        if (moodInfo != null && !TextUtils.isEmpty(moodInfo.getMoodTitle())) {
            textView.setText("回复你的心情：" + moodInfo.getMoodTitle());
        }
    }

    @BindingAdapter({"replyVisibleTo"})
    public static void setReplyVisibleTo(FrameLayout replyLayout, MessageForLike message) {
        ChatMessage quoteMessage = message.getQuoteMessage();
        MoodInfo moodInfo = null;
        if (quoteMessage != null) {
            try {
                moodInfo = GsonUtils.fromJson(quoteMessage.getContent(), MoodInfo.class);
            } catch (Exception ignored) {

            }
        }

        if (moodInfo != null && !TextUtils.isEmpty(moodInfo.getMoodTitle())) {
            replyLayout.setVisibility(View.VISIBLE);
        } else {
            replyLayout.setVisibility(View.GONE);
        }
    }

    @SuppressLint("SetTextI18n")
    @BindingAdapter({"moodTitleTo"})
    public static void setMoodTitleTo(TextView textView, MessageForLike message) {
        ChatMessage quoteMessage = message.getQuoteMessage();
        MoodInfo moodInfo = null;
        if (quoteMessage != null) {
            try {
                moodInfo = GsonUtils.fromJson(quoteMessage.getContent(), MoodInfo.class);
            } catch (Throwable e) {
                TLog.error("setMoodTitle", "e:" + e);
            }
        }

        if (moodInfo != null && !TextUtils.isEmpty(moodInfo.getMoodTitle())) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getQuoteMessage().getSender());
            if (contact != null) {
                textView.setText(contact.getNickName() + "的心情：" + moodInfo.getMoodTitle());
            } else {
                textView.setText("心情：" + moodInfo.getMoodTitle());
            }
        }
    }

    @BindingAdapter({"deleteQuoteMsg"})
    public static void setDeleteQuoteMsg(TextView textView, String extension) {
        TLog.info("deleteQuoteMsg", "extension:" + extension);
        if (TextUtils.isEmpty(extension)) {
            textView.setVisibility(View.GONE);
        } else {
            try {
                JSONObject jsonObject = new JSONObject(extension);
                String text = jsonObject.getString("text");
                if (TextUtils.isEmpty(text)) {
                    textView.setVisibility(View.GONE);
                } else {
                    textView.setVisibility(View.VISIBLE);
                    textView.setText(text);
                }
            } catch (Exception e) {
                TLog.error("deleteQuoteMsg", "e:" + e.getMessage());
                textView.setVisibility(View.GONE);
                e.printStackTrace();
            }
        }
    }

    /**
     * 设置语音播放状态
     * friend false: 自己的消息回复内容布局
     * true: 对方消息回复内容布局
     */
    @BindingAdapter({"voicePlayStatus", "friend"})
    public static void setVoicePlayStatus(ImageView imageView, int voiceStatus, boolean friend) {
        if (imageView != null) {
            if (voiceStatus == MessageForAudio.STATUS_VOICE_PLAYING) {
                if (friend) {
                    imageView.setImageResource(R.drawable.chat_ic_icon_chat_voice_from_pause);
                } else {
                    imageView.setImageResource(R.drawable.chat_ic_icon_chat_voice_to_pause);
                }
            } else {
                if (friend) {
                    imageView.setImageResource(R.drawable.chat_ic_icon_chat_voice_from_start);
                } else {
                    imageView.setImageResource(R.drawable.chat_ic_icon_chat_voice_to_start);
                }

            }
        }
    }

    @BindingAdapter({"shoProgress", "rpvProgress", "rpvProgressColor"})
    public static void setRPVProgress(RoundProgressView rpv, boolean showProgress, int progress, int progressColor) {
        if (showProgress) {
            if (progressColor > 0) {
                rpv.setProgressSelectColor(rpv.getResources().getColor(progressColor));
            }
            rpv.setProgress(progress);
        } else {
            rpv.setInProgress(false);
        }
    }


    @BindingAdapter("setAudioWidth")
    public static void setAudioWidth(View view, MessageForAudio messageForAudio) {
        ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
        int minWidth = QMUIDisplayHelper.dp2px(view.getContext(), 24);
        int maxWidth = (int) (QMUIDisplayHelper.getScreenWidth(view.getContext()) * 0.5f) - QMUIDisplayHelper.dp2px(view.getContext(), 32);
        int length = messageForAudio.getAudioInfo().getWaveArray().length;
        int width = (int) (((float) (length - 6) / 29) * (maxWidth - minWidth) + minWidth);
//        int currWidth = layoutParams.width;
//        if (currWidth > maxWidth) {
//            width = maxWidth;
//        } else if (currWidth > width) {
//            width = currWidth;
//        }
        layoutParams.width = width;
        view.setLayoutParams(layoutParams);
    }

    @BindingAdapter("voiceWave")
    public static void setVoiceWave(AudioRecorderPlayView playView, MessageForAudio messageForAudio) {
        playView.setLumpCount(messageForAudio.getWaveArraySize());
        playView.setWaveData(messageForAudio.getWaveArray());
        playView.setReadPercent(messageForAudio.getReadPercent());
    }

    /**
     * @param labelStatus 0 不显示，1-未解锁，2-已解锁，3-使用中
     */
    @BindingAdapter({"labelStatus"})
    public static void setLabelStatus(TextView textView, int labelStatus) {
        if (labelStatus == 0) {
            textView.setVisibility(View.GONE);
        } else {
            if (labelStatus == 3) {
                textView.setText("使用中");
                textView.setVisibility(View.VISIBLE);
            } else {
                textView.setVisibility(View.GONE);
            }
        }
    }

    /**
     * 设置进度
     */
    @BindingAdapter({"msgCount", "maxCount"})
    public static void setMsgCount(TextView textView, int msgCount, int maxCount) {
        SpannableStringBuilder builder = new SpannableStringBuilder();
        String indexS = String.valueOf(msgCount);
        SpannableString indexSpannableString = new SpannableString(indexS);
        indexSpannableString.setSpan(new ForegroundColorSpan(ContextCompat.getColor(textView.getContext(), R.color.common_black)),
                0, indexSpannableString.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        builder.append(indexSpannableString);
        builder.append("/");
        builder.append(String.valueOf(maxCount));
        textView.setText(builder);
    }

    /**
     * 设置相识卡卡片背景
     *
     * @param status 相识卡状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"dateCardBackground"})
    public static void setDateCardBackground(ConstraintLayout container, int status) {
        if (status == 20) {
            container.setBackgroundResource(R.mipmap.chat_bg_date_card_message_success);
        } else if (status == 30) {
            container.setBackgroundResource(R.mipmap.chat_bg_date_card_message_refuse);
        } else if (status == 40) {
            container.setBackgroundResource(R.mipmap.chat_bg_date_card_message_lose_efficacy);
        } else {
            container.setBackgroundResource(R.mipmap.chat_bg_date_card_message_send);
        }
    }

    /**
     * 设置相识卡文案
     *
     * @param status 相识卡状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"dateCardStatusDesc"})
    public static void setDateCardStatusDesc(TextView textView, int status) {
        if (status == 20) {
            textView.setText("对方欣然接受");
        } else if (status == 30) {
            textView.setText("Ta想再考虑一下");
        } else if (status == 40) {
            textView.setText("你送出的相识卡，失效了");
        } else {
            textView.setText("等待Ta的回应中...");
        }
    }

    /**
     * 设置相识卡文案 - 接收
     *
     * @param status 相识卡状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"dateCardStatusDescReceive"})
    public static void setDateCardStatusDescReceive(TextView textView, int status) {
        if (status == 20) {
            textView.setText("你欣然接受");
        } else if (status == 30) {
            textView.setText("你想再考虑一下");
        } else if (status == 40) {
            textView.setText("Ta送出的相识卡，失效了");
        } else {
            textView.setText("等待你的回应中...");

        }
    }

    @BindingAdapter({"dateCardStatusContentColor"})
    public static void setDateCardStatusContentColor(TextView textView, int status) {
        if (status == 20) {
            textView.setTextColor(ResourceExtKt.toResourceColor(R.color.color_white));
        } else if (status == 30) {
            textView.setTextColor(ResourceExtKt.toResourceColor(R.color.color_white));
        } else if (status == 40) {
            textView.setTextColor(ResourceExtKt.toResourceColor(R.color.color_white));
        } else {
            textView.setTextColor(ResourceExtKt.toResourceColor(R.color.chat_color_005D8E));

        }
    }

    /**
     * 设置表白信结束页title
     */
    @BindingAdapter({"msgSenderId", "reply"})
    public static void setLoveCardFinishTitle(TextView textView, String msgSenderId, int reply) {
        Context context = textView.getContext();
        if (!TextUtils.equals(AccountHelper.getInstance().getUserId(), msgSenderId)) {// 别人给我的
            if (reply == 1) {
                textView.setText(context.getString(R.string.chat_accept_ta_love));
            } else {
                textView.setText(context.getString(R.string.chat_refuse_ta_love));
            }
        } else {
            if (reply == 1) {
                textView.setText(context.getString(R.string.chat_accept_you_love));
            } else {
                textView.setText(context.getString(R.string.chat_refuse_you_love));
            }
        }
    }

    /**
     * 设置表白信卡片背景
     *
     * @param status 表白信状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     * @param from   0 别人发给我，1 我发给别人
     */
    @BindingAdapter({"loveCardBackground", "loveFrom"})
    public static void setLoveCardBackground(ConstraintLayout container, int status, int from) {
        User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
        if (user == null) return;
        if ((user.isMan() && from == 1) || (!user.isMan() && from != 1)) {// 我是男，我发给别人；我是女，别人发给我
            if (status == 20) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_man_success);
            } else if (status == 30) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_man_refused);
            } else if (status == 40) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_man_lose_efficacy);
            } else {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_man_send);
            }
        } else {
            if (status == 20) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_item_woman_success);
            } else if (status == 30) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_item_woman_refuse);
            } else if (status == 40) {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_item_woman_lose_efficacy);
            } else {
                container.setBackgroundResource(R.mipmap.chat_bg_love_card_item_woman_send
                );
            }
        }
    }

    /**
     * 设置表白信文案
     *
     * @param status 表白信状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"loveCardStatusDesc"})
    public static void setLoveCardStatusDesc(TextView textView, int status) {
        if (status == 20) {
            textView.setText("Ta已欣然接受");
        } else if (status == 30) {
            textView.setText("Ta没有收下");
        } else if (status == 40) {
            textView.setText("你送出的表白信，失效了");
        } else {
            textView.setText("等待Ta的回应中...");
        }
    }

    /**
     * 设置表白信文案 - 接收
     *
     * @param status 表白信状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"loveCardStatusDescReceive"})
    public static void setLoveCardStatusDescReceive(TextView textView, int status) {
        if (status == 20) {
            textView.setText("你已欣然接受");
        } else if (status == 30) {
            textView.setText("你没有收下");
        } else if (status == 40) {
            textView.setText("Ta送出的表白信，失效了");
        } else {
            textView.setText("等待你的回应中...");
        }
    }

    /**
     * 设置表白信文案 - 接收
     *
     * @param status 表白信状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
     */
    @BindingAdapter({"loveCardTextColor"})
    public static void setLoveCardTextColor(TextView textView, int status) {
        if (status == 20) {
            textView.setTextColor(textView.getContext().getColor(R.color.common_white));
        } else if (status == 30) {
            textView.setTextColor(textView.getContext().getColor(R.color.common_white));
        } else if (status == 40) {
            textView.setTextColor(textView.getContext().getColor(R.color.common_white));
        } else {
            textView.setTextColor(textView.getContext().getColor(R.color.common_color_4F39AE));
        }
    }

    /**
     * 显示指定格式日期
     *
     * @param timestamp 时间戳
     */
    @BindingAdapter({"formatTime"})
    public static void setFormatTime(TextView textView, long timestamp) {
        textView.setText(LDate.getTime(timestamp, "yyyy年MM月dd日"));
    }

    @BindingAdapter("toLoveCardName")
    public static void setToLoveCardName(TextView textView, ChatMessage chatMessage) {
        if (chatMessage != null) {
            String nickName = "";
            if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {// 我发的
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getChatId());
                nickName = contact != null ? contact.getNickName() : "";
            } else {
                User user = ServiceManager.getInstance().getDatabaseService().getUserDao().getUser(AccountHelper.getInstance().getUserId());
                nickName = user != null ? user.getNickName() : "";
            }

            StringBuilder stringBuilder = new StringBuilder();
            if (!TextUtils.isEmpty(nickName)) {
                stringBuilder.append("给");
                stringBuilder.append(nickName);
                stringBuilder.append("的");
            }
            textView.setText(stringBuilder.toString());
        }
    }

    @BindingAdapter({"myself", "avatarByUserId", "chatListener"})
    public static void setAvatarByUserId(OImageView imageView, boolean myself, ChatMessage chatMessage, ChatItemListener chatListener) {
        if (chatMessage != null) {
            if (myself) {// 我自己
                User user = ServiceManager.getInstance().getDatabaseService().getUserDao().getUser(AccountHelper.getInstance().getUserId());
                imageView.load(user != null ? user.getTinyAvatar() : "");
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getChatId());
                imageView.load(contact != null ? contact.getTinyAvatar() : "");
            }

            imageView.setOnClickListener(new OnMultiClickListener() {
                @Override
                public void OnNoMultiClick(View v) {
                    if (myself) {
                        if (chatListener != null) {
                            chatListener.clickAvatar(AccountHelper.getInstance().getUserId(), "");
                        }
                    } else {
                        Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getChatId());

                        String securityId = "";
                        if (contact != null) {
                            securityId = contact.getSecurityId();
                        }

                        if (chatListener != null) {
                            chatListener.clickAvatar(chatMessage.getSender(), securityId);
                        }
                    }
                }
            });
        }
    }

    @BindingAdapter({"relationshipBackground"})
    public static void setRelationBackground(ConstraintLayout constraintLayout, MessageForNotifyCard notifyCard) {
        if (notifyCard == null) {
            constraintLayout.setBackground(ContextCompat.getDrawable(constraintLayout.getContext(), R.mipmap.chat_bg_relationship_avatar_gray));
        } else if (notifyCard.getNotifyCardInfo() == null) {
            constraintLayout.setBackground(ContextCompat.getDrawable(constraintLayout.getContext(), R.mipmap.chat_bg_relationship_avatar_gray));
        } else if (notifyCard.getNotifyCardInfo().getType() == 3 && notifyCard.getNotifyCardInfo().getStatus() == 1) {
            constraintLayout.setBackground(ContextCompat.getDrawable(constraintLayout.getContext(), R.mipmap.chat_bg_relationship_avatar_light));
        } else if (notifyCard.getNotifyCardInfo().getStatus() == 2) {
            constraintLayout.setBackground(ContextCompat.getDrawable(constraintLayout.getContext(), R.mipmap.chat_bg_relationship_avatar_gray));
        } else if (notifyCard.getNotifyCardInfo().getStatus() == 1) {
            constraintLayout.setBackground(ContextCompat.getDrawable(constraintLayout.getContext(), R.mipmap.chat_bg_relationship_avatar));
        }

    }

    @BindingAdapter({"relationshipTitle"})
    public static void setRelationshipTitle(TextView textView, MessageForNotifyCard notifyCard) {
        if (notifyCard != null && notifyCard.getNotifyCardInfo() != null) {
            if (!TextUtils.isEmpty(notifyCard.getNotifyCardInfo().getContent())) {
                textView.setText(notifyCard.getNotifyCardInfo().getContent());
            } else {
                if (notifyCard.getNotifyCardInfo().getType() == 1) {// 1:相识卡
                    textView.setText(notifyCard.getNotifyCardInfo().getStatus() == 1 ? "你们的48h相识正式开启" : "你们的48h相识已结束");
                } else {// 2:表白信
                    textView.setText(notifyCard.getNotifyCardInfo().getStatus() == 1 ? "你们的情侣关系正式开启" : "你们的情侣关系已结束");
                }
            }
        }
    }

    @BindingAdapter({"dateCardStatusTitle", "msgSenderId"})
    public static void setDateCardStatusTitle(TextView textView, DateCardStatusBean bean, String msgSenderId) {
        if (bean != null) {
            if (TextUtils.equals(AccountHelper.getInstance().getUserId(), msgSenderId)) {// 我发的
                if (bean.reply == 0) {
                    textView.setText("等待回应中...");
                    textView.setTextColor(textView.getContext().getColor(R.color.common_color_B7B7B7));
                } else if (bean.reply == 1) {
                    textView.setText("Ta接受了你的相识邀请");
                    textView.setTextColor(textView.getContext().getColor(R.color.chat_date_card_date_mode));
                } else if (bean.reply == 2) {
                    textView.setText("Ta拒绝了你的相识邀请");
                    textView.setTextColor(textView.getContext().getColor(R.color.common_color_B7B7B7));
                }
            } else {
                if (bean.reply == 0) {
                    textView.setText("等待回应中...");
                    textView.setTextColor(textView.getContext().getColor(R.color.common_color_B7B7B7));
                } else if (bean.reply == 1) {
                    textView.setText("你接受了Ta的相识邀请");
                    textView.setTextColor(textView.getContext().getColor(R.color.chat_date_card_date_mode));
                } else if (bean.reply == 2) {
                    textView.setText("你拒绝了Ta的相识邀请");
                    textView.setTextColor(textView.getContext().getColor(R.color.common_color_B7B7B7));
                }
            }
        }
    }

    @BindingAdapter("likeReason")
    public static void setLikeReason(TextView textView, ChatMessage chatMessage) {
        if (chatMessage.getMediaType() == MessageConstants.MSG_LIKE_CARD) {
            MessageForLike messageForLike = (MessageForLike) chatMessage;
            if (TextUtils.isEmpty(messageForLike.getLikeInfo().likeReason)) {
                textView.setVisibility(View.GONE);
            } else {
                textView.setVisibility(View.VISIBLE);
                textView.setText(((MessageForLike) chatMessage).getLikeInfo().likeReason);
            }
        }
    }

    @BindingAdapter({"likeSource", "gender"})
    public static void setLikeSource(TextView textView, ChatMessage chatMessage, int gender) {
        if (chatMessage.getMediaType() == MessageConstants.MSG_LIKE_CARD) {
            MessageForLike messageForLike = (MessageForLike) chatMessage;
            String genderString = "Ta";
            if (gender == Constants.MALE) {
                genderString = "他";
            } else if (gender == Constants.FEMALE) {
                genderString = "她";
            }
            Resources resources = textView.getContext().getResources();
            Drawable drawable = null;
            switch (messageForLike.getLikeInfo().resourceType) {
                case Constants.TYPE_LIKE_AVATAR:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_pic, null);
                    break;
                case Constants.TYPE_LIKE_A_B_FACE:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_a_b, null);
                    break;
                case Constants.TYPE_LIKE_STORY:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_story, null);
                    break;
                case Constants.TYPE_LIKE_TEXT:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_text_answer, null);
                    break;
                case Constants.TYPE_LIKE_VOICE:
                    drawable = resources.getDrawable(R.mipmap.chat_ic_like_voice, null);
                    break;
                default:
                    break;
            }
            if (drawable != null) {
                drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
                textView.setCompoundDrawables(drawable, null, null, null);
            }

            String content;
//            int highlightLength = messageForLike.getLikeInfo().likeModuleName.length();
            if (TextUtils.equals(messageForLike.getSender(), AccountHelper.getInstance().getUserId())) {
                content = resources.getString(R.string.chat_like_item_source, "我", genderString, messageForLike.getLikeInfo().likeModuleName);
            } else {
                content = resources.getString(R.string.chat_like_item_source, "", "我", messageForLike.getLikeInfo().likeModuleName);
            }
//            SpannableString sb = new SpannableString(content);
//            sb.setSpan(new ForegroundColorSpan(ContextCompat.getColor(textView.getContext(), R.color.common_black)),
//                    content.length() - highlightLength, content.length(), Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
            textView.setText(content);
        } else {
            textView.setText(View.GONE);
        }
    }

    /**
     * 设置聊天页卡片解锁气泡icon
     */
    @BindingAdapter("popupIcon")
    public static void setChatPopupIcon(ImageView imageView, int type) {
        imageView.setImageResource(type == 1 ? R.mipmap.chat_ic_date_card_pop :
                type == 2 ? R.mipmap.chat_ic_love_card_pop : R.mipmap.chat_ic_voice_talk_pop);
    }

    /**
     * 设置聊天页卡昵称
     */
    @BindingAdapter("nickName")
    public static void setNickName(TextView textView, String userId) {
        String nickName = null;
        if (TextUtils.equals(userId, AccountHelper.getInstance().getUserId())) {
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            if (user != null) {
                nickName = user.getNickName();
            }
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
            if (contact != null) {
                nickName = contact.getNickName();
            }
        }
        if (nickName == null) {
            nickName = "Ta";
        }
        textView.setText(nickName);
    }

    @BindingAdapter("conversationTag")
    public static void setConversationTag(TextView textView, List<String> tags) {
        if (LList.isEmpty(tags)) {
            textView.setVisibility(View.GONE);
        } else {
            textView.setVisibility(View.VISIBLE);
            textView.setText(tags.get(0));
        }
    }

    @BindingAdapter({"linkMediaType", "isSelf"})
    public static void setLinkCallIcon(TextView textView, int linkMediaType, boolean isSelf) {
        int resId = 0;
        switch (linkMediaType) {
            case MessageConstants.LINK_CALL_CANCEL://发起方取消
            case MessageConstants.LINK_CALL_OVER://通话结束
            case MessageConstants.LINK_CALL_NOT_CONNECT://无应答
                resId = R.drawable.chat_link_call_media_ok_f;
                break;
            case MessageConstants.LINK_CALL_REFUSE://接收方拒绝
            case MessageConstants.LINK_CALL_BUSY://忙线中
                resId = R.drawable.chat_link_call_media_cancel_f;
                break;
        }
        if (resId > 0) {
            Drawable dra = textView.getResources().getDrawable(resId);
            setLeftDrawable(textView, dra);
        }
    }

    public static void setLeftDrawable(TextView textView, Drawable dra) {
        dra.setBounds(0, 0, dra.getMinimumWidth(), dra.getMinimumHeight());
        textView.setCompoundDrawables(dra, null, null, null);
    }


    @BindingAdapter("likeVoiceWave")
    public static void setLikeVoiceWave(AudioRecorderPlayView playView, MessageForLike messageForLike) {
        playView.setWaveData(messageForLike.getWaveArray());
        playView.setReadPercent(messageForLike.getReadPercent());
    }

    @BindingAdapter("conversationText")
    public static void conversationText(TextView textView, Conversation con) {
        String content;
        if (TextUtils.isEmpty(con.getDraftContent())) {
            content = con.getContent();
        } else {
            content = textView.getContext().getString(R.string.chat_draft_content, con.getDraftContent());
        }
        Spannable spannable = ChatUtils.createSpannable(textView, content);
        textView.setText(ChatUtils.obtainEmotionSpannable(spannable));
    }

    @BindingAdapter({"agreementText", "chatListener"})
    public static void setAgreementText(MLinkEmotionTextView textView, MessageForText message, ChatItemListener chatItemListener) {
        textView.setContent(message, chatItemListener);
    }

    @BindingAdapter({"agreementText", "chatListener"})
    public static void setAgreementText(MLinkEmotionTextView textView, MessageForHint message, ChatItemListener chatItemListener) {
        textView.setContent(message, chatItemListener);
    }

    @BindingAdapter("topicGameBg")
    public static void setTopicGameBg(View view, MessageForTopicGame message) {
        String bgColor = message.getQuestion().bgColor;
        String startBgColor = "#F4F6FF";
        String endBgColor = "#E2E7FF";
        String[] split = bgColor.split(",");
        if (split.length >= 2) {
            startBgColor = split[0];
            endBgColor = split[1];
        }
        view.setBackground(DrawableUtil.getTopicGameDetailBgDrawable(startBgColor, endBgColor));

    }

    @BindingAdapter({"chatTopicGameOption", "chatIsFrom"})
    public static void setChatTopicGameOption(RecyclerView recyclerView, MessageForTopicGame message, boolean chatIsFrom) {
        if (message.getTopicGameStatus() == MessageForTopicGame.TopicGameInfo.STATUS_OTHER_DO) {
            recyclerView.setVisibility(View.GONE);
        } else {
            recyclerView.setVisibility(View.VISIBLE);
            BaseQuickAdapter quickAdapter = new BaseQuickAdapter<TopicGameOptionBean, BaseDataBindingHolder<ChatItemChatTopicGameOptionBinding>>(R.layout.chat_item_chat_topic_game_option, message.getQuestion().optionList) {

                @Override
                protected void convert(@NonNull BaseDataBindingHolder<ChatItemChatTopicGameOptionBinding> baseViewHolder, TopicGameOptionBean item) {
                    ChatItemChatTopicGameOptionBinding binding = baseViewHolder.getDataBinding();
                    binding.setItem(item);
                    ChatTopicGameAnswer senderAnswer = message.getSenderAnswer();
                    binding.tvTitle.setText(StringUtil.getTopicGameOptionTitle(getItemPosition(item)));
                    boolean selected = false;
                    if (!chatIsFrom) {
                        selected = TextUtils.equals(senderAnswer.optionId, item.id);
                    }
                    binding.ivOption.setSelected(selected);
                    binding.setSelected(selected);
                }

                @Override
                protected void bindViewClickListener(@NonNull BaseDataBindingHolder<ChatItemChatTopicGameOptionBinding> viewHolder, int viewType) {

                }
            };
            recyclerView.setAdapter(quickAdapter);
        }
    }

    @BindingAdapter("imageAvatarUrl")
    public static void setAvatarUrl(OImageView view, String userId) {
        String avatar = "";
        if (TextUtils.equals(userId, AccountHelper.getInstance().getUserId())) {
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            if (user != null) {
                avatar = user.getTinyAvatar();
            }
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(userId);
            if (contact != null) {
                avatar = contact.getTinyAvatar();
            }
        }
        if (!TextUtils.isEmpty(avatar)) {
            view.load(avatar);
        }
    }

    @BindingAdapter({"showChatHistoryHighLight"})
    public static void showChatHistoryHighLight(ViewGroup viewGroup, ObservableBoolean showLight) {
        if (showLight.get()) {
            ValueAnimator animator = new ValueAnimator().setDuration(5000);
            animator.setFloatValues(0F, 1.0F);
            animator.addListener(new Animator.AnimatorListener() {
                @Override
                public void onAnimationStart(Animator animation) {

                }

                @Override
                public void onAnimationEnd(Animator animation) {
                    showLight.set(false);
                }

                @Override
                public void onAnimationCancel(Animator animation) {
                    showLight.set(false);
                }

                @Override
                public void onAnimationRepeat(Animator animation) {

                }
            });
            animator.start();
        }
    }

    @BindingAdapter({"dateMainAvatarBackground"})
    public static void setChatListItemDateMainAvatarBackground(FrameLayout frameLayout, int gender) {
        if (gender == Account.ACCOUNT_GENDER_MEN) {
            frameLayout.setBackgroundResource(R.mipmap.chat_bg_date_female_main_avatar);
        } else {
            frameLayout.setBackgroundResource(R.mipmap.chat_bg_date_male_main_avatar);
        }
    }

    @BindingAdapter({"dateSubAvatarBackground"})
    public static void setChatListItemDateSubAvatarBackground(FrameLayout frameLayout, int gender) {
        if (gender == Account.ACCOUNT_GENDER_MEN) {
            frameLayout.setBackgroundResource(R.mipmap.chat_bg_date_male_sub_avatar);
        } else {
            frameLayout.setBackgroundResource(R.mipmap.chat_bg_date_female_sub_avatar);
        }
    }

    @BindingAdapter({"dateHeart"})
    public static void setChatListItemDateHeart(ImageView imageView, int gender) {
        if (gender == Account.ACCOUNT_GENDER_MEN) {
            imageView.setImageResource(R.mipmap.chat_ic_conversation_date_female_heart);
        } else {
            imageView.setImageResource(R.mipmap.chat_ic_conversation_date_male_heart);
        }
    }

//    @BindingAdapter({"moodUpdate"})
//    public static void setMoodUpdateImage(ImageView imageView, int gender) {
//        if (gender == Account.ACCOUNT_GENDER_MEN) {
//            imageView.setImageResource(R.mipmap.chat_ic_conversation_date_female_heart);
//        } else {
//            imageView.setImageResource(R.mipmap.chat_ic_conversation_date_male_heart);
//        }
//    }


    public static String getTinyAvatar(ChatMessage chatMessage) {
        String avatar = "";
        if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            if (user != null) {
                avatar = user.getTinyAvatar();
            }
        } else {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
            if (contact != null) {
                avatar = contact.getTinyAvatar();
            }
        }
        return avatar;
    }


    @BindingAdapter({"chatAvatar"})
    public static void setChatAvatarAndClick(View view, String tinyAvatar) {

    }

    @BindingAdapter({"chatItemBackground"})
    public static void setChatItemBackground(View view, ChatMessage message) {
        if (message.getRelationStatus() == MessageConstants.MATCHING_STATUS_DATING || message.getRelationStatus() == MessageConstants.MATCHING_STATUS_LOVERS) {
            if (TextUtils.equals(message.getSender(), AccountHelper.getInstance().getUserId())) {
                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
                if (user != null) {
                    if (user.getGender() == Constants.MALE) {
                        view.setBackgroundResource(R.drawable.chat_bg_to_male);
                    } else {
                        view.setBackgroundResource(R.drawable.chat_bg_to_female);
                    }
                }
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getSender());
                if (contact != null) {
                    if (contact.getGender() == Constants.MALE) {
                        view.setBackgroundResource(R.drawable.chat_bg_from_male);
                    } else {
                        view.setBackgroundResource(R.drawable.chat_bg_from_female);
                    }
                }
            }
        } else {
            if (TextUtils.equals(message.getSender(), AccountHelper.getInstance().getUserId())) {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getChatId());
                boolean isMeetingPlan = contact != null && contact.getModeType() == 2;

                view.setBackgroundResource(isMeetingPlan ? R.drawable.chat_bg_message_to4 : R.drawable.chat_bg_message_to3);
            } else {
                view.setBackgroundResource(R.drawable.chat_bg_message_from3);
            }
        }
    }

    @BindingAdapter({"heartVisibility"})
    public static void setMeetingPlanHeartVisibility(View view, ChatMessage message) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getChatId());
        boolean isMeetingPlan = contact != null && contact.getModeType() == 2;

        View ivHeart = view.findViewById(R.id.iv_heart);
        ivHeart.setVisibility(isMeetingPlan ? View.VISIBLE : View.GONE);
    }

    @BindingAdapter({"msgContentLayout"})
    public static void setMeetingPlanContentLayout(View view, ChatMessage message) {
        Contact contact = ServiceManager.getInstance().getContactService().getContactById(message.getChatId());
        boolean isMeetingPlan = contact != null && contact.getModeType() == 2;

        FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) view.getLayoutParams();
        layoutParams.setMarginStart(QMUIDisplayHelper.dp2px(view.getContext(), isMeetingPlan ? 12 : 0));
        view.setLayoutParams(layoutParams);
    }

    @BindingAdapter("quoteToMsgText")
    public static void setQuoteToText(TextView textView, MessageForText chatMessage) {
        if (chatMessage != null) {
            if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
                if (user != null) {
                    textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name_text, user.getNickName(), chatMessage.getContent()));
                }
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                if (contact != null) {
                    textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name_text, contact.getNickName(), chatMessage.getContent()));
                }
            }
        }
    }

    @BindingAdapter("quoteToLikeMsgText")
    public static void setQuoteToLikeMsgText(TextView textView, MessageForLike chatMessage) {
        if (chatMessage != null) {
            textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_no_name_text, chatMessage.getSummary()));
        }
    }

    @BindingAdapter("quoteToMsgEmotion")
    public static void setQuoteToEmotion(TextView textView, MessageForEmotion chatMessage) {
        if (chatMessage != null) {
            textView.setText(chatMessage.getSummary());
        }
    }

    @BindingAdapter("quoteToMsgLikeMood")
    public static void setQuoteToLikeMood(TextView textView, MessageForLike chatMessage) {
        if (chatMessage != null) {
            if (TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
                User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
                if (user != null) {
                    textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name_text, user.getNickName(), chatMessage.getContent()));
                }
            } else {
                Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                if (contact != null) {
                    textView.setText(textView.getContext().getResources().getString(R.string.chat_quote_name_text, contact.getNickName(), chatMessage.getContent()));
                }
            }
        }
    }

    /**
     * 设置语音播放状态
     * friend false: 自己的消息回复内容布局
     * true: 对方消息回复内容布局
     */
    @BindingAdapter({"voiceLikePlayStatus"})
    public static void setVoiceLikePlayStatus(ImageView imageView, int voiceStatus) {
        if (imageView != null) {
            if (voiceStatus == MessageForAudio.STATUS_VOICE_PLAYING) {
                imageView.setImageResource(R.drawable.me_ic_icon_info_preview_voice_pause);
            } else {
                imageView.setImageResource(R.drawable.me_ic_icon_info_preview_voice_start);
            }
        }
    }

    @BindingAdapter("reWrite")
    public static void showCommonIconText(TextView textView, ChatMessage chatMessage) {
        if (chatMessage.getWithdraw() == MessageConstants.MSG_WITHDRAW && TextUtils.equals(chatMessage.getSender(), AccountHelper.getInstance().getUserId())) {
            long mid = -chatMessage.getMid();
            HashMap<Long, Long> withDrawnMessages = ServiceManager.getInstance().getMessageService().getLocalWithDrawMsg();
            Set<Long> ids = withDrawnMessages.keySet();
            if (ids.contains(mid)) {
                if (System.currentTimeMillis() - withDrawnMessages.get(mid) < MessageConstants.MSG_AT_ALL_REWRITE_TIME) {
                    textView.setVisibility(View.VISIBLE);
                } else {
                    textView.setVisibility(View.GONE);
                    ServiceManager.getInstance().getMessageService().getLocalWithDrawMsg().remove(mid);
                }
            } else {
                textView.setVisibility(View.GONE);
            }
        } else {
            textView.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"showBg", "bgResource"})
    public static void setViewBgOrClean(View view, boolean showBg, int bgResource) {
        if (showBg) {
            if (bgResource > 0) {
                view.setBackgroundResource(bgResource);
                return;
            }
        }
        view.setBackgroundResource(R.color.common_translate);
    }

    @BindingAdapter({"chatUserInfo"})
    public static void setChatUserInfoCard(View view, MessageForUserCard message) {
        ChatBindingAdapterHelper.Companion.setChatUserInfoCard(view, message);
    }

    @BindingAdapter({"chatUserStickyInfo"})
    public static void setChatStickyInfo(View view, MessageForUserCard message) {
        ChatBindingAdapterHelper.Companion.setChatStickyInfo(view, message);
    }

    @BindingAdapter({"chatUserStickyInfo1"})
    public static void setChatStickyInfo1(View view, MessageForUserCard message) {
        ChatBindingAdapterHelper.Companion.setChatStickyInfo(view, message);
    }

    @BindingAdapter({"chatActivityInfo"})
    public static void setChatActivityInfo(View view, MessageForActivityMedia message) {
        ChatBindingAdapterHelper.Companion.setChatActivityInfo(view, message);
    }

    @BindingAdapter({"noticeAvatars"})
    public static void setNoticeAvatars(View view, Conversation conversation) {
        ChatBindingAdapterHelper.Companion.setNoticeAvatars(view, conversation);
    }

    @BindingAdapter({"chatSender", "chatListener"})
    public static void setChatSender(View view, ChatMessage chatMessage, ChatItemListener chatListener) {
        OImageView oImageView = view.findViewById(R.id.iv_avatar);
        String tinyAvatar = getTinyAvatar(chatMessage);
        if (oImageView != null) {
            oImageView.load(tinyAvatar);
            oImageView.setOnClickListener(new OnMultiClickListener() {
                @Override
                public void OnNoMultiClick(View v) {
                    Contact contact = ServiceManager.getInstance().getContactService().getContactById(chatMessage.getSender());
                    if (contact != null && contact.getUserType() == MessageConstants.MSG_CONTACT_TYPE_SYS) {
                        return;
                    }
                    ChatPointReporter.Companion.reportChatUserDetailClick("头像", chatMessage.getSender());
                    String securityId = "";
                    if (contact != null) {
                        securityId = contact.getSecurityId();
                    }
                    if (chatListener != null)
                        chatListener.clickAvatar(chatMessage.getSender(), securityId);
                }
            });
        }
    }

    @BindingAdapter("showChatListLoveStatus")
    public static void showChatListLoveStatus(OImageView oImageView, Conversation conversation) {
        if (conversation != null && conversation.getIconShowType() == 2) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(conversation.getChatId());
            if (contact != null && (contact.getModeType() == 1 || contact.getModeType() == 0)) {
                oImageView.load(conversation.getIcon());
                oImageView.setVisibility(View.VISIBLE);
            } else {
                oImageView.setVisibility(View.GONE);
            }
        } else {
            oImageView.setVisibility(View.GONE);
        }


    }

    @BindingAdapter({"messageForMomentRecommendCard"})
    public static void setMessageForMomentRecommendCard(ComposeView composeView, @NonNull MessageForCommonCard messageForCommonCard) {
        MomentRecommendComposableKt.bindMomentRecommend(composeView, messageForCommonCard);
    }

    @BindingAdapter({"messageForChangeWxCard"})
    public static void setMessageForChangeWxCard(ComposeView composeView, @NonNull MessageForCommonCard messageForCommonCard) {
        ChangeWXCardComposableKt.bindChangeWXCard(composeView, messageForCommonCard);
    }

    @BindingAdapter({"messageForFeedbackReplayCard"})
    public static void setMessageForFeedbackReplayCard(ComposeView composeView, @NonNull MessageForFeedbackReplayCard messageForCommonCard) {
        FeedbackReplayComposableKt.bindFeedbackReplay(composeView, messageForCommonCard);
    }

    @BindingAdapter({"messageForTacitTestCard"})
    public static void setMessageTacitTestCard(ComposeView composeView, @NonNull MessageForTacitTestCard messageForTacitTestCard) {
        TacitTestComposableKt.bindTacitTest(composeView, messageForTacitTestCard);
    }

    @BindingAdapter({"messageForMeetingPlanTaskCard"})
    public static void setMessageForMeetingPlanTaskCard(ComposeView composeView, @NonNull MessageForMeetingPlanTaskCard messageForTacitTestCard) {
        bindMeetingPlanTip(composeView, messageForTacitTestCard);
    }

    @BindingAdapter({"messageForMeetingPlanInviteCard"})
    public static void setMessageForMeetingPlanInviteCard(ComposeView composeView, @NonNull MessageForMeetingPlanInviteCard messageForTacitTestCard) {
        ChatItemMeetingPlanComposableKt.bindChatItemMeetingPlanCard(composeView, messageForTacitTestCard);
    }

    @BindingAdapter({"messageForMeetingProtectCard"})
    public static void setMessageForMeetingProtectCard(ComposeView composeView, @NonNull MessageForMeetingProtectCard messageForMeetingProtectCard) {
        ChatItemMeetingProtectComposableKt.bindChatItemMeetingProtectCard(composeView, messageForMeetingProtectCard);
    }
}
