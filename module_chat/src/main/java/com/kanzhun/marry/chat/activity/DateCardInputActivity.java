package com.kanzhun.marry.chat.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.kotlin.ext.ActivityExtKt;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.LText;
import com.kanzhun.common.views.edittext.LengthNoticeFilter;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.http.callback.ICommonCallback;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.callback.DateCardInputCallback;
import com.kanzhun.marry.chat.databinding.ChatActivityDateCardInputBinding;
import com.kanzhun.marry.chat.viewmodel.DateCardInputViewModel;
import com.kanzhun.utils.views.MultiClickUtil;
import com.qmuiteam.qmui.util.QMUIKeyboardHelper;
import com.qmuiteam.qmui.util.QMUIStatusBarHelper;
import com.sankuai.waimai.router.annotation.RouterUri;

/**
 * 消息 - 相识卡输入
 * <p>
 * Created by Qu Zhiyong on 2022/5/26
 */
@RouterUri(path = ChatPageRouter.DATE_CARD_INPUT_ACTIVITY)
public class DateCardInputActivity extends FoundationVMActivity<ChatActivityDateCardInputBinding, DateCardInputViewModel> implements DateCardInputCallback {

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_date_card_input;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        QMUIStatusBarHelper.setStatusBarLightMode(this);
        getViewModel().chatId = getIntent().getStringExtra(BundleConstants.BUNDLE_CHAT_ID);

        getDataBinding().editText.requestFocus();
        QMUIKeyboardHelper.showKeyboard(getDataBinding().editText, 300);

        InputFilter[] inputFilters = new InputFilter[1];
        LengthNoticeFilter filter = new LengthNoticeFilter(20);
        inputFilters[0] = filter;
        getDataBinding().editText.setFilters(inputFilters);
        getDataBinding().editText.setTypeface(LText.getBoldTypeface(this));
        getDataBinding().editText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                getViewModel().setErrorObservable("");
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        getViewModel().getErrorLiveData().observe(this, reason -> {
            // 光标定位到最后
            String content = getDataBinding().editText.getText().toString();
            getDataBinding().editText.setSelection(content.length());
        });
        ActivityExtKt.showSoftInput( getDataBinding().editText,true);
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickLeft(View view) {
        AppUtil.finishActivity(this);
    }

    @Override
    public void clickSubmit(View view) {
        if (MultiClickUtil.isMultiClick()) return;
        String content = getDataBinding().editText.getText().toString().trim();
        String msg = !TextUtils.isEmpty(content) ? content : getResources().getString(R.string.chat_would_you_like_date_with_me);
        getViewModel().sendDateCard(msg, new ICommonCallback() {
            @Override
            public void onSuccess() {
                QMUIKeyboardHelper.hideKeyboard(getDataBinding().editText);
                AppUtil.finishActivity(DateCardInputActivity.this);
                BaseApplication.getApplication().getActivityLifecycleCallbacks().finishActivity(DateCardActivity.class);
            }
        });
    }
}