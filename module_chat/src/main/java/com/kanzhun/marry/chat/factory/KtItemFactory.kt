package com.kanzhun.marry.chat.factory

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.model.message.ChatMessage
import com.kanzhun.foundation.model.message.MessageConstants
import com.kanzhun.foundation.model.message.MessageForLike
import com.kanzhun.foundation.utils.MessageUtils
import com.kanzhun.marry.chat.databinding.ChatItemFeedbackReplayCardBinding
import com.kanzhun.marry.chat.databinding.ChatItemMeetingPlanInviteBinding
import com.kanzhun.marry.chat.databinding.ChatItemMeetingPlanProtectBinding
import com.kanzhun.marry.chat.databinding.ChatItemMeetingPlanTaskBinding
import com.kanzhun.marry.chat.databinding.ChatItemMessageUpdateBinding
import com.kanzhun.marry.chat.databinding.ChatItemTacitTestBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2AbBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2AboutMeBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2AboutMePicBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2Binding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2HobbyBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2HobbyPicBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2IdealBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2ProfileBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2QaBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2SingleReasonBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2StoryBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFrom2VoiceBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeFromActivityPersonalPhotoBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeMoodFromBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeMoodToBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2AbBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2AboutMeBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2AboutMePicBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2Binding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2HobbyBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2HobbyPicBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2IdealBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2ProfileBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2QaBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2SingleReasonBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2StoryBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeTo2VoiceBinding
import com.kanzhun.marry.chat.databinding.ChatViewMessageLikeToActivityPersonalPhotoBinding
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_AB
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_ABOUT_ME
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_ABOUT_ME_PIC
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_HOBBY
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_HOBBY_PIC
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_MOOD
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_PROFILE
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_QA
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_STORY
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_FROM_VOICE
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_AB
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_ABOUT_ME
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_ABOUT_ME_PIC
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_HOBBY
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_HOBBY_PIC
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_MOOD
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_PROFILE
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_QA
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_STORY
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_LIKE_CARD_TO_VOICE
import com.kanzhun.marry.chat.factory.ItemFactory.ITEM_UPDATE_FROM
import com.techwolf.lib.tlog.TLog

private const val TAG = "KtItemFactory"

//region ViewType

// ******* 支持后台回复用户反馈@大石
// https://zhishu.zhipin.com/wiki/jo5LXM4cNQg
const val ITEM_FEEDBACK_REPLAY = ItemFactory.ITEM_MOMENT_RECOMMEND_CARD + 1

// ******* 新增聊天玩法默契考验@丹丹
// https://zhishu.zhipin.com/wiki/DIB5za5DkAo
const val ITEM_TACIT_TEST = ITEM_FEEDBACK_REPLAY + 1

// 506 活动个人照片，这里类型只有1-图片
const val ITEM_ACTIVITY_PERSONAL_PHOTO_FROM = ITEM_TACIT_TEST + 1
const val ITEM_ACTIVITY_PERSONAL_PHOTO_TO = ITEM_ACTIVITY_PERSONAL_PHOTO_FROM + 1

// 理想型
const val ITEM_IDEAL_PARTNER_DESC_FROM = ITEM_ACTIVITY_PERSONAL_PHOTO_TO + 1
const val ITEM_IDEAL_PARTNER_DESC_TO = ITEM_IDEAL_PARTNER_DESC_FROM + 1

// 单身原因
const val ITEM_SINGLE_REASON_FROM = ITEM_IDEAL_PARTNER_DESC_TO + 1
const val ITEM_SINGLE_REASON_TO = ITEM_SINGLE_REASON_FROM + 1

//见面计划-任务
const val ITEM_MEETING_PLAN_TASK = ITEM_SINGLE_REASON_TO + 1
//见面计划-邀请
const val ITEM_MEETING_PLAN_INVITE = ITEM_MEETING_PLAN_TASK + 1
//守护计划
const val ITEM_MEETING_PROTECT = ITEM_MEETING_PLAN_INVITE + 1

//交换微信卡
const val ITEM_CHANGE_WX_CARD = ITEM_MEETING_PROTECT + 1



//endregion

fun getViewType(messageRecord: ChatMessage): Int {
    val isFriend = !MessageUtils.isAuthor(messageRecord)
    TLog.info(TAG, "getViewType.isFriend: $isFriend")
    return when (messageRecord.mediaType) {
        MessageConstants.MSG_LIKE_CARD -> {
            getLikeCardViewType(messageRecord)
        }

        MessageConstants.MSG_FEEDBACK_REPLAY -> {
            ITEM_FEEDBACK_REPLAY
        }

        MessageConstants.MSG_TACIT_TEST -> {
            ITEM_TACIT_TEST
        }

        MessageConstants.MSG_MEDIA_TYPE_MEETUP_TASK -> {
            ITEM_MEETING_PLAN_TASK
        }

        MessageConstants.MSG_MEDIA_TYPE_MEETUP_INVITE -> {
            ITEM_MEETING_PLAN_INVITE
        }

        MessageConstants.MSG_MEDIA_TYPE_MEET_PROTECT -> {
            ITEM_MEETING_PROTECT
        }


        else -> {
            ITEM_UPDATE_FROM
        }
    }
}

fun getLikeCardViewType(messageRecord: ChatMessage): Int {
    val isFriend = !MessageUtils.isAuthor(messageRecord)
    var type = if (isFriend) ITEM_LIKE_CARD_FROM else ITEM_LIKE_CARD_TO
    if (messageRecord is MessageForLike) {
        val likeInfo = messageRecord.likeInfo
        val resourceType = likeInfo.resourceType
        when (resourceType) {
            Constants.TYPE_LIKE_USER -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM else ITEM_LIKE_CARD_TO

            Constants.TYPE_LIKE_AVATAR -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_PROFILE else ITEM_LIKE_CARD_TO_PROFILE

            Constants.TYPE_LIKE_ABOUT_ME -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_ABOUT_ME else ITEM_LIKE_CARD_TO_ABOUT_ME

            Constants.TYPE_LIKE_IDEAL_PARTNER_DESC -> type =
                if (isFriend) ITEM_IDEAL_PARTNER_DESC_FROM else ITEM_IDEAL_PARTNER_DESC_TO

            Constants.TYPE_LIKE_STORY -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_STORY else ITEM_LIKE_CARD_TO_STORY

            Constants.TYPE_LIKE_VOICE -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_VOICE else ITEM_LIKE_CARD_TO_VOICE

            Constants.TYPE_LIKE_TEXT -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_QA else ITEM_LIKE_CARD_TO_QA

            Constants.TYPE_LIKE_A_B_FACE -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_AB else ITEM_LIKE_CARD_TO_AB

            Constants.TYPE_LIKE_MOOD -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_MOOD else ITEM_LIKE_CARD_TO_MOOD

            Constants.TYPE_LIKE_HOBBY -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_HOBBY else ITEM_LIKE_CARD_TO_HOBBY

            Constants.TYPE_LIKE_SINGLE_REASON -> type =
                if (isFriend) ITEM_SINGLE_REASON_FROM else ITEM_SINGLE_REASON_TO

            Constants.TYPE_LIKE_ABOUT_ME_PIC -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_ABOUT_ME_PIC else ITEM_LIKE_CARD_TO_ABOUT_ME_PIC

            Constants.TYPE_LIKE_HOBBY_PIC -> type =
                if (isFriend) ITEM_LIKE_CARD_FROM_HOBBY_PIC else ITEM_LIKE_CARD_TO_HOBBY_PIC

            Constants.TYPE_ACTIVITY_PERSONAL_PHOTO -> type =
                if (isFriend) ITEM_ACTIVITY_PERSONAL_PHOTO_FROM else ITEM_ACTIVITY_PERSONAL_PHOTO_TO

            else -> {
                if (isFriend) ITEM_LIKE_CARD_FROM else ITEM_LIKE_CARD_TO
            }
        }
    }
    return type
}

fun createViewDataBinding(
    inflater: LayoutInflater,
    parent: ViewGroup,
    viewType: Int,
): ViewDataBinding {
    return when (viewType) {
        ITEM_FEEDBACK_REPLAY -> {
            ChatItemFeedbackReplayCardBinding.inflate(inflater, parent, false)
        }

        ITEM_TACIT_TEST -> {
            ChatItemTacitTestBinding.inflate(inflater, parent, false)
        }

        ITEM_MEETING_PLAN_TASK -> {
            ChatItemMeetingPlanTaskBinding.inflate(inflater, parent, false)
        }

        ITEM_MEETING_PLAN_INVITE -> {
            ChatItemMeetingPlanInviteBinding.inflate(inflater, parent, false)
        }

        ITEM_MEETING_PROTECT ->{
            ChatItemMeetingPlanProtectBinding.inflate(inflater, parent, false)
        }

        else -> {
            createLikeCardViewDataBinding(inflater, parent, viewType)
        }
    }
}

fun createLikeCardViewDataBinding(
    inflater: LayoutInflater,
    parent: ViewGroup,
    viewType: Int,
): ViewDataBinding {
    return when (viewType) {
        ITEM_LIKE_CARD_TO -> {
            ChatViewMessageLikeTo2Binding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM -> {
            ChatViewMessageLikeFrom2Binding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_PROFILE -> {
            ChatViewMessageLikeTo2ProfileBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_PROFILE -> {
            ChatViewMessageLikeFrom2ProfileBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_ABOUT_ME -> {
            ChatViewMessageLikeTo2AboutMeBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_ABOUT_ME -> {
            ChatViewMessageLikeFrom2AboutMeBinding.inflate(inflater, parent, false)
        }

        ITEM_IDEAL_PARTNER_DESC_TO -> {
            ChatViewMessageLikeTo2IdealBinding.inflate(inflater, parent, false)
        }

        ITEM_IDEAL_PARTNER_DESC_FROM -> {
            ChatViewMessageLikeFrom2IdealBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_STORY -> {
            ChatViewMessageLikeTo2StoryBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_STORY -> {
            ChatViewMessageLikeFrom2StoryBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_VOICE -> {
            ChatViewMessageLikeTo2VoiceBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_VOICE -> {
            ChatViewMessageLikeFrom2VoiceBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_QA -> {
            ChatViewMessageLikeTo2QaBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_QA -> {
            ChatViewMessageLikeFrom2QaBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_AB -> {
            ChatViewMessageLikeTo2AbBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_AB -> {
            ChatViewMessageLikeFrom2AbBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_MOOD -> {
            ChatViewMessageLikeMoodToBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_MOOD -> {
            ChatViewMessageLikeMoodFromBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_HOBBY -> {
            ChatViewMessageLikeTo2HobbyBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_HOBBY -> {
            ChatViewMessageLikeFrom2HobbyBinding.inflate(inflater, parent, false)
        }

        ITEM_SINGLE_REASON_FROM -> {
            ChatViewMessageLikeFrom2SingleReasonBinding.inflate(inflater, parent, false)
        }

        ITEM_SINGLE_REASON_TO -> {
            ChatViewMessageLikeTo2SingleReasonBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_ABOUT_ME_PIC -> {
            ChatViewMessageLikeTo2AboutMePicBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_ABOUT_ME_PIC -> {
            ChatViewMessageLikeFrom2AboutMePicBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_TO_HOBBY_PIC -> {
            ChatViewMessageLikeTo2HobbyPicBinding.inflate(inflater, parent, false)
        }

        ITEM_LIKE_CARD_FROM_HOBBY_PIC -> {
            ChatViewMessageLikeFrom2HobbyPicBinding.inflate(inflater, parent, false)
        }

        ITEM_ACTIVITY_PERSONAL_PHOTO_TO -> {
            ChatViewMessageLikeToActivityPersonalPhotoBinding.inflate(inflater, parent, false)
        }

        ITEM_ACTIVITY_PERSONAL_PHOTO_FROM -> {
            ChatViewMessageLikeFromActivityPersonalPhotoBinding.inflate(inflater, parent, false)
        }

        else -> {
            ChatItemMessageUpdateBinding.inflate(inflater, parent, false)
        }
    }
}