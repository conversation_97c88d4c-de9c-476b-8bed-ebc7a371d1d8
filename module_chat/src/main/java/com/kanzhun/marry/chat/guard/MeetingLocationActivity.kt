package com.kanzhun.marry.chat.guard

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.compose.runtime.Composable
import androidx.lifecycle.viewmodel.compose.viewModel
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.ChatPageRouterKT.KEY_RECORD_ID
import com.kanzhun.marry.chat.guard.ui.MeetingLocationScreen
import com.kanzhun.marry.chat.guard.viewmodel.MeetingLocationViewModel
import com.sankuai.waimai.router.annotation.RouterUri

/**
 * Activity for selecting a meeting location
 */
@RouterUri(path = [ChatPageRouterKT.MEETING_LOCATION_ACTIVITY])
class MeetingLocationActivity : BaseComposeActivity() {

    // Record ID from intent
    private var recordId: String? = null

    companion object {
        /**
         * Create an intent to start this activity
         */
        fun getIntent(context: Context): Intent {
            return Intent(context, MeetingLocationActivity::class.java)
        }

        /**
         * Start this activity
         */
        fun start(context: Context) {
            AppUtil.startActivity(context, getIntent(context))
        }
    }

    override fun enableSafeDrawingPadding() = false

    override fun onCreate(savedInstanceState: Bundle?) {
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        super.onCreate(savedInstanceState)

        // Get record ID from intent
        recordId = intent.getStringExtra(KEY_RECORD_ID)
    }

    @Composable
    override fun OnSetContent() {
        val viewModel: MeetingLocationViewModel = viewModel()

        // Fetch meeting plan setting detail when the composable is first created
        androidx.compose.runtime.LaunchedEffect(Unit) {
            recordId?.let { id ->
                if (id.isNotEmpty()) {
                    viewModel.getMeetingPlanSettingDetail(id)
                }
            }
        }

        MeetingLocationScreen(
            viewModel = viewModel,
            onBackClick = { AppUtil.finishActivity(this) },
            onCancelSuccess = { AppUtil.finishActivity(this) } // 取消见面成功后退出 Activity
        )
    }
}
