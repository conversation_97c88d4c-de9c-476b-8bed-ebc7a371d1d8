package com.kanzhun.marry.chat.fragment;

import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.foundation.base.fragment.FoundationVMShareFragment;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.callback.LoveCardFinishedCallback;
import com.kanzhun.marry.chat.databinding.ChatFragmentLoveCardFinishedBinding;
import com.kanzhun.marry.chat.databinding.ChatFragmentLoveCardFinishedSenderBinding;
import com.kanzhun.marry.chat.viewmodel.LoveCardDetailViewModel;
import com.kanzhun.marry.chat.viewmodel.LoveCardSenderFinishedViewModel;

public class LoveCardFinishedSenderFragment extends FoundationVMShareFragment<ChatFragmentLoveCardFinishedSenderBinding, LoveCardSenderFinishedViewModel, LoveCardDetailViewModel> implements LoveCardFinishedCallback {

    @Override
    protected void initFragment() {
        super.initFragment();

    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getActivityBindingVariable() {
        return BR.activityViewModel;
    }

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_fragment_love_card_finished_sender;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void clickClose() {
        AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void clickContinue() {
        AppUtil.finishActivity(activity, ActivityAnimType.UP_GLIDE);
    }

}