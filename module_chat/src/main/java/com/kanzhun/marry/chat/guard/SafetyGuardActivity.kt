package com.kanzhun.marry.chat.guard

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.ContactsContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.net.toUri
import androidx.lifecycle.viewmodel.compose.viewModel
import com.gyf.immersionbar.ktx.immersionBar
import com.kanzhun.common.base.compose.BaseComposeActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.kotlin.ktx.getPageSource
import com.kanzhun.foundation.permission.PermissionHelper
import com.kanzhun.foundation.router.ChatPageRouterKT
import com.kanzhun.foundation.router.ChatPageRouterKT.KEY_PEER_ID
import com.kanzhun.foundation.router.ChatPageRouterKT.jumpToSafetyGuardActivity
import com.kanzhun.marry.chat.guard.model.SafetyContact
import com.kanzhun.marry.chat.guard.ui.SafetyGuardScreen
import com.kanzhun.marry.chat.guard.ui.SafetyGuardianCallSheet
import com.kanzhun.marry.chat.guard.viewmodel.SafetyGuardViewModel
import com.kanzhun.utils.T
import com.sankuai.waimai.router.annotation.RouterUri

/**
 * Activity for the Safety Guard page
 */
@RouterUri(path = [ChatPageRouterKT.SAFETY_GUARD_ACTIVITY])
class SafetyGuardActivity : BaseComposeActivity() {

    private val viewModel: SafetyGuardViewModel by viewModels()

    // Contact picker launcher
    private val contactPickerLauncher = registerForActivityResult(
        ActivityResultContracts.PickContact()
    ) { uri ->
        uri?.let { contactUri ->
            val contact = getContactFromUri(contactUri)
            contact?.let {
                viewModel.cachePickedSafetyContact(it)
            }
        }
    }

    override fun enableSafeDrawingPadding() = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Set status bar to be transparent with dark icons
        immersionBar {
            statusBarDarkFont(true)
            transparentStatusBar()
        }

        viewModel.loadGuardianList(
            pageSource = intent.getPageSource(),
            peerId = intent.getStringExtra(KEY_PEER_ID).toString()
        )
    }

    @Composable
    override fun OnSetContent() {
        val context = LocalContext.current
        val safetyGuardViewModel: SafetyGuardViewModel = viewModel()

        // 使用 collectAsState 收集安全守护人列表的状态
        val guardians = safetyGuardViewModel.safetyContacts

        // 显示安全守护人列表的弹框
        var showGuardiansDialog by remember { mutableStateOf(false) }

        SafetyGuardScreen(
            viewModel = viewModel,
            onBackClick = { AppUtil.finishActivity(this) },

            peerId = intent.getStringExtra(KEY_PEER_ID).toString(),

            onSelectContact = { requestContactsPermission() },
            pickedSafetyContact = viewModel.pickedSafetyContacts,
            onAddNewContact = { contact -> viewModel.addSafetyContact(contact) },

            onContactFriendsClick = {
                if (guardians.isEmpty()) {
                    // 如果没有安全守护人，提示用户并跳转到安全守护页面
                    T.ss("请先添加安全守护人")
                    jumpToSafetyGuardActivity(context)
                } else {
                    // 如果有安全守护人，显示安全守护人列表
                    showGuardiansDialog = true
                }
            },
            onCallEmergencyClick = { callEmergency() }
        )

        // 如果显示守护人列表弹框
        if (showGuardiansDialog && guardians.isNotEmpty()) {
            SafetyGuardianCallSheet(
                guardians = guardians,
                onDismiss = { showGuardiansDialog = false }
            )
        }
    }

    /**
     * Request contacts permission
     */
    private fun requestContactsPermission() {
        PermissionHelper.getContactsHelper(this)
            .setPermissionCallback { granted, _ ->
                if (granted) {
                    openContactPicker()
                } else {
                    T.ss("请允许访问通讯录权限")
                }
            }
            .requestPermission()
    }

    /**
     * Open contact picker
     */
    private fun openContactPicker() {
        contactPickerLauncher.launch(null)
    }

    /**
     * Get contact from URI
     */
    private fun getContactFromUri(uri: Uri): SafetyContact? {
        // First get the contact ID from the contact URI
        val contactIdProjection =
            arrayOf(ContactsContract.Contacts._ID, ContactsContract.Contacts.DISPLAY_NAME)
        var contactId: String? = null
        var displayName: String? = null

        contentResolver.query(uri, contactIdProjection, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val idIndex = cursor.getColumnIndex(ContactsContract.Contacts._ID)
                val nameIndex = cursor.getColumnIndex(ContactsContract.Contacts.DISPLAY_NAME)

                if (idIndex >= 0 && nameIndex >= 0) {
                    contactId = cursor.getString(idIndex)
                    displayName = cursor.getString(nameIndex)
                }
            }
        }

        if (contactId == null || displayName == null) {
            return null
        }

        // Now get the phone number using the contact ID
        val phoneUri = ContactsContract.CommonDataKinds.Phone.CONTENT_URI
        val phoneProjection = arrayOf(ContactsContract.CommonDataKinds.Phone.NUMBER)
        val phoneSelection = "${ContactsContract.CommonDataKinds.Phone.CONTACT_ID} = ?"
        val phoneSelectionArgs = arrayOf(contactId)

        contentResolver.query(phoneUri, phoneProjection, phoneSelection, phoneSelectionArgs, null)
            ?.use { cursor ->
                if (cursor.moveToFirst()) {
                    val numberIndex =
                        cursor.getColumnIndex(ContactsContract.CommonDataKinds.Phone.NUMBER)
                    if (numberIndex >= 0) {
                        val phoneNumber =
                            cursor.getString(numberIndex)?.replace("\\s+".toRegex(), "")
                        if (!phoneNumber.isNullOrEmpty()) {
                            return SafetyContact(
                                name = displayName,
                                phoneNumber = phoneNumber,
                                maskedPhoneNumber = formatPhoneNumber(phoneNumber)
                            )
                        }
                    }
                }
            }

        return null
    }

    /**
     * Format phone number as 186****1111
     */
    private fun formatPhoneNumber(phoneNumber: String): String {
        val cleanNumber = phoneNumber.replace(Regex("[^0-9]"), "")
        if (cleanNumber.length < 7) return cleanNumber

        val prefix = cleanNumber.take(3)
        val suffix = cleanNumber.takeLast(4)

        return "$prefix****$suffix"
    }

    /**
     * Call emergency action
     */
    @SuppressLint("QueryPermissionsNeeded")
    private fun callEmergency() {
        try {
            val intent = Intent(Intent.ACTION_DIAL).apply {
                data = "tel:110".toUri()
            }

            if (intent.resolveActivity(packageManager) != null) {
                startActivity(intent)
            } else {
                T.ss("无法拨打电话")
            }
        } catch (_: Exception) {

        }
    }

    companion object {
        /**
         * Start the Safety Guard activity
         */
        fun start(context: Context) {
            val intent = Intent(context, SafetyGuardActivity::class.java)
            context.startActivity(intent)
        }
    }

    @Preview
    @Composable
    private fun ContentPreview() {
        SafetyGuardScreen()
    }
}
