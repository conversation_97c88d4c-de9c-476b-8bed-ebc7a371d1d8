package com.kanzhun.marry.chat.activity;

import android.os.Bundle;

import com.kanzhun.common.kotlin.ui.statusbar.StatusBarKt;
import com.kanzhun.common.util.ActivityAnimType;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.base.activity.FoundationVMActivity;
import com.kanzhun.foundation.router.ChatPageRouter;
import com.kanzhun.foundation.statelayout.StateLayoutManager;
import com.kanzhun.marry.chat.BR;
import com.kanzhun.marry.chat.R;
import com.kanzhun.marry.chat.callback.LoveCardCallback;
import com.kanzhun.marry.chat.databinding.ChatActivityLoveCardBinding;
import com.kanzhun.marry.chat.fragment.LoveCardContinueFragment;
import com.kanzhun.marry.chat.fragment.LoveCardWriteFragment;
import com.kanzhun.marry.chat.viewmodel.LoveCardViewModel;
import com.sankuai.waimai.router.annotation.RouterUri;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * 消息 - 表白信
 * <p>
 * Created by <PERSON><PERSON>yong on 2022/5/26
 */
@RouterUri(path = ChatPageRouter.LOVE_CARD_ACTIVITY)
public class LoveCardActivity extends FoundationVMActivity<ChatActivityLoveCardBinding, LoveCardViewModel> implements LoveCardCallback {

    private StateLayoutManager stateLayoutManager;

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_activity_love_card;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        stateLayoutManager = new StateLayoutManager(this, getDataBinding().fragmentContent);
        StatusBarKt.darkMode(this,false);
        StatusBarKt.addStatusMargin(getDataBinding().titleBar);
        getDataBinding().titleBar.setBackButtonRes(R.mipmap.common_ic_close_black);
        getDataBinding().titleBar.asBackButton(new Function0<Unit>() {
            @Override
            public Unit invoke() {
                clickClose();
                return null;
            }
        });

        getViewModel().chatId = getIntent().getStringExtra(BundleConstants.BUNDLE_CHAT_ID);
        getViewModel().fromName = getIntent().getStringExtra(BundleConstants.BUNDLE_FROM_NAME);
        getViewModel().toName = getIntent().getStringExtra(BundleConstants.BUNDLE_TO_NAME);
        getViewModel().checkCardStatus(2);

        getViewModel().getLoadingLiveData().observe(this, show -> {
            if (show) {
                stateLayoutManager.showLoadingView();
            } else {
                stateLayoutManager.dismiss();
            }
        });
        getViewModel().getCardStatusLiveData().observe(this, cardStatusBean -> {
            if (cardStatusBean == null) return;
            getSupportFragmentManager()
                    .beginTransaction()
                    .replace(R.id.fragment_content, cardStatusBean.status == 1
                            ? new LoveCardContinueFragment() : new LoveCardWriteFragment())
                    .commit();
        });
    }

    @Override
    protected boolean shouldFullScreen() {
        return true;
    }

    @Override
    public int getBindingVariable() {
        return BR.viewModel;
    }

    @Override
    public int getCallbackVariable() {
        return BR.callback;
    }

    @Override
    public Object getCallback() {
        return this;
    }

    @Override
    public void finish() {
        super.finish();
        AppUtil.finishActivityAnim(this, ActivityAnimType.UP_GLIDE);
    }

    @Override
    public void clickClose() {
        AppUtil.finishActivity(this, ActivityAnimType.UP_GLIDE);
    }
}