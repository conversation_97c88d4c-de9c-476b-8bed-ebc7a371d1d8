package com.kanzhun.marry.chat.emotion.data

import android.text.TextUtils
import com.kanzhun.marry.chat.R
import com.techwolf.lib.tlog.TLog

private const val TAG = "EmotionInnerManager"

class EmotionInnerManager {
    companion object {
        private val mInnerEmotionList: MutableList<EmotionItem> = mutableListOf()

        /**
         * 获取黄豆人表情列表
         */
        fun getInnerEmotionList(): MutableList<EmotionItem> {
            if (mInnerEmotionList.isEmpty()) {
                initInnerEmotionList()
            }
            return mInnerEmotionList
        }

        private fun initInnerEmotionList() {
            initNewItem(1, "[微笑]", R.mipmap.new_expression_weixiao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/02d34550bb00842632bab60ad6da9d09.webp")
            initNewItem(2, "[憨笑]", R.mipmap.new_expression_hanxiao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/855e28fc1e6ec8bcaae84074838a727c.webp")
            initNewItem(3, "[偷笑]", R.mipmap.new_expression_touxiao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/7ca184f119ccc2ca214478687fd0d11d.webp")
            initNewItem(4, "[呲牙]", R.mipmap.new_expression_ciya, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/f71a939b795045f41549b5020c071e50.webp")
            initNewItem(5, "[开心]", R.mipmap.new_expression_kaixin, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/8add2d248231e5d790f6b899f525766c.webp")
            initNewItem(6, "[害羞]", R.mipmap.new_expression_haixiu, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/9a495393a0db1efa1028d14a4c109016.webp")
            initNewItem(7, "[调皮]", R.mipmap.new_expression_tiaopi, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/8da64cd7a22e17ddaf5fcf964f61ad4f.webp")
            initNewItem(8, "[发呆]", R.mipmap.new_expression_fadai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/5a11997ee2587274a7ab159169be632f.webp")
            initNewItem(9, "[坏笑]", R.mipmap.new_expression_huaixiao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/78c35e0b518e8d3ff47d8115a5d81e04.webp")
            initNewItem(10, "[淘气]", R.mipmap.new_expression_taoqi, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/4adf932599e24c2ba060145d13f42792.webp")
            initNewItem(11, "[鼓掌]", R.mipmap.new_expression_guzhang, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/2259fb8cb42e6eb704a92b247a4b2399.webp")
            initNewItem(12, "[抠鼻]", R.mipmap.new_expression_koubi, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/78d3ce099535a99b22dfa038e6374ce5.webp")
            initNewItem(13, "[流汗]", R.mipmap.new_expression_liuhan, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/fcdc03ae86e45ce51b6ed04187b041c5.webp")
            initNewItem(14, "[奋斗]", R.mipmap.new_expression_fendou, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/fc5016a254a4d4ffad6f743315de1d33.webp")
            initNewItem(15, "[尴尬]", R.mipmap.new_expression_ganga, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/adf786f7aef0aa595c2dad02716cb1ec.webp")
            initNewItem(16, "[感动]", R.mipmap.new_expression_gandong, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/2c96f28c98803394b5e9a90c64a3c718.webp")
            initNewItem(17, "[委屈]", R.mipmap.new_expression_weiqu, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/8417d8626f3d7fa8769095fcd0b87cff.webp")
            initNewItem(18, "[笑哭]", R.mipmap.new_expression_xiaoku, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a5d581f7bb6fbb2bfca2718efa1128f4.webp")
            initNewItem(19, "[难过]", R.mipmap.new_expression_nanguo, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/f964790d7be20a789ce60ef4cafc9682.webp")
            initNewItem(20, "[流泪]", R.mipmap.new_expression_liulei, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/c67c8af5560d4edc43e00c2fdd605d31.webp")
            initNewItem(21, "[石化]", R.mipmap.new_expression_shihua, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/53751a3ade7f918f3214dce75c7d557f.webp")
            initNewItem(24, "[牛]", R.mipmap.new_expression_niu, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/e50bec8098fb4f8aa2915fb95a53afe9.webp")
            initNewItem(25, "[生病]", R.mipmap.new_expression_shengbing, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/5084e8da187faed748112c6d6218ca3f.webp")
            initNewItem(26, "[疑问]", R.mipmap.new_expression_yiwen, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/ac944a68d8f91e4c9cc572b10ab8686f.webp")
            initNewItem(27, "[不屑]", R.mipmap.new_expression_buxie, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/d736fad71b8cf794e44401955dffce1c.webp")
            initNewItem(28, "[生气]", R.mipmap.new_expression_fanu, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/3fc4f30c134beca428eb41e85385a5f1.webp")
            initNewItem(29, "[无奈]", R.mipmap.new_expression_wunai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/bca6422becba855cd1af558ed8857833.webp")
            initNewItem(30, "[惊呆]", R.mipmap.new_expression_jingdai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/b80f7fc73ce364d6fa3299e62b467a85.webp")
            initNewItem(31, "[酷]", R.mipmap.new_expression_ku, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a68ed1b20451251b82c2711c632bc0d9.webp")
            initNewItem(32, "[钱]", R.mipmap.new_expression_qian, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/149f7c0d354defdf12847fcb4d9ed657.webp")
            initNewItem(33, "[敬礼]", R.mipmap.new_expression_jingli, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/78c11ae3471c1d488e3a047ab1863c10.webp")
            initNewItem(34, "[困]", R.mipmap.new_expression_kun, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/b5951d77b52d734336599d09b29cf28b.webp")
            initNewItem(35, "[衰]", R.mipmap.new_expression_shuai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/ab93b26d03b6ffcdc832580e43cddd46.webp")
            initNewItem(36, "[喝可乐]", R.mipmap.new_expression_hekele, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/fd9c8a25c04aa9e5bd6864fc32741289.webp")
            initNewItem(37, "[晕]", R.mipmap.new_expression_yun, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/3beb407693b59de5867474d2660a3a40.webp")
            initNewItem(38, "[等待]", R.mipmap.new_expression_dengdai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a3600b832a1606cfbe379b95c0d17566.webp")
            initNewItem(39, "[吃惊]", R.mipmap.new_expression_chijing, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/28044345bddcceb319d36c55807afbc6.webp")
            initNewItem(40, "[挑眉]", R.mipmap.new_expression_tiaomei, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/9d8529137c503d727c47176509498929.webp")
            initNewItem(41, "[呵呵]", R.mipmap.new_expression_hehe, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/7e660e689054c27f0acc7fcece83805a.webp")
            initNewItem(42, "[晚安]", R.mipmap.new_expression_wanan, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/b01e4849e011ba3a4372fa6a80c7f2cd.webp")
            initNewItem(43, "[捂脸]", R.mipmap.new_expression_wulian, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/ba65c12052c75b2b5f5e1446ea17437c.webp")
            initNewItem(44, "[比心]", R.mipmap.new_expression_bixin, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/dd0626a38ebe6061f9adb0bf76a1a32d.webp")
            initNewItem(45, "[机智]", R.mipmap.new_expression_jizhi, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/6cf663340270964abbe155640dfe9192.webp") //有问题
            initNewItem(46, "[社会社会]", R.mipmap.new_expression_shehuishehui, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/c596deed25a0b72938102fe6845e0c81.webp")
            initNewItem(47, "[裂开]", R.mipmap.new_expression_liekai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/480c3ad13ed0aace4640b347dd6cd5bb.webp")
            initNewItem(48, "[加油]", R.mipmap.new_expression_jiayou, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/48555dfb79832060972c222e8d633cc1.webp")
            initNewItem(49, "[期待]", R.mipmap.new_expression_qidai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/42a0d878e6a1633843e20716d0ffe709.webp")
            initNewItem(53, "[爱心]", R.mipmap.new_expression_aixin, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/71c922a85cb227f14eea08263f8c049c.webp")
            initNewItem(54, "[心碎]", R.mipmap.new_expression_xinsui, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/5868b9d74668fdf51e771d9e07668aeb.webp")
            initNewItem(55, "[猫头]", R.mipmap.new_expression_maotou, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/316d621a556c346c3c6e7694fc5e5e8a.webp")
            initNewItem(56, "[狗头]", R.mipmap.new_expression_goutou, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/4d9e4f896564b3ddabfeb58de44adb9f.webp")
            initNewItem(57, "[干杯]", R.mipmap.new_expression_ganbei, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/cac4781706e6ece6faf0fe1d6c9fa2f1.webp")
            initNewItem(58, "[玫瑰]", R.mipmap.new_expression_meigui, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/6c56be58099b8999b05beb313a998d7b.webp")
            initNewItem(59, "[闹钟]", R.mipmap.new_expression_naozhong, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/4f61a095b2591e8bc5b02b06b8896820.webp")
            initNewItem(60, "[月亮]", R.mipmap.new_expression_yueliang, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/71d125f4e39a26acbce14d8f79ec7765.webp")

            //60-66
            initNewItem(61, "[太阳]", R.mipmap.new_expression_taiyang, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/2e78b84bda1b2ec994161358928b2b34.webp")
            initNewItem(62, "[幽灵]", R.mipmap.new_expression_youling, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/5e52be51c2e4827e68b764b087484cbb.webp")
            initNewItem(63, "[抱抱]", R.mipmap.new_expression_baobao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/5658cf1c4d83214fc2965d3cd7f77271.webp")
            initNewItem(64, "[格局]", R.mipmap.new_expression_geju, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a3f01800f20d5e305c61dad12b791772.webp")
            initNewItem(66, "[冲鸭]", R.mipmap.new_expression_chongya, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/236da1a6836c4b1f2b40406692239bc1.webp")
            initNewItem(70, "[hi]", R.mipmap.new_expression_hi, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/8ff8608f37214f56df90476f8bd6b5aa.webp")
            initNewItem(71, "[握手]", R.mipmap.new_expression_woshou, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a38ce1b78a598fe25b236cfd1dd707e2.webp")
            initNewItem(72, "[赞]", R.mipmap.new_expression_zan, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/19bd155691e8c7d8daf43a16a423e134.webp")
            initNewItem(73, "[来]", R.mipmap.new_expression_lai, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/6c68346b82fffd6ab0bd0c117a1ae345.webp")
            initNewItem(74, "[OK]", R.mipmap.new_expression_ok, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/57138ced0c2d6998b27e380ff77a5b1c.webp")
            initNewItem(75, "[祈祷]", R.mipmap.new_expression_qidao, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/9b0fb26118a1f55e2d8a1772a772dd24.webp")
            initNewItem(76, "[抱拳]", R.mipmap.new_expression_baoquan, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/4feb5b44ce96d82db450cbcb662e4751.webp")
            initNewItem(80, "[鸡腿]", R.mipmap.new_expression_jitui, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/95a7e14a80982feeab398ebedeece25d.webp")
            initNewItem(81, "[可]", R.mipmap.new_expression_ke, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/a4a432a2b208d2f24eac385bd69ebb64.webp")
            initNewItem(83, "[get]", R.mipmap.new_expression_get, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/3879c74b42beeb7751955fdd7af6c64c.webp")
            initNewItem(84, "[emo]", R.mipmap.new_expression_emo, "https://img.bosszhipin.com/beijin/mcs/sticker/20221101/e3450f3b4b588e65dd8ba4c77f3f2a42.webp")
        }


        private fun initNewItem(id: Long, name: String, resourceId: Int, webpUrl: String) {
            mInnerEmotionList.add(EmotionItem(id = id, name = name,
                origUrl = webpUrl, packId = EmotionDataManager.EMOTION_INNER_PACK_ID.toString(), resId = resourceId).also {
                it.groupType = EmotionGroupType.INNER
            })
        }

        //从集合查找指定的表情
        fun findGifByName(text: String): EmotionItem? {
            try {
                if (TextUtils.isEmpty(text)) return null
                val newInnerEmotionList= getInnerEmotionList()
                for (emotion in newInnerEmotionList) {
                    if (TextUtils.equals(emotion.name, text)) {
                        return emotion
                    }
                }
                return null
            } catch (e: Throwable) {
                TLog.error(TAG, "findGifByName: $e")
                return null
            }
        }


        // 通过文字匹配到【表情】
        fun getResourceId(text: String): Int {
            val targetGifItem = findGifByName(text)
            return targetGifItem?.resId ?: 0
        }

    }
}