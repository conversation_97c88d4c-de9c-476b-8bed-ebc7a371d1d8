package com.kanzhun.marry.chat.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.adapter.DataBoundListAdapter;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.listener.ChatItemListener;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageForHint;
import com.kanzhun.foundation.model.message.MessageForTopicGame;
import com.kanzhun.marry.chat.callback.ListViewChangeListener;
import com.kanzhun.marry.chat.factory.ItemFactory;
import com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel;
import com.kanzhun.utils.L;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;


/**
 * Author: Zhang Lishun
 * Date: 2019/03/13.
 */
public class ChatAdapter extends DataBoundListAdapter<ChatMessage, ViewDataBinding> {
    public static final String TAG = ChatAdapter.class.getSimpleName();
    private ChatItemListener mChatItemListener;
    private ChatBaseViewModel chatViewModel;
    private List<ChatMessage> mList = new ArrayList<>();
    private ListViewChangeListener mChangeListener;

    public ChatAdapter(ChatItemListener chatItemListener) {
        this(ExecutorFactory.getIOExecutor(), new DiffUtil.ItemCallback<ChatMessage>() {
            @Override
            public boolean areItemsTheSame(ChatMessage oldItem, ChatMessage newItem) {
                return oldItem.getMid() == newItem.getMid() || (oldItem.getCmid() >0
                        && oldItem.getCmid() == newItem.getCmid() && AccountHelper.getInstance().getUserId().equals(oldItem.getSender()));
            }

            @Override
            public boolean areContentsTheSame(ChatMessage oldItem, ChatMessage newItem) {
                boolean same = true;
                if(oldItem instanceof MessageForHint || newItem instanceof MessageForHint){
                    return false;
                }
                if (oldItem instanceof MessageForTopicGame && newItem instanceof MessageForTopicGame) {
                    MessageForTopicGame oldMsg = ((MessageForTopicGame) oldItem);
                    MessageForTopicGame newMsg = ((MessageForTopicGame) newItem);
                    same = oldMsg.getTopicGameStatus() == newMsg.getTopicGameStatus();
                }
                if (oldItem.getQuoteMessage() != null && newItem.getQuoteMessage() != null) {
                    ChatMessage oldQuoteMessage = oldItem.getQuoteMessage();
                    ChatMessage newQuoteMessage = newItem.getQuoteMessage();
                    if (oldQuoteMessage.getWithdraw() != newQuoteMessage.getWithdraw() ||
                            oldQuoteMessage.getMediaType() != newQuoteMessage.getMediaType() ||
                            oldQuoteMessage.isShow() != newQuoteMessage.isShow() ||
                            !TextUtils.equals(oldQuoteMessage.getContent(), newQuoteMessage.getContent())) {
                        return false;
                    }
                }else if(oldItem.getMid() == oldItem.getMid() && oldItem.getReplyId()>0 && oldItem.getQuoteMessage() == null){
                    return false;
                }
//                return false;
                TLog.print("areContentsTheSame","getIllegalText %s : %s",oldItem.getIllegalText(),newItem.getIllegalText());
                TLog.print("areContentsTheSame","getMediaType %s : %s",oldItem.getMediaType(),newItem.getMediaType());
                TLog.print("areContentsTheSame","getMid %s : %s",oldItem.getMid(),newItem.getMid());
                TLog.print("areContentsTheSame","getStatus %s : %s",oldItem.getStatus(),newItem.getStatus());
                TLog.print("areContentsTheSame","getWithdraw %s : %s",oldItem.getWithdraw(),newItem.getWithdraw());
                TLog.print("areContentsTheSame","getReplyId %s : %s",oldItem.getReplyId(),newItem.getReplyId());
                TLog.print("areContentsTheSame","getContent %s : %s",oldItem.getContent(),newItem.getContent());
                TLog.print("areContentsTheSame","getExtension %s : %s",oldItem.getExtension(),newItem.getExtension());
                TLog.print("areContentsTheSame","getRelationStatus %s : %s",oldItem.getRelationStatus(),newItem.getRelationStatus());
                TLog.print("areContentsTheSame","isDeleted %s : %s",oldItem.isDeleted(),newItem.isDeleted());
                boolean k = same
                        && TextUtils.equals(oldItem.getIllegalText(), newItem.getIllegalText())
                        && oldItem.getMediaType() == newItem.getMediaType()
                        && oldItem.getMid() == newItem.getMid()
                        && oldItem.getStatus() == newItem.getStatus()
                        && oldItem.getWithdraw() == newItem.getWithdraw()
                        && oldItem.getReplyId() == newItem.getReplyId()
                        && TextUtils.equals(oldItem.getContent(), newItem.getContent())
                        && TextUtils.equals(oldItem.getExtension(), newItem.getExtension())
                        && TextUtils.equals(oldItem.getExtStr(), newItem.getExtStr())
                        && oldItem.getRelationStatus() == newItem.getRelationStatus()
                        && oldItem.isDeleted() == newItem.isDeleted()
                        ;
                TLog.print("areContentsTheSame","same:%s"+same);
                return k;
            }

        }, chatItemListener);

    }

    public ChatAdapter(Executor bgExecutor, @NonNull DiffUtil.ItemCallback<ChatMessage> diffCallback, ChatItemListener chatItemListener) {
        super(bgExecutor, diffCallback);
        this.mChatItemListener = chatItemListener;
        registerAdapterDataObserver(new MyAdapterDataObserver());
    }

    public ChatBaseViewModel getChatViewModel() {
        return chatViewModel;
    }

    public void setChatViewModel(ChatBaseViewModel chatViewModel) {
        this.chatViewModel = chatViewModel;
    }

    public void registerChangeListener(ListViewChangeListener listener) {
        mChangeListener = listener;
    }

    @Override
    protected ViewDataBinding createBinding(ViewGroup parent, int viewType) {
        LayoutInflater inflater = LayoutInflater.from(parent.getContext());
        return ItemFactory.createViewDataBinding(inflater, parent, viewType);
    }

    @Override
    protected void bind(ViewDataBinding vdb, ChatMessage item, int position) {
        ItemFactory.bind(vdb, item, chatViewModel, mChatItemListener, position);
    }

    @Override
    public int getItemViewType(int position) {
        return ItemFactory.getViewType(getItem(position));
    }

    @Override
    public long getItemId(int position) {
        if (position >= 0 && position < getItemCount()) {
            return getItem(position).getMid();
        }
        return 0;
    }

    public long getItemSeq(int position) {
        if (position < 0) {
            position = 0;
        } else if (position >= getItemCount()) {
            position = getItemCount() - 1;
        }
        return getItem(position).getSeq();
    }

    @Override
    public void submitList(List<ChatMessage> list) {
        mList.clear();
        mList.addAll(list);
        super.submitList(list);
    }

    public List<ChatMessage> getData() {
        return mList;
    }

    class MyAdapterDataObserver extends RecyclerView.AdapterDataObserver {
        @Override
        public void onChanged() {
            super.onChanged();
            L.d(TAG, "onChanged() called");
            if (mChangeListener != null) {
                mChangeListener.onChange();
            }
        }

        @Override
        public void onItemRangeChanged(int positionStart, int itemCount) {
            super.onItemRangeChanged(positionStart, itemCount);
            L.d(TAG, "onItemRangeChanged() called with: positionStart = [" + positionStart + "], itemCount = [" + itemCount + "]");
        }

        @Override
        public void onItemRangeChanged(int positionStart, int itemCount, @Nullable Object payload) {
            super.onItemRangeChanged(positionStart, itemCount, payload);
            L.d(TAG, "onItemRangeChanged() called with: positionStart = [" + positionStart + "], itemCount = [" + itemCount + "], payload = [" + payload + "]");
        }

        @Override
        public void onItemRangeInserted(int positionStart, int itemCount) {
            super.onItemRangeInserted(positionStart, itemCount);
            L.d(TAG, "onItemRangeInserted() called with: positionStart = [" + positionStart + "], itemCount = [" + itemCount + "]");
            if (mChangeListener != null) {
                mChangeListener.onItemRangeInserted();
            }
        }

        @Override
        public void onItemRangeRemoved(int positionStart, int itemCount) {
            super.onItemRangeRemoved(positionStart, itemCount);
            L.d(TAG, "onItemRangeRemoved() called with: positionStart = [" + positionStart + "], itemCount = [" + itemCount + "]");
        }

        @Override
        public void onItemRangeMoved(int fromPosition, int toPosition, int itemCount) {
            super.onItemRangeMoved(fromPosition, toPosition, itemCount);
            L.d(TAG, "onItemRangeMoved() called with: fromPosition = [" + fromPosition + "], toPosition = [" + toPosition + "], itemCount = [" + itemCount + "]");
        }
    }
}
