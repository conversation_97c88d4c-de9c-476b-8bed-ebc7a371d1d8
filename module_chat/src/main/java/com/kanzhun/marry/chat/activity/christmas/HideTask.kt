package com.kanzhun.marry.chat.activity.christmas

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.kanzhun.foundation.model.Contact
import com.kanzhun.foundation.model.message.OgChristmasCard
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.marry.chat.R

// 7 隐藏任务卡
@Composable
fun HideTask(
    christmasCard: OgChristmasCard, modifier: Modifier = Modifier,
    contact: Contact? = Contact(),
) {
    var shouldReportExpose by rememberSaveable { mutableStateOf(true) }
    LaunchedEffect(shouldReportExpose) {
        if (shouldReportExpose) {
            // 上报曝光
            reportPoint("christ-hidecue-publish-expo") {
                actionp2 = christmasCard.content
            }
            shouldReportExpose = false
        }
    }

    SantaMessage(contact = contact, modifier = modifier) {
        ConstraintLayout(
            Modifier.Companion
                .fillMaxWidth()
                .background(
                    color = Color(0xFFFFF8EB),
                    shape = RoundedCornerShape(20.dp)
                )
        ) {
            val (content) = createRefs()

            Column(
                modifier = Modifier.Companion
                    .padding(16.dp)
                    .constrainAs(content) {
                        start.linkTo(parent.start)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom)
                        top.linkTo(parent.top)
                    },
                verticalArrangement = Arrangement.spacedBy(9.dp),
            ) {
                Text(
                    text = christmasCard.title
                        ?: "恭喜你完成了今日的全部任务，\n我还在app的各个地方埋了多个彩蛋等你去解锁，快去试试吧～",
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF191919),
                    )
                )

                Row(
                    modifier = Modifier.Companion
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .border(
                            width = 3.dp,
                            color = Color(0xFFF9EACD),
                            shape = RoundedCornerShape(size = 16.dp)
                        )
                        .padding(1.5.dp)
                        .background(
                            color = Color(0xFFFFFFFF),
                            shape = RoundedCornerShape(size = 16.dp)
                        )
                        .padding(16.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Image(
                        painter = painterResource(id = R.mipmap.chat_ic_tips_black),
                        contentDescription = "image description",
                        modifier = Modifier.Companion
                            .size(18.dp)
                    )

                    Text(
                        text = christmasCard.content ?: "可以去「个人信息编辑页」看看哦～",
                        style = TextStyle(
                            fontSize = 13.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF191919),
                        )
                    )
                }
            }

        }
    }
}

@Preview
@Composable
private fun PreviewHideTask() {
    HideTask(christmasCard = OgChristmasCard().apply {
        title =
            "恭喜你完成了今日的全部任务，我还在app的各个地方埋了多个彩蛋等你去解锁，找到对应页面点击圣诞元素小图标，即可获得糖果券，快去试试吧～"
        content = "可以去个人信息预览页看看哦～"
    })
}