package com.kanzhun.marry.chat.guard.util

import com.amap.api.maps.AMapUtils
import com.amap.api.maps.model.LatLng
import com.kanzhun.marry.chat.api.model.AddressModel
import com.kanzhun.marry.chat.api.model.NearbySearchResponse.PoiItem
import kotlin.math.roundToInt

fun toMyLocationDistance(myLocation: LatLng? = null, poiItem: PoiItem): String {
    val latitude = poiItem.latitude
    val longitude = poiItem.longitude
    if (latitude != null && longitude != null) {
        return toMyLocationDistance(myLocation, latitude, longitude)
    }
    return ""
}

fun toMyLocationDistance(myLocation: LatLng? = null, addressModel: AddressModel): String {
    // 经纬度：116.433555,39.921347
    val latLng = addressModel.gps?.split(",")
    val longitude =
        if (latLng != null && latLng.isNotEmpty() && latLng[0].isNotEmpty()) latLng[0] else null
    val latitude =
        if (latLng != null && latLng.size > 1 && latLng[1].isNotEmpty()) latLng[1] else null

    if (latitude != null && longitude != null) {
        return toMyLocationDistance(myLocation, latitude, longitude)
    }
    return ""
}

fun toMyLocationDistance(
    myLocation: LatLng? = null,
    latitude: String,
    longitude: String
): String {
    // 可以通过 AMapUtils.calculateLineDistance(LatLng startLatlng, LatLng endLatlng) 来计算两点距离，单位：米。
    if (myLocation != null) {
        val startLatlng = LatLng(myLocation.latitude, myLocation.longitude)
        val endLatlng =
            LatLng(latitude.toDouble(), longitude.toDouble())
        try {
            val distance =
                AMapUtils.calculateLineDistance(startLatlng, endLatlng).toString()
            return formatDistance(distance)
        } catch (_: Exception) {
            return ""
        }
    } else {
        return ""
    }
}

/**
 * 格式化距离显示
 * 1公里范围内显示米，精确到整数
 * 超过1公里，显示公里，精确到小数点后一位
 */
fun formatDistance(distance: String?): String {
    if (distance.isNullOrEmpty()) return ""

    try {
        val distanceValue = distance.toDouble()

        return if (distanceValue < 1000) {
            // 1公里范围内，显示米，精确到整数
            "${distanceValue.roundToInt()}米"
        } else {
            // 超过1公里，显示公里，精确到小数点后一位
            val kilometers = distanceValue / 1000
            val formattedKm = (kotlin.math.round(kilometers * 10) / 10.0)

            // 如果小数点后是0，则显示整数
            if (formattedKm == formattedKm.toInt().toDouble()) {
                "${formattedKm.toInt()}公里"
            } else {
                "${formattedKm}公里"
            }
        }
    } catch (_: Exception) {
        return ""
    }
}