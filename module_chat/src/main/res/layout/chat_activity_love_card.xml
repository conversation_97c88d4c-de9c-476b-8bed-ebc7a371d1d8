<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".LoveCardActivity">

    <data>

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.LoveCardViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.LoveCardCallback" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <ImageView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="h,375:230"
            app:layout_constraintTop_toTopOf="parent"
            android:src="@mipmap/chat_bg_love_card_page"/>

        <com.kanzhun.common.views.AppTitleView
            android:id="@+id/titleBar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <FrameLayout
            app:layout_constraintTop_toBottomOf="@id/titleBar"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:id="@+id/fragment_content"
            android:layout_width="match_parent"
            android:layout_height="0dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>