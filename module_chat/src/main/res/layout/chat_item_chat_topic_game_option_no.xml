<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="item"
            type="com.kanzhun.foundation.api.bean.TopicGameOptionBean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="16dp">

        <ImageView
            android:id="@+id/iv_option"
            android:layout_width="10dp"
            android:layout_height="10dp"
            android:layout_marginTop="4dp"
            android:src="@drawable/chat_bg_oval_color_7171f6_30"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_content" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{item.content}"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="16dp"
            android:textColor=" @color/common_color_707070"
            android:textSize="@dimen/common_text_sp_12"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@+id/iv_option"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="以父之小名以父之小名以父之小名以父之小名以父之小名" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>