<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_like_related"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_color_F5F5F5">

        <FrameLayout
            android:id="@+id/fl_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/iv_bg_top"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/ll_title_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fitsSystemWindows="true"
                android:orientation="vertical"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    layout="@layout/common_title_bar"
                    app:callback="@{callback}"
                    app:title="@{type == 1 ? @string/chat_like_you_title(viewModel.likeMeCount) : @string/chat_i_like}" />
            </LinearLayout>

            <com.scwang.smart.refresh.layout.SmartRefreshLayout
                android:id="@+id/refresh_layout"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ll_title_bar">

                <androidx.coordinatorlayout.widget.CoordinatorLayout
                    android:id="@+id/coordinatorLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">


                    <com.google.android.material.appbar.AppBarLayout
                        android:id="@+id/appBarLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        app:elevation="0dp">

                        <com.google.android.material.appbar.CollapsingToolbarLayout
                            android:id="@+id/collapsingToolbarLayout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:contentScrim="@android:color/transparent"
                            app:layout_scrollFlags="scroll|exitUntilCollapsed">

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:id="@+id/cl_collapsing_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <androidx.constraintlayout.widget.ConstraintLayout
                                    android:id="@+id/cl_head_content"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <androidx.constraintlayout.widget.ConstraintLayout
                                        android:id="@+id/cl_like_top"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:visibility="invisible"
                                        app:layout_constraintTop_toTopOf="parent">

                                        <TextView
                                            android:id="@+id/tv_like"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginLeft="18dp"
                                            android:layout_marginTop="12dp"
                                            android:text="@{type == 1 ? @string/chat_how_many_people_like_you(viewModel.likeMeCount) : @string/chat_i_like}"
                                            android:textColor="@color/common_color_191919"
                                            android:textSize="@dimen/common_text_sp_28"
                                            android:textStyle="bold"
                                            app:layout_constraintLeft_toLeftOf="parent"
                                            app:layout_constraintTop_toTopOf="parent" />

                                        <TextView
                                            android:id="@+id/tv_desc"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:layout_marginTop="4dp"
                                            android:layout_marginBottom="16dp"
                                            android:textColor="@color/common_color_7F7F7F"
                                            android:textSize="@dimen/common_text_sp_14"
                                            app:layout_constraintBottom_toBottomOf="parent"
                                            app:layout_constraintLeft_toLeftOf="@id/tv_like"
                                            app:layout_constraintTop_toBottomOf="@id/tv_like" />
                                    </androidx.constraintlayout.widget.ConstraintLayout>

                                </androidx.constraintlayout.widget.ConstraintLayout>
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </com.google.android.material.appbar.CollapsingToolbarLayout>
                    </com.google.android.material.appbar.AppBarLayout>
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_list"
                        android:paddingLeft="16dp"
                        android:paddingRight="16dp"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:layout_behavior="@string/appbar_scrolling_view_behavior"
                        app:layout_constraintLeft_toLeftOf="parent" />
                </androidx.coordinatorlayout.widget.CoordinatorLayout>
            </com.scwang.smart.refresh.layout.SmartRefreshLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <variable
            name="type"
            type="int" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatLikeRelatedViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.ChatLikeRelatedListCallback" />
    </data>

</layout>