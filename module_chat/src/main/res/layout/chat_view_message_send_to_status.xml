<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/iv_to_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:singleMsgStatus="@{msg}"
            app:resendChatListener="@{listener}"
            app:resendChatMessage="@{msg}"
            tools:src="@drawable/chat_ic_icon_message_failure"/>

    </FrameLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.ChatMessage" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>