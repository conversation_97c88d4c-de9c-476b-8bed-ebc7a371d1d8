<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.views.layout.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingLeft="12dp">

    <include
        android:layout_marginTop="24dp"
        android:id="@+id/flAvatar"
        layout="@layout/chat_like_list_view_avatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_marginTop="24dp"
        android:id="@+id/llInfo"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="12dp"
        android:orientation="horizontal"
        app:layout_constraintLeft_toRightOf="@+id/flAvatar"
        app:layout_constraintRight_toLeftOf="@+id/clResourceTypeClickLayout"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/clInfo"
            layout="@layout/chat_like_list_view_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clResourceTypeClickLayout"
        android:paddingRight="12dp"
        android:paddingTop="24dp"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@color/common_black">

        <include
            android:id="@+id/clResourceType"
            layout="@layout/chat_like_list_view_ab_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
             />
    </androidx.constraintlayout.widget.ConstraintLayout>



    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="flAvatar,llInfo,clResourceTypeClickLayout" />

    <View
        android:id="@+id/vDivider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="12dp"
        android:background="@color/common_color_F5F5F5"
        app:layout_constraintLeft_toRightOf="@+id/flAvatar"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrier"
        tools:background="@color/common_color_E03641" />

</com.kanzhun.common.views.layout.InterceptConstraintLayout>
