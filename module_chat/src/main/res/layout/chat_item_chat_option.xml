<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="callback"
            type="com.kanzhun.marry.chat.callback.ChatOptionCallback" />

        <variable
            name="bean"
            type="com.kanzhun.marry.chat.model.ChatClickOptionBean" />
    </data>

    <LinearLayout
        android:layout_width="@dimen/chat_option_item_with"
        android:layout_height="match_parent"
        android:gravity="center"
        android:onClick="@{()->callback.onChatOptionClick(bean)}"
        android:orientation="vertical">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            app:imageSrc="@{bean.icon}"
            tools:src="@drawable/chat_option_copy" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="@{bean.name}"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_12"
            tools:text="复制" />
    </LinearLayout>
</layout>
