<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}"
        tools:background="@color/common_black">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/cl_card"
            android:layout_width="246dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/chat_bg_user_mood_from_bg"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:qmui_radius="16dp">

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:id="@+id/flMood"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:background="@color/common_color_F4F4F6"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_radius="12dp">

                <com.kanzhun.common.views.image.OImageView
                    android:id="@+id/ivMood"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="7dp"
                    android:scaleType="centerCrop"
                    app:common_radius="12dp"
                    app:showMood="@{msg}" />

            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

            <TextView
                android:id="@+id/tvMoodName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginEnd="16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{msg.moodTitle}"
                android:textColor="@color/common_color_292929"
                android:textSize="@dimen/common_text_sp_20"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@+id/flMood"
                app:layout_constraintLeft_toRightOf="@+id/flMood"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@+id/flMood"
                tools:ignore="SpUsage"
                tools:text="摸鱼中中摸鱼中中摸鱼中中" />

            <TextView
                android:id="@+id/tvMoodContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="16dp"
                android:ellipsize="end"
                android:maxLines="6"
                android:text="@{msg.moodContent}"
                android:textColor="@color/common_color_292929"
                android:textSize="@dimen/common_text_sp_13"
                android:visibility="@{TextUtils.isEmpty(msg.moodContent) ? View.GONE : View.VISIBLE}"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/flMood"
                app:layout_constraintVertical_bias="0"
                tools:ignore="SpUsage"
                tools:text="摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中摸鱼中中"
                tools:visibility="visible" />

            <com.qmuiteam.qmui.layout.QMUIFrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="2dp"
                android:background="@color/common_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMoodContent"
                app:qmui_hideRadiusSide="top"
                app:qmui_radius="16dp">

                <TextView
                    android:id="@+id/tvMoodTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/chat_bg_color_ffe9e9ed_to_00f4f4f6"
                    android:paddingHorizontal="16dp"
                    android:paddingVertical="14dp"
                    android:text="@{msg.moodUpdateTime}"
                    android:textColor="@color/common_color_992929_60"
                    android:textSize="@dimen/common_text_sp_12"
                    app:layout_goneMarginTop="16dp"
                    tools:ignore="SpUsage"
                    tools:text="4月3日19:00更新了心情" />

            </com.qmuiteam.qmui.layout.QMUIFrameLayout>

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <ImageView
            android:id="@+id/ivMoodUpdate"
            android:layout_width="82dp"
            android:layout_height="64dp"
            android:src="@mipmap/chat_ic_mood_update"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <data>

        <import type="android.text.TextUtils" />

        <import type="android.view.View" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForMood" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>

</layout>