<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#B3000000"
        android:clickable="true"
        android:focusable="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="148dp"
            android:background="@drawable/common_bg_corner_20_top_2_color_white">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_content"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/btn_know"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/tv_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:text="@string/chat_topic_game_desc_title"
                    android:textColor="@color/common_black"
                    android:textSize="@dimen/common_text_sp_18"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/iv_close"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="20dp"
                    android:src="@mipmap/common_ic_close"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_title"
                    tools:ignore="ContentDescription" />


                <androidx.core.widget.NestedScrollView
                    android:id="@+id/scroll_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="28dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="28dp"
                    android:layout_marginBottom="10dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_title"
                    app:layout_constraintVertical_bias="0">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="@string/chat_topic_game_desc_send"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_14"
                            android:textStyle="bold" />


                        <TextView
                            android:id="@+id/tv_send"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="8dp"
                            android:layout_toRightOf="@+id/iv_send"
                            android:textColor="@color/common_color_333333"
                            android:textSize="@dimen/common_text_sp_16"
                            tools:text="11" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="@string/chat_topic_game_desc_receive"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_14"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tv_reply"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginTop="8dp"
                            android:layout_toRightOf="@+id/iv_reply"
                            android:textColor="@color/common_color_333333"
                            android:textSize="@dimen/common_text_sp_16"
                            tools:text="11" />


                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:text="@string/chat_topic_game_desc_advice"
                            android:textColor="@color/common_color_191919"
                            android:textSize="@dimen/common_text_sp_14"
                            android:textStyle="bold" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layoutManager="com.kanzhun.common.views.LinearLayoutManagerWrapper" />

                    </LinearLayout>
                </androidx.core.widget.NestedScrollView>

            </androidx.constraintlayout.widget.ConstraintLayout>


            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/btn_know"
                android:layout_width="match_parent"
                android:layout_height="46dp"
                android:layout_alignParentBottom="true"
                android:layout_marginStart="20dp"
                android:layout_marginTop="24dp"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="32dp"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:text="@string/common_i_know_me"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                app:qmui_backgroundColor="@color/common_color_191919"
                app:qmui_radius="25dp" />

        </RelativeLayout>
    </FrameLayout>
</layout>