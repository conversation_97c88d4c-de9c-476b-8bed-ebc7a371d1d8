<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:chatListener="@{listener}"
        app:chatMessage="@{msg}">

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:layout_width="100dp"
            android:layout_height="100dp"
            app:qmui_radius="10dp">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/iv_pic"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitXY"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:showEmotionScale="@{msg}" />


        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>
    </LinearLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForEmotion" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />
    </data>
</layout>