<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/chat_message_item_padding_top_bottom"
        android:layout_marginBottom="@dimen/chat_message_item_padding_top_bottom"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:paddingLeft="40dp"
        android:paddingRight="40dp">

        <com.kanzhun.marry.chat.views.MLinkEmotionTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:agreementText="@{msg}"
            app:chatListener="@{listener}"
            android:textColor="@color/common_color_9999A3"
            android:textSize="@dimen/common_text_sp_14"
            tools:text="sfdkjsfdkjsfdkjsfdkj" />

        <TextView
            android:id="@+id/tv_re_write"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:onClick="@{()->listener.onReWrite(msg)}"
            android:paddingTop="8dp"
            android:paddingBottom="8dp"
            android:text="@string/chat_re_write"
            android:textColor="@color/common_color_4C9CF8"
            android:textSize="13dp"
            app:reWrite="@{msg}" />

    </LinearLayout>

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForHint" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />

    </data>
</layout>