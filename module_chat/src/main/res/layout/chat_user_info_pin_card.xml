<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForUserCard" />
    </data>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/chat_bg_user_info_card2"
        android:orientation="horizontal"
        android:padding="16dp"
        app:chatUserStickyInfo="@{msg}"
        tools:showIn="@layout/chat_item_message_user_info_card">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@mipmap/chat_ic_same_tag"
            tools:ignore="ContentDescription" />

        <com.kanzhun.common.views.span.ExpandColorTextView
            android:id="@+id/tvSameTag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/common_color_B8B8B8"
            android:textSize="14dp"
            app:zpui_stv_collapse_color="@color/common_color_005EFF"
            app:zpui_stv_collapse_text="收起"
            app:zpui_stv_custom_color="@color/common_color_191919"
            app:zpui_stv_expand_color="@color/common_color_005EFF"
            app:zpui_stv_expand_text="@string/common_expand"
            app:zpui_stv_max_line="3"
            app:zpui_stv_support_always_showright="true"
            app:zpui_stv_support_animation="false"
            app:zpui_stv_support_collapse="true"
            app:zpui_stv_support_custom="true"
            app:zpui_stv_support_custom_expand="false"
            app:zpui_stv_support_expand="true"
            app:zpui_stv_support_real_expand_or_collapse="true"
            tools:ignore="SpUsage"
            tools:text="@string/common_long_placeholder" />

    </LinearLayout>

</layout>