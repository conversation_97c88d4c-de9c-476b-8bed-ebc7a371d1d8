<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.kanzhun.common.views.layout.InterceptConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="20dp"
        android:paddingBottom="20dp">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_center"
            android:layout_width="101dp"
            android:layout_height="62dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:relationshipBackground="@{msg}">
            <com.kanzhun.common.views.image.OAvatarImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="7dp"
                android:layout_marginLeft="7dp"
                app:common_circle="true"
                app:myself="@{false}"
                app:chatListener="@{listener}"
                app:avatarByUserId="@{msg}"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kanzhun.common.views.image.OAvatarImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginTop="7dp"
                android:layout_marginRight="7dp"
                app:common_circle="true"
                app:myself="@{true}"
                app:chatListener="@{listener}"
                app:avatarByUserId="@{msg}"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="94dp"
            android:layout_height="1dp"
            app:layout_constraintTop_toTopOf="@+id/cl_center"
            app:layout_constraintBottom_toBottomOf="@+id/cl_center"
            app:layout_constraintRight_toLeftOf="@+id/cl_center"
            android:layout_marginRight="15dp"
            android:alpha="0.2"
            android:background="@drawable/chat_bg_color_ffffff_to_7fbfff"/>

        <View
            android:layout_width="94dp"
            android:layout_height="1dp"
            app:layout_constraintTop_toTopOf="@+id/cl_center"
            app:layout_constraintBottom_toBottomOf="@+id/cl_center"
            app:layout_constraintLeft_toRightOf="@+id/cl_center"
            android:layout_marginLeft="15dp"
            android:alpha="0.2"
            android:background="@drawable/chat_bg_color_7fbfff_to_ffffff"/>

        <com.kanzhun.common.views.OTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/cl_center"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginTop="6dp"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_707070"
            app:relationshipTitle="@{msg}"/>
    </com.kanzhun.common.views.layout.InterceptConstraintLayout>

    <data>
        <import type="com.kanzhun.marry.chat.R" />
        <import type="androidx.core.content.ContextCompat" />

        <variable
            name="msg"
            type="com.kanzhun.foundation.model.message.MessageForNotifyCard" />

        <variable
            name="listener"
            type="com.kanzhun.foundation.listener.ChatItemListener" />

        <variable
            name="viewModel"
            type="com.kanzhun.marry.chat.viewmodel.ChatBaseViewModel" />
    </data>

</layout>