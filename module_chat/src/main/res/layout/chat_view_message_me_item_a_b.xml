<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.kanzhun.foundation.model.profile.ProfileMetaModel" />

        <import type="com.kanzhun.foundation.views.MeABOImageView" />

        <variable
            name="bean"
            type="com.kanzhun.foundation.api.model.ABFace" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:ignore="ContentDescription,SpUsage"
        tools:layout_height="216dp">

        <com.kanzhun.foundation.views.MeABOImageView
            android:id="@+id/iv_a"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:common_bottom_left_radius="12dp"
            app:common_top_left_radius="12dp"
            app:imageUrl="@{bean.photoA}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.53"
            app:pathClipCorner="@{MeABOImageView.RIGHT_BOTTOM}"
            tools:background="@color/common_black_30" />

        <com.kanzhun.foundation.views.MeABOImageView
            android:id="@+id/iv_b"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            app:common_bottom_right_radius="12dp"
            app:common_top_right_radius="12dp"
            app:imageUrl="@{bean.photoB}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.53"
            app:pathClipCorner="@{MeABOImageView.LEFT_TOP}"
            tools:background="@color/common_black_30" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginBottom="3dp"
            android:src="@mipmap/me_ic_info_edit_a"
            app:layout_constraintBottom_toTopOf="@+id/tv_a_title"
            app:layout_constraintLeft_toLeftOf="parent" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_a_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:maxWidth="136dp"
            android:maxLines="2"
            android:text="@{bean.titleA}"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintWidth_percent="0.416"
            tools:text="生活中的我" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="3dp"
            android:src="@mipmap/me_ic_info_edit_b"
            app:layout_constraintBottom_toTopOf="@+id/tv_b_title"
            app:layout_constraintRight_toRightOf="parent" />

        <com.kanzhun.common.views.OTextView
            android:id="@+id/tv_b_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:ellipsize="end"
            android:gravity="end"
            android:maxLines="2"
            android:text="@{bean.titleB}"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.416"
            tools:text="工作中的我" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>