<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="bean"
            type="com.kanzhun.marry.chat.model.PanelFunc" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:padding="10dp">

        <ImageView
            android:id="@+id/iv_icon"
            android:layout_width="57dp"
            android:layout_height="57dp"
            android:layout_gravity="center_horizontal"
            android:scaleType="fitXY"
            app:imageSrc="@{bean.drawableId}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="ContentDescription" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/compose_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
            app:layout_constraintEnd_toEndOf="@+id/iv_icon"
            app:layout_constraintStart_toStartOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="@+id/iv_icon" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/progress_bar"
            android:layout_width="64dp"
            android:layout_height="64dp"
            app:layout_constraintBottom_toBottomOf="@+id/iv_icon"
            app:layout_constraintEnd_toEndOf="@+id/iv_icon"
            app:layout_constraintStart_toStartOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="@+id/iv_icon"
            app:visibleGone="@{bean.showProgress}"
            tools:composableName="com.kanzhun.marry.chat.component.MoreFunctionComponentKt.PreviewRoundedSquareProgress" />

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="11dp"
            android:text="@{bean.name}"
            android:textColor="@color/common_black"
            android:textSize="@dimen/common_text_sp_14"
            app:layout_constraintLeft_toLeftOf="@+id/iv_icon"
            app:layout_constraintRight_toRightOf="@+id/iv_icon"
            app:layout_constraintTop_toBottomOf="@+id/iv_icon"
            tools:ignore="SpUsage"
            tools:text="语音通话" />

        <TextView
            android:id="@+id/tv_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/chat_bg_label_color_black"
            android:gravity="center"
            android:paddingHorizontal="9dp"
            android:paddingVertical="4dp"
            android:textColor="@color/common_white"
            android:textSize="@dimen/common_text_sp_13"
            android:textStyle="bold"
            android:visibility="gone"
            app:labelStatus="@{bean.labelStatus}"
            app:layout_constraintEnd_toEndOf="@+id/iv_icon"
            app:layout_constraintStart_toStartOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="SpUsage"
            tools:text="使用中"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_lock"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginTop="-8dp"
            android:layout_marginEnd="-8dp"
            android:src="@mipmap/chat_ic_lock"
            app:layout_constraintEnd_toEndOf="@+id/iv_icon"
            app:layout_constraintTop_toTopOf="@+id/iv_icon"
            app:visibleGone="@{bean.labelStatus == 1}"
            tools:ignore="ContentDescription"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>