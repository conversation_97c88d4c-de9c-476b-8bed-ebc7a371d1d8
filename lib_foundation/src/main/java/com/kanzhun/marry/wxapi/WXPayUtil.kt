package com.kanzhun.marry.wxapi

import android.content.Context
import android.text.TextUtils
import com.kanzhun.common.util.LText
import com.kanzhun.foundation.constant.ShareKey
import com.kanzhun.marry.pay.PayConstants.APP_PAY_NO_INSTALLED
import com.kanzhun.marry.pay.PayConstants.APP_PAY_PARAM_ERROR
import com.kanzhun.marry.pay.PayConstants.APP_PAY_RESULT_FAILED
import com.kanzhun.utils.base.MD5
import com.tencent.mm.opensdk.modelbiz.WXOpenBusinessWebview
import com.tencent.mm.opensdk.modelmsg.SendAuth
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import java.util.UUID

object WXPayUtil {

    private val key = ShareKey.getWxAppId()

    fun isWXAppInstalled(context: Context): Boolean {
        val api: IWXAPI = WXAPIFactory.createWXAPI(context, key)
        api.registerApp(key)
        return api.isWXAppInstalled()
    }

    /**
     * 启动支付接口
     *
     * @param context  启动上下文
     * @param prepayId 订单ID
     */
    fun startDeltaPay(
        context: Context, appId: String?,
        partnerId: String?, prepayId: String?,
        packageValue: String?, nonceStr: String?,
        timeStamp: String?, sig: String?
    ): BzbReqResult? {
        if (TextUtils.isEmpty(prepayId)) {
            return BzbReqResult.wx()
                .setErrorCode(APP_PAY_PARAM_ERROR)
                .setErrorMsg("prepayId错误")
        }

        val api: IWXAPI = WXAPIFactory.createWXAPI(context, key)
        api.registerApp(key)
        if (!api.isWXAppInstalled()) {
            return BzbReqResult.wx()
                .setErrorCode(APP_PAY_NO_INSTALLED)
                .setErrorMsg("未安装微信")
        }

        val request: PayReq = PayReq()
        request.appId = appId
        request.partnerId = partnerId
        request.prepayId = prepayId
        request.packageValue = packageValue
        request.nonceStr = nonceStr
        request.timeStamp = timeStamp
        request.sign = sig
        val b: Boolean = api.sendReq(request)
        if (!b) {
            return BzbReqResult.wx()
                .setErrorCode(APP_PAY_RESULT_FAILED)
                .setErrorMsg("启动微信失败")
        }
        return null
    }
}
