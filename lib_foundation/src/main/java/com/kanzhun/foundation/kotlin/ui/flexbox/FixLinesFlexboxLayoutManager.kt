package com.kanzhun.foundation.kotlin.ui.flexbox

import android.content.Context
import com.google.android.flexbox.FlexLine
import com.google.android.flexbox.FlexboxLayoutManager


class FixLinesFlexboxLayoutManager(context: Context?, val maxLines: Int) : FlexboxLayoutManager(context) {

    /**
     * 这里限制了最大行数，多出部分被以 subList 方式截掉
     */
    override fun getFlexLinesInternal(): List<FlexLine> {
        val flexLines: List<FlexLine> = super.getFlexLinesInternal()
        val size = flexLines.size
        if (maxLines in 1 until size) {
            return flexLines.subList(0, maxLines)
        }
        return flexLines
    }
}