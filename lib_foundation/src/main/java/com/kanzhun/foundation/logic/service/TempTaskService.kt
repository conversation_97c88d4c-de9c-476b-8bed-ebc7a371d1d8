package com.kanzhun.foundation.logic.service

import com.kanzhun.foundation.facade.NoviceTaskRepository
import com.kanzhun.foundation.facade.TempTaskRepository
import com.kanzhun.foundation.kernel.core.BaseService
import com.kanzhun.foundation.model.NoviceTaskType

class TempTaskService():BaseService() {

    private var mTempTaskRepository : TempTaskRepository? = null

    fun getTempTaskRepository():TempTaskRepository?{
        return mTempTaskRepository
    }

    override fun onAccountInitialized() {
        mTempTaskRepository?.destroy()
        mTempTaskRepository = TempTaskRepository()
    }

    override fun onAccountRelease() {
        mTempTaskRepository?.destroy()
        mTempTaskRepository = null
    }


}