package com.kanzhun.foundation.logic.service;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.kanzhun.foundation.api.model.InteractTabSummary;
import com.kanzhun.foundation.db.dao.ConversationDao;
import com.kanzhun.foundation.facade.ConversationCallback;
import com.kanzhun.foundation.facade.ConversationRepository;
import com.kanzhun.foundation.kernel.core.BaseService;
import com.kanzhun.foundation.model.Conversation;
import com.kanzhun.utils.GsonUtils;
import com.techwolf.lib.tlog.TLog;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ConversationService extends BaseService {
    private ConversationRepository mConversationRepository;
    private MutableLiveData<Boolean> scrollToNextUnReadItem = new MutableLiveData<>();
    private ConversationDao mDao;
    private LiveData<List<Conversation>> allConversations;

    private LiveData<List<Conversation>> unReadConversations;

    @Override
    protected void onAccountInitialized() {
        if (mConversationRepository == null) {
            mConversationRepository = new ConversationRepository();
            allConversations = mConversationRepository.queryAllConversation();
            TLog.print("zl_log", "ConversationService: onAccountInitialized: List<Conversation>=%s", GsonUtils.toJson(allConversations.getValue()));
            unReadConversations = mConversationRepository.queryUnreadConversation();
        }
        mDao = ServiceManager.getInstance().getDatabaseService().getConversationDao();
    }

    @Override
    protected void onAccountRelease() {
        if (mConversationRepository != null) {
            mConversationRepository.destroy();
            mConversationRepository = null;
        }
    }

    public ConversationCallback getMessageCallback() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository;
        }
        return null;
    }


    public LiveData<List<Conversation>> getUnReadConversations() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return unReadConversations;
        }
        return new MediatorLiveData<>();
    }

    /**
     * 获取所有会话
     */
    public LiveData<List<Conversation>> getAllConversations() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return allConversations;
        }
        return new MediatorLiveData<>();
    }

    /**
     * 获取所有可见会话
     */
    public LiveData<List<Conversation>> getAllVisibleConversations() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.getAllVisibleConversations();
        }
        return new MediatorLiveData<>();
    }


    public Conversation getByUid(String uid, int type) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                return dao.queryByUid(uid, type);
            }
        }
        return null;
    }

    public int delete(Conversation conversation) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                return dao.delete(conversation.getChatId(), conversation.getType());
            }
        }
        return -1;
    }

    public void saveDraft(String chatId, int type, String draft) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                dao.updateDraft(chatId, type, draft, System.currentTimeMillis());
            }
        }
    }

    public int delete(String chatId, int type) {
        ConversationDao dao = mDao;
        if (dao != null) {
            return dao.delete(chatId, type);
        }
        return 0;
    }

    public LiveData<Integer> queryAllUnReadCountWithoutSelf(String chatId) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                return dao.queryAllUnReadCountWithoutSelf(chatId);
            }
        }
        return new MediatorLiveData<>();
    }

    public int queryChatUnreadCount(String chatId, int chatType) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                return dao.queryChatUnReadCount(chatId, chatType);
            }
        }
        return 0;
    }

    /**
     * 不包含归档消息的静音回话的未读数量
     */
    public LiveData<Integer> getAllSilenceUnReadCount() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                return dao.queryAllSilenceUnReadCount();
            }
        }
        return new MediatorLiveData<>();
    }

    public LiveData<Conversation> findByUid(String uid, int type) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.findByUid(uid, type);
        }
        return new MediatorLiveData<>();
    }

    public MutableLiveData<Boolean> getScrollToNextUnReadItem() {
        return scrollToNextUnReadItem;
    }

    public void setScrollToNextUnReadItem(Boolean canScroll) {
        this.scrollToNextUnReadItem.postValue(canScroll);
    }

    public void update(List<Conversation> data) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.update(data);
        }
    }

    public void updateConversation(Conversation data) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.update(data);
        }
    }

    public Conversation getConversation(String chatId, int type) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.getConversation(chatId, type);
        }
        return null;
    }

    /**
     * 获取所有消息数量
     */
    public LiveData<Integer> getAllConversationCount() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.getAllConversationCount();
        }
        return new MediatorLiveData<>();
    }

    public boolean getContactSyncCompleted() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.getContactSyncCompleted();
        }
        return false;
    }

    public void setContactSyncCompleted(boolean contactSyncCompleted) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.setContactSyncCompleted(contactSyncCompleted);
        }
    }

    public void resetUnread(String chatId, int type) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.resetUnread(chatId, type);
        }
    }

    public MutableLiveData<Boolean> getContactSyncOver() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.getContactSyncOver();
        }
        return new MutableLiveData<>();
    }

    public MutableLiveData<InteractTabSummary> getInteractTabSummaryInfo() {
        if (mConversationRepository != null) {
            return mConversationRepository.getInteractTabSummaryInfo();
        }
        return new MutableLiveData<>();
    }

    public MutableLiveData<HashMap<String, Integer>> getCardTipsLiveData() {
        if (mConversationRepository != null) {
            return mConversationRepository.getCardTipsLiveData();
        }
        return new MutableLiveData<>();
    }

    public void unregisterShowCardTips(String chatId) {
        if (mConversationRepository != null) {
            mConversationRepository.unregisterShowCardTips(chatId);
        }
    }

    public LiveData<Integer> getUnReadCountLiveData() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            return conversationRepository.queryUnReadCountLiveData();
        }
        return new MediatorLiveData<>();
    }

    public void updateNoNameConversation() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.updateNoNameConversation();
        }
    }

    public void saveLatestMomentId(String chatId, int type, String momentId) {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            ConversationDao dao = mDao;
            if (dao != null) {
                dao.updateLatestMomentId(chatId, type, momentId);
            }
        }
    }

    public void updateInteractTab() {
        ConversationRepository conversationRepository = mConversationRepository;
        if (conversationRepository != null) {
            conversationRepository.updateInteractTab();
        }
    }
}
