package com.kanzhun.foundation.dialog;

import android.view.View;

import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.kanzhun.common.dialog.BottomSheetBehaviorBaseFragment;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.databinding.ChatLayoutLoveModeDescriptionBinding;

/**
 * 情侣模式说明页
 * <p>
 * Created by <PERSON><PERSON> on 2022/5/28
 */
public class LoveModeDescriptionDialog extends BottomSheetBehaviorBaseFragment<ChatLayoutLoveModeDescriptionBinding, FoundationViewModel> {
    public static final String TAG = "LoveModeDescriptionDialog";

    @Override
    public int getContentLayoutId() {
        return R.layout.chat_layout_love_mode_description;
    }

    @Override
    protected void initFragment() {
        getDataBinding().btnConfirm.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dismiss();
            }
        });
    }

    @Override
    public int getBottomSheetLayoutParamsHeight() {
        return (int) (QMUIDisplayHelper.getScreenHeight(activity) * 0.7f);
    }

    @Override
    protected void setBehaviorDefaultState() {
        if (getBehavior() != null) {
            // 初始为展开状态
            getBehavior().setState(BottomSheetBehavior.STATE_EXPANDED);
            // 禁止滑动
            getBehavior().setDraggable(false);
        }
    }

    @Override
    public int getCallbackVariable() {
        return 0;
    }

    @Override
    public Object getCallback() {
        return null;
    }

    @Override
    public int getBindingVariable() {
        return 0;
    }

}
