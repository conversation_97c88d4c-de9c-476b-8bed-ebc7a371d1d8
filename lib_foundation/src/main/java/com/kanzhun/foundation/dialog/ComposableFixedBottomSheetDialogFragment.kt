package com.kanzhun.foundation.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.Composable
import com.kanzhun.common.base.compose.ext.onSetWindowContent
import com.kanzhun.foundation.databinding.FoundationCommonDialogBinding

abstract class ComposableFixedBottomSheetDialogFragment() : FixedBottomSheetDialogFragment() {
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return FoundationCommonDialogBinding.inflate(inflater, container, false).apply {
            composeView.onSetWindowContent {
                OnSetContent()
            }
        }.root
    }

    @Composable
    abstract fun OnSetContent()
}