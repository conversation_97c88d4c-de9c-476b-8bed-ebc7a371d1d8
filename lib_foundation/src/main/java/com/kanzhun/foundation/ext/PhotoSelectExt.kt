package com.kanzhun.foundation.ext

import android.graphics.BitmapFactory
import android.net.Uri
import com.kanzhun.common.base.BaseApplication
import java.io.FileNotFoundException


/**
 * 相册返回的图片列表，过滤gif图
 */
fun MutableList<Uri>?.toFilterGif() {
    val iterator = this?.iterator()
    if (iterator != null) {
        while (iterator.hasNext()) {
            val next = iterator.next()
            if (next.isGif()) {
                iterator.remove()
            }
        }
    }

}

fun Uri.isGif():Boolean{
    try {
        val inputStream = BaseApplication.getApplication().contentResolver.openInputStream(this)
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeStream(inputStream, null, options)

        return if (options.outMimeType != null) {
            when (options.outMimeType) {
                "image/gif" -> true
                else -> false
            }
        } else {
            false
        }
    } catch (e: FileNotFoundException) {
        e.printStackTrace()
        return false
    }
}


//fun MutableList<Uri>?.toFilterGif() {
//    val iterator = this?.iterator()
//    if (iterator != null) {
//        while (iterator.hasNext()) {
//            val next = iterator.next()
//            val map = MimeTypeMap.getSingleton()
//            val resolver: ContentResolver = BaseApplication.getApplication().contentResolver
//            val type = map.getExtensionFromMimeType(resolver.getType(next))
//            TLog.print("zl_log", "PhotoSelectExt: type1=%s, type2=%s", type, resolver.getType(next))
//            if (type != null && type.equals("gif", ignoreCase = true)) {
//                iterator.remove()
//            }
//        }
//    }
//
//
//}
