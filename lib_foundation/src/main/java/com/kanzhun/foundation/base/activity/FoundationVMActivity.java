package com.kanzhun.foundation.base.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.Nullable;
import androidx.databinding.ViewDataBinding;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.sankuai.waimai.router.Router;
import com.kanzhun.common.activity.BaseVMActivity;
import com.kanzhun.common.base.BaseViewModel;
import com.kanzhun.foundation.api.bean.ProfileStatusBean;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.base.activity.preformance.CommonGlobalPerformanceManager;
import com.kanzhun.foundation.bean.BaseLoadingEmptyBean;
import com.kanzhun.foundation.databinding.CommonIncludeLoadingEmptyViewBinding;
import com.kanzhun.foundation.router.MePageRouter;
import com.kanzhun.foundation.router.service.IMeRouterService;
import com.kanzhun.foundation.views.OLoadingEmptyView;

import java.util.List;

/**
 * 可添加业务逻辑
 */
public abstract class FoundationVMActivity<D extends ViewDataBinding, M extends BaseViewModel> extends BaseVMActivity<D, M> implements IBaseActivity {

    private CommonGlobalPerformanceManager parentPerformManager;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initAnnotation();
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).getShowProgressBar().observe(this, new Observer<String>() {
                @Override
                public void onChanged(@Nullable String s) {
                    if (s == null) {
                        dismissProgressDialog();
                    } else {
                        showProgressDialog(s, ((FoundationViewModel) getViewModel()).isProgressBarCanCancel());
                    }
                }
            });
            ((FoundationViewModel) getViewModel()).getProfileStatusLiveData().observe(this, new Observer<List<ProfileStatusBean>>() {
                @Override
                public void onChanged(List<ProfileStatusBean> profileStatusBeans) {
                    IMeRouterService s = Router.getService(IMeRouterService.class, MePageRouter.ME_SERVICE);
                    if (s != null) {
                        s.jumpUserInfoPerfectDialog(FoundationVMActivity.this, profileStatusBeans);
                    }
                }
            });

            initEmptyViewLiveData(((FoundationViewModel) getViewModel()).getShowLoadingEmptyLiveData());

        }
//        ServiceManager.getInstance().getMessageService().getVideoInvitation().observe(this, new Observer<MessageForAction>() {
//            @Override
//            public void onChanged(@Nullable MessageForAction messageForAction) {
//                try {
//                    if (messageForAction != null) {
//                        ServiceManager.getInstance().getMessageService().setVideoInvitation(null);
//                        Account account = AccountHelper.getInstance().getAccount();
//                        if (account != null) {
//                            if (!TextUtils.equals(account.getUserId(), messageForAction.getSender())) {
//                                MessageForAction.VideoChatActionInfo videoChatActionInfo = (MessageForAction.VideoChatActionInfo) messageForAction.getBody();
//                                Observable<BaseResponse<VideoMeetingInfo>> observable = RetrofitManager.getInstance().createApi(FoundationApi.class).getVideoMeetingInfo(videoChatActionInfo.roomId);
//                                HttpExecutor.execute(observable, new BaseRequestCallback<VideoMeetingInfo>() {
//                                    @Override
//                                    public void onSuccess(VideoMeetingInfo data) {
//                                        TLog.error("VideoChatEngineListener", "431 roomId=" + data.roomId);
//                                        if (data != null && data.status < 3) {
//                                            HashMap<String, String> cancelMap = ServiceManager.getInstance().getMessageService().getPostInviteCancelAction();
//                                            Iterator<Map.Entry<String, String>> iterator = cancelMap.entrySet().iterator();
//                                            while (iterator.hasNext()) {
//                                                Map.Entry<String, String> entry = iterator.next();
//                                                String roomId = entry.getValue();
//                                                if (TextUtils.equals(roomId, data.roomId)) {
//                                                    return;
//                                                }
//                                            }
//                                            IMeetingService iMeetingService = Router.getService(IMeetingService.class, MediaConstants.ENGINE_LISTENER_KEY);
//                                            iMeetingService.postLocalOverTimeMessage();
//                                            if (iMeetingService.alive()) {
//                                                TLog.error("VideoChatEngineListener", "onSuccess alive" + data.roomId);
//                                                return;
//                                            }
//                                            iMeetingService.setAlive(true);
//                                            if (isSenderOneVOneChat(messageForAction.getSender()) || !PermissionUtil.canDrawOverlays(FoundationVMActivity.this) || !PermissionManager.checkAllSelfPermissions(FoundationVMActivity.this, new String[]{Manifest.permission.MODIFY_AUDIO_SETTINGS, Manifest.permission.RECORD_AUDIO})) {
//                                                Intent intent = Navigation.getVideoChatActivity(FoundationVMActivity.this);
//                                                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_ROOM_ID, data.roomId);
//                                                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_TYPE, videoChatActionInfo.linkType);
//                                                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_INVITATION, messageForAction.getSender());
//                                                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_NEBULA_ID, data.nebulaId);
//                                                AppUtil.startActivity(FoundationVMActivity.this, intent);
//                                            } else {
//                                                FloatParamBean paramBean = new FloatParamBean(data.roomId, videoChatActionInfo.linkType, messageForAction.getSender(), data.nebulaId, 0);
//                                                VideoAudioFloatPageManager.getInstance().addPageVideoAudio(Navigation.getFloatPageClass(), paramBean);
//                                            }
//                                        }
//                                    }
//
//                                    @Override
//                                    public void dealFail(ErrorReason reason) {
//
//                                    }
//                                });
//                            }
//                        }
//                    }
//                } catch (Exception e) {
//
//                }
//            }
//        });
        parentPerformManager = new CommonGlobalPerformanceManager(this);
        parentPerformManager.init();
    }

    private void initAnnotation() {
        this.getClass().getDeclaredAnnotations();
    }

    /**
     * 语音通话，是否在根邀请语音人的对话中
     *
     * @param senderId
     * @return
     */
    @Override
    public boolean isSenderOneVOneChat(@Nullable String senderId) {
        return false;
    }


    //region loading empty 逻辑
    protected OLoadingEmptyView emptyView;

    /**
     * 需要重写 需要在xml include 布局
     * 类似
     * <include android:id="@+id/ic_empty"
     * layout="@layout/common_include_loading_empty_view"
     * app:viewModel="@{viewModel}" />
     */
    public OLoadingEmptyView getEmptyView() {
        return emptyView;
    }

    /**
     * @return true 不需要在xml include 布局 不需要重写getEmptyView()
     */
    public boolean isShowEmptyViewForRoot() {
        return true;
    }

    protected void initEmptyViewLiveData(MutableLiveData<BaseLoadingEmptyBean> showLoadingEmptyLiveData) {
        showLoadingEmptyLiveData.observe(this, new Observer<BaseLoadingEmptyBean>() {
            @Override
            public void onChanged(BaseLoadingEmptyBean baseLoadingEmptyBean) {
                if (baseLoadingEmptyBean != null) {
                    switch (baseLoadingEmptyBean.status) {
                        case OLoadingEmptyView.LOADING:
                            showEmptyLoading();
                            break;
                        case OLoadingEmptyView.ERROR:
                            showEmptyError();
                            break;
                        case OLoadingEmptyView.NO_DATA:
                            if (TextUtils.isEmpty(baseLoadingEmptyBean.desc)) {
                                showEmptyNoData();
                            } else {
                                showEmptyNoData(baseLoadingEmptyBean.desc);
                            }
                            break;
                        case OLoadingEmptyView.SUCCESS:
                            showEmptySuccess();
                            break;
                        default:
                            break;
                    }
                }

            }
        });
    }

    private void initEmptyView() {
        if (emptyView == null) {
            if (!isShowEmptyViewForRoot()) {
                emptyView = getEmptyView();
            } else {
                ViewGroup parent = findViewById(android.R.id.content);
                CommonIncludeLoadingEmptyViewBinding emptyViewBinding = CommonIncludeLoadingEmptyViewBinding.inflate(LayoutInflater.from(this));
                View child = emptyViewBinding.getRoot();
                parent.addView(child);
                emptyView = emptyViewBinding.emptyView;
                if (getViewModel() instanceof FoundationViewModel) {
                    emptyViewBinding.setViewModel((FoundationViewModel) getViewModel());
                }
            }

            if (emptyView != null) {
                emptyView.setRetryListener(new OLoadingEmptyView.RetryListener() {
                    @Override
                    public void onRetry() {
                        showEmptyLoading();
                        onLoadRetry();
                    }
                });
            }
        }
    }


    /**
     * 点击重新加载
     */
    public void onLoadRetry() {

    }

    public void showEmptyLoading() {
        initEmptyView();
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).setShowLoadingEmptyObservable(OLoadingEmptyView.LOADING);
        }
    }

    public void showEmptyError() {
        initEmptyView();
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).setShowLoadingEmptyObservable(OLoadingEmptyView.ERROR);
        }
    }

    public void showEmptyNoData() {
        initEmptyView();
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).setShowLoadingEmptyObservable(OLoadingEmptyView.NO_DATA);
        }
    }

    public void showEmptyNoData(String noData) {
        initEmptyView();
        if (emptyView != null) {
            emptyView.setNoData(noData);
        }
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).setShowLoadingEmptyObservable(OLoadingEmptyView.NO_DATA);
        }
    }

    public void showEmptySuccess() {
        initEmptyView();
        if (getViewModel() instanceof FoundationViewModel) {
            ((FoundationViewModel) getViewModel()).setShowLoadingEmptyObservable(OLoadingEmptyView.SUCCESS);
        }
    }

    //endregion

}
