package com.kanzhun.foundation.api.model

import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.api.callback.SendLikeBean
import com.kanzhun.foundation.views.PreviewLikeView
import java.io.Serializable

data class SendChatBean(val userId:String?,
                        val likeType: PreviewLikeView.LIKE_TYPE,
                        val source: PageSource,
                        val chatWordList:List<String>,
                        val resId:String?,
                        val sendLikeBean: SendLikeBean,
                        var isSecondPageView:Boolean = false,
):Serializable


