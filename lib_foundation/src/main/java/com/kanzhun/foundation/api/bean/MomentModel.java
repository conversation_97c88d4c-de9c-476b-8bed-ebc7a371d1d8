package com.kanzhun.foundation.api.bean;

import androidx.annotation.Nullable;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/16.
 */
public class MomentModel {
    public static final int UN_DID_THUMB = 0;//未点赞
    public static final int DID_THUMB = 1;//已赞
    public String momentId;// 动态id
    public String userId; // 用户id
    public String avatar;// 头像地址
    public String tinyAvatar;// 缩略图头像地址
    public String liveVideo;// 头像 livePhoto的视频 地址 可空
    public String nickName;// 昵称
    public long createTime;// 创建时间
    public String content;// 内容
    public int replyCount;// 评论数量
    public int thumbCount;// 点赞数量
    public int thumbStatus;// 是否已赞，1-是，0-否
    public String ipLocation;// 发布时的位置
    public int visibleType;// 可见范围，1-全部可见，2-好友可见，3-仅自己可见   其他人查看时，该字段不返回
    public List<PictureListItemBean> pictures;// 图片列表
    public String impId;//算法曝光id
    public int gender;//1男2女

    @Nullable
    public String title;
    @Nullable
    public String sourceId;

}
