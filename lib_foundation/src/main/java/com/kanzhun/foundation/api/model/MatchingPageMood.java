package com.kanzhun.foundation.api.model;

import java.io.Serializable;

public class MatchingPageMood implements Serializable {
    private static final long serialVersionUID = -9005057584742724107L;
    //        "code": "xxx", // 枚举待定
//                "name": "xxx", // 心情内容
//                "icon": "https://xxxxx", // 心情icon

    public String code;
    public String name;
    public String icon;
    public String gifIcon;

    public boolean selected;

    @Override
    public String toString() {
        return "MatchingPageMood{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", icon='" + icon + '\'' +
                ", gifIcon='" + gifIcon + '\'' +
                ", selected=" + selected +
                '}';
    }
}
