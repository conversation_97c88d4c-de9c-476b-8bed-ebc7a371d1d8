/*
 * Copyright (C) 2008 ZXing authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.kanzhun.foundation.zxing.view;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.core.content.ContextCompat;

import com.kanzhun.foundation.R;
import com.kanzhun.foundation.zxing.camera.CameraManager;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;


public final class ViewfinderView extends View {

    private static final long ANIMATION_DELAY = 1L;

    private final Paint paint;
    private final int maskColor;
    private final int frameColor;
    private final int laserColor;
    private final int fourColor;

    private final int fourBugleSize;
    private final int fourBugleMargin;
    private final int fourBugleLength;

    private int scanningLocal = 0;
    private boolean down = true;
    private static final int SPEED = 5;
    private Bitmap bitmap;

    // This constructor is used when the class is built from an XML resource.
    public ViewfinderView(Context context, AttributeSet attrs) {
        super(context, attrs);

        // Initialize these once for performance rather than calling them every time in onDraw().
        paint = new Paint();

        Resources resources = getResources();
        maskColor = ContextCompat.getColor(context,R.color.common_black_70);
        frameColor = ContextCompat.getColor(context,R.color.common_white);
        laserColor = ContextCompat.getColor(context,R.color.common_color_3FD5F3);
        fourColor = ContextCompat.getColor(context,R.color.common_color_EB5D48);

        int width = QMUIDisplayHelper.getScreenWidth(getContext());
        fourBugleSize = QMUIDisplayHelper.dp2px(context, 2);
        fourBugleMargin = fourBugleSize;
        fourBugleLength = width / 15;
        bitmap = BitmapFactory.decodeResource(getResources(),R.mipmap.img_qr_code_sacan);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if(bitmap != null && !bitmap.isRecycled()){
            bitmap.recycle();
            bitmap = null;
        }
    }

    @Override
    public void onDraw(Canvas canvas) {
        RectF frame = CameraManager.get().getFramingRect();
        if (frame == null) {
            return;
        }
        int width = canvas.getWidth();
        int height = canvas.getHeight();

        // 绘制背景效果
//        paint.setColor(maskColor);
//        canvas.drawRect(0, 0, width, frame.top, paint);
//        canvas.drawRect(0, frame.top, frame.left, frame.bottom + 1, paint);
//        canvas.drawRect(frame.right + 1, frame.top, width, frame.bottom + 1, paint);
//        canvas.drawRect(0, frame.bottom + 1, width, height, paint);

        // 绘制边框
//        paint.setColor(frameColor);
//        canvas.drawRect(frame.left, frame.top, frame.right + 1, frame.top + 2, paint);
//        canvas.drawRect(frame.left, frame.top + 2, frame.left + 2, frame.bottom - 1, paint);
//        canvas.drawRect(frame.right - 1, frame.top, frame.right + 1, frame.bottom - 1, paint);
//        canvas.drawRect(frame.left, frame.bottom - 1, frame.right + 1, frame.bottom + 1, paint);

        // 绘制左上角边框
//        paint.setColor(fourColor);
//        float apexLeft = frame.left - fourBugleSize - fourBugleMargin;
//        float apexTop = frame.top - fourBugleSize - fourBugleMargin;
//        float apexRight = frame.right + fourBugleSize + fourBugleMargin;
//        float apexBottom = frame.bottom + fourBugleSize + fourBugleMargin;
//        canvas.drawRect(apexLeft, apexTop,
//                apexLeft + fourBugleLength, apexTop + fourBugleSize, paint);
//        canvas.drawRect(apexLeft, apexTop,
//                apexLeft + fourBugleSize, apexTop + fourBugleLength, paint);
        // 绘制右上角边框
//        canvas.drawRect(apexRight - fourBugleLength, apexTop,
//                apexRight, apexTop + fourBugleSize, paint);
//        canvas.drawRect(apexRight - fourBugleSize, apexTop,
//                apexRight, apexTop + fourBugleLength, paint);
        // 绘制左下角边框
//        canvas.drawRect(apexLeft, apexBottom - fourBugleLength, apexLeft + fourBugleSize, apexBottom, paint);
//        canvas.drawRect(apexLeft, apexBottom - fourBugleSize, apexLeft + fourBugleLength, apexBottom, paint);
        // 绘制右下角边框
//        canvas.drawRect(apexRight - fourBugleLength, apexBottom - fourBugleMargin, apexRight, apexBottom, paint);
//        canvas.drawRect(apexRight - fourBugleMargin, apexBottom - fourBugleLength, apexRight, apexBottom, paint);

        // 绘制扫描线
        paint.setColor(laserColor);
        if (down) {
            scanningLocal += SPEED;
        } else {
            scanningLocal -= SPEED;
        }
        float lineLocal = scanningLocal + frame.top;
        if (lineLocal >= frame.bottom) {
            scanningLocal = 0;
        }
        if(bitmap != null && !bitmap.isRecycled()){
            canvas.drawBitmap(bitmap,0,lineLocal - bitmap.getHeight(),paint);
        }
        postInvalidateDelayed(ANIMATION_DELAY);
    }

}
