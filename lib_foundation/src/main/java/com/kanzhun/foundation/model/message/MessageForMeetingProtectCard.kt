package com.kanzhun.foundation.model.message

import com.kanzhun.utils.GsonUtils
import com.xxjz.orange.protocol.codec.ChatProtocol
import java.io.Serializable
import kotlin.jvm.java

/**
 */
class MessageForMeetingProtectCard : ChatMessage() {
    var meetingProtectCard: MeetingProtectCard? = null

    init {
        mediaType = MessageConstants.MSG_MEDIA_TYPE_MEET_PROTECT
    }

    override fun parserMessage(message: ChatProtocol.OgMessage?) {
        message?.run {
            val protectMeet = message.body?.protectMeet ?: return
            meetingProtectCard = MeetingProtectCard(
                cardType = protectMeet.cardType,
                recordId = protectMeet.recordId,
                maleUserId = protectMeet.maleUserId,
                femaleUserId = protectMeet.femaleUserId,
                extentJson = protectMeet.extentJson
            )
        }
    }

    override fun parseFromDB() {
        super.parseFromDB()
        val tmp = GsonUtils.getGson().fromJson(content, MeetingProtectCard::class.java)
        if (tmp != null) {
            meetingProtectCard = tmp
        }
    }

    override fun prepare2DB() {
        super.prepare2DB()
        content = GsonUtils.getGson().toJson(meetingProtectCard)
    }

    override fun getSummary(): String {
        return "[见面邀约]"
    }
}

/**
extentJson信息
消息：邀请见面卡
{
"maleUserStatus": 0, //男状态：1-未确认，2-拒绝；3-接受
"femaleUserStatus": 0, //女用户状态：1-未确认，2-拒绝；3-接受
"maleUserTag": "a,b", // 男用户标签 多个逗号分割
"femaleUserTag": "a,b" // 女用户标签 多个逗号分割
"expireTime":1222222222222, //过期时间，毫秒值
}

消息：见面信息设置卡
{
"status": 0, //设置状态 10- 未设置，11-已设置  12-已同意  13-已取消
"settingActiveId": "xxx", //主动设置uid
"inviteActiveId": "xxx", //主动邀请uid
"cancelUserId": "xxx", //主动取消的用户Id
"time": 11111111, //时间，毫秒值
"address": "北京" //见面地点
}

消息：到达确认卡
{
“maleUserStatus”: 0, //男状态：0-未确认，20-已到达；21-已取消
“femaleUserStatus”: 0, //女用户状态：0-未确认，20-已到达；21-已取消
“time”: 1222222222222, //见面时间
“address”: “北京” //见面地点
"maleProtectOpenStatus":0,//0-未开启守护，1开启守护
“femaleProtectOpenStatus”:0,//0-未开启守护，1开启守护
}

消息：见面评价卡
{
"maleUserStatus": 0, //男状态：30-未评价，31-已评价
"femaleUserStatus": 0, //女用户状态：30-未评价，31-已评价
}
 */
data class MeetingProtectCard(
    val cardType: Int,//类型 1:见面邀约卡；2-见面设置卡；3-见面到打卡；4-评价卡片
    val recordId: String?,//对应业务记录id
    val maleUserId: String?,//男用户id
    val femaleUserId: String?,//女用户id
    val extentJson: String?,//拓展信息 json
): Serializable

data class MeetingProtectCardItemInvite(
    val maleUserStatus: Int,//男状态：1-未确认，2-拒绝；3-接受
    val femaleUserStatus: Int,//女用户状态：1-未确认，2-拒绝；3-接受
    val maleUserTag: String?,// 男用户标签 多个逗号分割
    val femaleUserTag: String?,// 女用户标签 多个逗号分割
    val expireTime: Long,//过期时间，毫秒值
): Serializable

data class MeetingProtectCardItemSetting(
    val status: Int,//设置状态 10- 未设置，11-已设置  12-已同意 13-已取消
    val settingActiveId: String?,//主动设置uid
    val inviteActiveId: String?,//主动邀请uid
    val cancelUserId: String?,//主动取消uid
    val time: Long,//时间，毫秒值
    val address: String?,//见面地点
    val gps:String?,//经纬度英文逗号分割
): Serializable

data class MeetingProtectCardItemConfirm(
    val maleUserStatus: Int,//男状态：0-未确认，20-已到达
    val femaleUserStatus: Int,//女用户状态：0-未确认，20-已到达
    val time: Long,//时间，毫秒值
    val address: String?,//见面地点
    val maleProtectOpenStatus: Int,//0-未开启守护，1开启守护
    val femaleProtectOpenStatus: Int,//0-未开启守护，1开启守护
    val gps:String?,//经纬度英文逗号分割
): Serializable

data class MeetingProtectCardItemReviews(
    val maleUserStatus: Int,//男状态：1-未确认，2-拒绝；3-接受
    val femaleUserStatus: Int,//女用户状态：1-未确认，2-拒绝；3-接受
): Serializable