package com.kanzhun.foundation.model.message;

import com.kanzhun.utils.GsonUtils;
import com.xxjz.orange.protocol.codec.ChatProtocol;

import java.io.Serializable;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/31
 */
public class MessageForDateCard extends ChatMessage {
    private MessageForDateCard.DateCardInfo mDateCardInfo = new MessageForDateCard.DateCardInfo();

    @Override
    protected void parserMessage(ChatProtocol.OgMessage message) {
        ChatProtocol.OgDateCardMedia dateCard = message.getBody().getDateCard();
        setDateCardInfo(new DateCardInfo(dateCard.getStatus(), dateCard.getText()));
    }

    @Override
    public void parseFromDB() {
        super.parseFromDB();
        mDateCardInfo = GsonUtils.getGson().fromJson(getContent(), MessageForDateCard.DateCardInfo.class);
    }

    @Override
    public void prepare2DB() {
        super.prepare2DB();
        String content = GsonUtils.getGson().toJson(mDateCardInfo);
        setContent(content);
    }

    public DateCardInfo getDateCardInfo() {
        return mDateCardInfo;
    }

    public void setDateCardInfo(DateCardInfo mDateCardInfo) {
        this.mDateCardInfo = mDateCardInfo;
    }

    public MessageForDateCard() {
        setMediaType(MessageConstants.MSG_DATE_CARD);
    }

    @Override
    public String getSummary() {
        return "[相识卡]";
    }

    public static class DateCardInfo implements Serializable {
        private static final long serialVersionUID = -6353277694147561856L;
        private int status;// 相识卡状态 10-待处理, 20-已接受, 30-已拒绝, 40-超时
        private String text;

        public DateCardInfo() {
        }

        public DateCardInfo(int status, String text) {
            this.status = status;
            this.text = text;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public String getText() {
            return text;
        }

        public void setText(String text) {
            this.text = text;
        }
    }
}
