package com.kanzhun.foundation.model.profile;

import com.kanzhun.foundation.api.bean.VoiceAnswerBean;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.kernel.account.AccountHelper;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/18.
 */
public class ProfileMetaModel extends ProfileInfoModel implements Serializable {
    public static final int STATUS_UNDER_REVIEW = 1;//审核中
    public static final int STATUS_REJECTED = 2;//已驳回
    public static final int STATUS_PASS_THROUGH = 3;//通过
    public static final int EDIT_ENABLE = 1;

    public static final int STATUS_LIKE_YOU = 12;
    public static final int STATUS_ORDINARY_FRIENDS = 20;//普通好友
    public static final int STATUS_DATING = 30;//相识中
    public static final int STATUS_LOVERS = 40;//情侣
    private static final long serialVersionUID = 2458848402299717138L;

    public int userInfoProcess;//完成进度
    //  "canPreview": 1, // 是否可预览，1-是，0-否
    public String canNotPreviewReason;//不可预览原因
    public int canPreview;
    public boolean joinActivity;// 是否参与过线下活动

    public int unSubmitBaseInfoCount; // 自己预览自己 展示未提交个人信息的数量

    // "relationStatus": 11, // 关系：11-你喜欢TA，12-TA喜欢你，20-普通好友，30-相识中，40-情侣，50-历史，60-删除
    public int relationStatus;//可以使用RelationStatus的枚举判断

    //   "showLike": 1, // 是否展示喜欢，1-是，0-否
    @Deprecated
    public int showLike;

    public int canSendLike;//1-是，0-否


    public int newMomentCount;// 未读的动态数量，暂只要大于0就展示红点

    public VoiceAnswerBean localVoiceAnswerBean;//本地声音答题

    public boolean isForwarded;//是否已经妆发

    public int bindStatus;//ParentBindStatus

    public boolean isSelfChild;//是否是自己的孩子

    public String lid;//是否是自己

    public boolean blockPreview(){
        //自己看自己，不阻断
        if(userId.equals(AccountHelper.getInstance().getUserId()) ){
            return false;
        }else if(abnormalUser()){//注销、封禁、锁定的用户，阻断
            return true;
        }else if(hideByUser()){//自己隐藏了信息，阻断
            return true;
        }else if(isLoveMode()){
            return true;
        }
        return false;
    }

    public boolean isLoveMode(){
        boolean isMelove = (relationStatus == STATUS_DATING || relationStatus == STATUS_LOVERS);
        boolean isLoveMode = (userCurrentRelation == 3 || userCurrentRelation == 2);
        return isLoveMode && !isMelove;
    }

    /**
     * 是否是好友
     * @return
     */
    public boolean isFriend(){
        return relationStatus == STATUS_ORDINARY_FRIENDS || relationStatus == STATUS_DATING || relationStatus == STATUS_LOVERS;
    }

    /**
     * 是否情侣
     * @return
     */
    public boolean isLover(){
        return relationStatus == STATUS_ORDINARY_FRIENDS || relationStatus == STATUS_DATING || relationStatus == STATUS_LOVERS;
    }
}
