package com.kanzhun.foundation.model.message;

import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.kanzhun.foundation.db.DBConstants;

@Entity(tableName = DBConstants.TAB_MESSAGE, indices = @Index(value = {"chatId", "seq"}))
public class MessageRecord {
    @PrimaryKey
    private long mid;// 消息id，类型：long
    private long seq;// 消息顺序id, 类型：long
    private int mediaType;//消息类型：1-文本，2-图片，3-语音，4-小视频，5-动态表情，6-文件，7-灰条，8-动作，9-名片，10-转发消息，11-红包，12-系统消息卡片, 13-语音视频
    private String sender;// 发送方用户id，类型：long
    private String chatId;// 会话ID，群id或者联系人id，类型：long
    private boolean badged;//是否计数
    private int withdraw; // 消息状态：0-未撤回，12-撤回
    private int status;// 0:发送 1:送达/部分已读 2:已读/全部已读；单聊中每条消息都要发送已读回执
    private String content;// 消息内容，消息撤回后为null
    private byte[] data;//扩展字段
    private String extStr;
    private int type = MessageConstants.MSG_SINGLE_CHAT;//聊天类型 1:单聊 2:群聊
    private long cmid;//客户端消息id
    private long time;
    private boolean isShow = true;
    private String extension;
    private int transformer;
    private int version = 0;
    private long updateTime;
    private long replyId;//回复的消息id
    private String illegalText;//消息违规提示
    private int relationStatus;//消息发送时，双方好友关系

    private boolean deleted;//是否删除 0:未删除 1:已删除

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public long getMid() {
        return mid;
    }

    public void setMid(long mid) {
        this.mid = mid;
    }

    public long getSeq() {
        return seq;
    }

    public void setSeq(long seq) {
        this.seq = seq;
    }

    public int getMediaType() {
        return mediaType;
    }

    public void setMediaType(int mediaType) {
        this.mediaType = mediaType;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public String getChatId() {
        return chatId;
    }

    public void setChatId(String chatId) {
        this.chatId = chatId;
    }

    public int getWithdraw() {
        return withdraw;
    }

    public void setWithdraw(int withdraw) {
        this.withdraw = withdraw;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public byte[] getData() {
        return data;
    }

    public void setData(byte[] data) {
        this.data = data;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public long getCmid() {
        return cmid;
    }

    public void setCmid(long cmid) {
        this.cmid = cmid;
    }

    public boolean isBadged() {
        return badged;
    }

    public void setBadged(boolean badged) {
        this.badged = badged;
    }

    public String getExtStr() {
        return extStr;
    }

    public void setExtStr(String extStr) {
        this.extStr = extStr;
    }

    public boolean isShow() {
        return isShow;
    }

    public void setShow(boolean show) {
        isShow = show;
    }

    public int getTransformer() {
        return transformer;
    }

    public void setTransformer(int transformer) {
        this.transformer = transformer;
    }

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public long getReplyId() {
        return replyId;
    }

    public void setReplyId(long replyId) {
        this.replyId = replyId;
    }

    public String getIllegalText() {
        return illegalText;
    }

    public void setIllegalText(String illegalText) {
        this.illegalText = illegalText;
    }

    public int getRelationStatus() {
        return relationStatus;
    }

    public void setRelationStatus(int relationStatus) {
        this.relationStatus = relationStatus;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }
}
