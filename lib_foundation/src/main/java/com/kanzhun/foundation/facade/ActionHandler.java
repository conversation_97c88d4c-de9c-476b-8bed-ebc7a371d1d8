package com.kanzhun.foundation.facade;

import android.text.TextUtils;

import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageForAction;
import com.petterp.floatingx.imp.FxAppLifecycleProvider;

import java.util.ArrayList;
import java.util.List;

final class ActionHandler {
    private MessageCache mMessageCache;
    private ConversationCallback mConversationCallback;
    private MessageHandler mMessageHandler;
    private VideoMessageCallback mVideoMessageCallback;


    public ActionHandler(MessageCache messageCache) {
        mMessageCache = messageCache;
    }

    public void setMessageCallback(ConversationCallback conversationCallback) {
        mConversationCallback = conversationCallback;
    }

    public void setMessageHandler(MessageHandler messageHandler) {
        mMessageHandler = messageHandler;
    }

    public void setVideoChatCallback(VideoMessageCallback videoMessageCallback) {
        mVideoMessageCallback = videoMessageCallback;
    }

    public boolean handleAction(MessageForAction messageForAction, boolean isRealTimeMessage) {
        boolean interceptHover = false;
        ConversationCallback conversationCallback = mConversationCallback;
        switch (messageForAction.getContent()) {
            case MessageForAction.TYPE_REFRESH_STATUS_MULTIPLE:
                MessageForAction.MultipleActionInfo actionInfos = (MessageForAction.MultipleActionInfo) messageForAction.getBody();
                if (actionInfos != null && actionInfos.mids != null && actionInfos.mids.size() > 0) {
                    List<Long> clearBadgedMids = isRealTimeMessage && actionInfos.clearBadgedMids != null ? actionInfos.clearBadgedMids : new ArrayList<>();
                    mMessageHandler.refreshMessages(actionInfos.mids, clearBadgedMids);
                }
                interceptHover = true;
                break;
//
//            case MessageForAction.TYPE_HIDE:
//                MessageForAction.HideInfo hide = (MessageForAction.HideInfo) messageForAction.getBody();
//                if (hide != null && hide.mid > 0) {
//                    mMessageHandler.hideMessages(messageForAction.getChatId(), messageForAction.getType(), Arrays.asList(hide.mid));
//                    if (conversationCallback != null && hide.clearBadged == 1) {
//                        conversationCallback.onHide(messageForAction, Arrays.asList(hide.mid), isRealTimeMessage);
//                    }
//                }
//                interceptHover = true;
//                break;
//            case MessageForAction.TYPE_HIDE_MULTIPLE:
//                MessageForAction.MultipleHideInfo hideInfos = (MessageForAction.MultipleHideInfo) messageForAction.getBody();
//                if (hideInfos != null && hideInfos.mids != null && hideInfos.mids.size() > 0) {
//                    mMessageHandler.hideMessages(messageForAction.getChatId(), messageForAction.getType(), hideInfos.mids);
//                    if (conversationCallback != null && !LList.isEmpty(hideInfos.clearBadgedMids)) {
//                        conversationCallback.onHide(messageForAction, hideInfos.clearBadgedMids, isRealTimeMessage);
//                    }
//                }
//                interceptHover = true;
//                break;
//
            case MessageForAction.TYPE_VIDEO_CHAT_INVITATION:///邀请通话,已经有实时判断，不需要判断isRealTimeMessage，
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null) {
                    mVideoMessageCallback.invitationChat(messageForAction);
                    mConversationCallback.onReceiveMessage(messageForAction, isRealTimeMessage);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_INVITATION_CANCEL://取消呼叫
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.invitationCancel(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_REFUSE://拒绝呼叫
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.invitationRefuse(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_OVER://通话结束
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.videoChatOver(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_BROKEN:// 通话异常断开
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.videoChatBroken(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_NOANSWER://超时取消
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.videoChatNoAnswer(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.TYPE_VIDEO_CHAT_ACCEPT:// 接受呼叫
                if (!TextUtils.equals(messageForAction.getSender(), AccountHelper.getInstance().getAccount().getUserId()) && mVideoMessageCallback != null && isRealTimeMessage) {
                    mVideoMessageCallback.videoChatAccept(messageForAction);
                }
                interceptHover = true;
                break;
            case MessageForAction.SEND_LOG_ACTION: //上传tlog
                MessageForAction.SendLogInfo logInfo = (MessageForAction.SendLogInfo) messageForAction.getBody();
                if (logInfo.platform == 2) {
                    SyncDispatch.getInstance().setEvent(SyncDispatch.EVENT_TLOG_UPLOAD, logInfo.date);
                }
                interceptHover = true;
                break;
//            case MessageForAction.TYPE_EMOJI_REPLY_MESSAGE:
//                MessageForAction.EmojiReplyInfo emojiReplyInfo = (MessageForAction.EmojiReplyInfo) messageForAction.getBody();
//                if (isRealTimeMessage) {
//                    mConversationCallback.emojiReplyPush(mMessageCache.query(emojiReplyInfo.msgId), emojiReplyInfo.content, emojiReplyInfo.title);
//                }
//                break;
            case MessageForAction.TYPE_MESSAGE_SEND_FAIL:
                MessageForAction.SendFailedInfo sendFailedInfo = (MessageForAction.SendFailedInfo) messageForAction.getBody();
                if (sendFailedInfo != null && sendFailedInfo.msgId > 0 && isRealTimeMessage) {
                    ChatMessage chatMessage = mMessageCache.query(sendFailedInfo.msgId);
                    if (chatMessage != null) {
                        conversationCallback.onSendRefreshFailed(chatMessage);
                        mMessageHandler.dealActionFailedMessage(chatMessage,sendFailedInfo.toast);
                    }
                }
                interceptHover = true;
                break;
            case MessageForAction.FINISH_NOVICE_GUIDE_TASK_ACTION://新手引导资料审核通过
                NoviceTaskRepository noviceTaskRepository = ServiceManager.getInstance().getNoviceTaskService().getNoviceTaskRepository();
                if(noviceTaskRepository != null){
                    noviceTaskRepository.noviceTaskAllApprove();
                }
                interceptHover = true;
                break;


        }
        return interceptHover;
    }

    public void refreshMessagesFormHistory(List<Long> refreshIds) {
        if (mMessageHandler != null) {
            mMessageHandler.refreshMessages(refreshIds, null);
        }
    }

    public void hideMessageFromHistory(List<Long> hideIds) {
        mMessageCache.hideMessages(hideIds);
    }
}
