package com.kanzhun.foundation.views;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.os.Handler;
import android.os.Message;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.kanzhun.common.animator.BaseViewAnimator;
import com.kanzhun.common.animator.YoYo;
import com.kanzhun.common.animator.interpolator.InterpolatorUtil;
import com.kanzhun.common.views.image.imageloader.progress.OnProgressListener;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.animation.CardOverTurnAnimationEngine;
import com.kanzhun.foundation.animation.OnOverTurnListener;
import com.kanzhun.foundation.api.callback.SendLikeBean;
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData;
import com.kanzhun.foundation.api.model.ABFace;
import com.kanzhun.foundation.api.model.ProfileInfoModel;
import com.kanzhun.foundation.databinding.MeLayoutAbFaceLayoutBinding;
import com.kanzhun.foundation.databinding.MeLayoutAbImpressionV2Binding;
import com.kanzhun.foundation.sp.SpManager;
import com.kanzhun.utils.views.OnMultiClickListener;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

import kotlin.Unit;
import kotlin.jvm.functions.Function0;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/5/5
 * 预览页ab面
 */
public class ABImpressionViewV2 extends FrameLayout {

    private MeLayoutAbImpressionV2Binding binding;

    private AtomicBoolean guideRunning = new AtomicBoolean(false);

    private BaseViewAnimator shakeAnimator;
    private BaseViewAnimator guideAnimator;

    private CardOverTurnAnimationEngine animationEngine;

    private TurnClickListener turnClickListener;

    private Handler handler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(Message msg) {
            if (msg.what == 1) {
                guideFinishAnim();
            }
            return false;
        }
    });

    public ABImpressionViewV2(@NonNull Context context) {
        this(context, null);
    }

    public ABImpressionViewV2(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public ABImpressionViewV2(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    public MeLayoutAbFaceLayoutBinding getSlideA() {
        return binding.slideA;
    }

    public MeLayoutAbFaceLayoutBinding getSlideB() {
        return binding.slideB;
    }

    private void init(Context context) {
        binding = MeLayoutAbImpressionV2Binding.inflate(LayoutInflater.from(context), null, false);
        addView(binding.getRoot());
        animationEngine = new CardOverTurnAnimationEngine(getSlideA().getRoot(), getSlideB().getRoot(), 250, new OnOverTurnListener() {
            @Override
            public void onAnimationStart() {
                cancelGuideAnimator();

            }

            @Override
            public void onAnimationEnd() {

            }
        });
        getSlideB().ivGuide.setVisibility(View.GONE);
        getSlideA().flAnimationView.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                animationEngine.overTurn();
                if(turnClickListener != null){
                    turnClickListener.onClick();
                }
            }
        });
        getSlideB().flAnimationView.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                animationEngine.overTurn();
                if(turnClickListener != null){
                    turnClickListener.onClick();
                }
            }
        });
        getSlideA().tvContent.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                animationEngine.overTurn();
                if(turnClickListener != null){
                    turnClickListener.onClick();
                }

            }
        });
        getSlideB().tvContent.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                animationEngine.overTurn();
                if(turnClickListener != null){
                    turnClickListener.onClick();
                }
            }
        });
    }

    public void setTurnClickListener(TurnClickListener turnClickListener) {
        this.turnClickListener = turnClickListener;
    }


    private void cancelGuideAnimator() {
        handler.removeMessages(1);
        if (guideAnimator != null && guideAnimator.isRunning()) {
            guideAnimator.cancel();
        }
        getSlideA().ivGuide.setVisibility(View.GONE);
    }


    public void setLikeData(int hideThumb,
                            String uid, SendLikeBean sendLikeBean,SendLikeBeanItemData mSendLikeBeanItemData){
        if(item == null){
            return;
        }
        getSlideA().idLike.setVisibility(VISIBLE);
        getSlideB().idLike.setVisibility(VISIBLE);

        getSlideA().idLike.setData(hideThumb, item.thumbInfo, true, uid, PreviewLikeView.LIKE_TYPE.AB_FACE, item.id, sendLikeBean, mSendLikeBeanItemData, false,false, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return null;
            }
        });
        getSlideB().idLike.setData(hideThumb, item.thumbInfo, true, uid, PreviewLikeView.LIKE_TYPE.AB_FACE, item.id, sendLikeBean, mSendLikeBeanItemData, false,false, new Function0<Unit>() {
            @Override
            public Unit invoke() {
                return null;
            }
        });
        getSlideA().idLike.addDataChange(new PreviewLikeView.LikeChange() {
            @Override
            public void change(int hideThumb, @Nullable ProfileInfoModel.ThumbInfoItem thumbInfo, boolean useWhite, @Nullable String uid, @Nullable PreviewLikeView.LIKE_TYPE likeType, @Nullable String resourceId) {
                getSlideA().idLike.setData(hideThumb, thumbInfo, true, uid, likeType, resourceId, sendLikeBean, mSendLikeBeanItemData, false, false,new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        return null;
                    }
                });
                getSlideB().idLike.setData(hideThumb, thumbInfo, true, uid, likeType, resourceId, sendLikeBean, mSendLikeBeanItemData, false,false, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        return null;
                    }
                });
            }
        });

        getSlideB().idLike.addDataChange(new PreviewLikeView.LikeChange() {
            @Override
            public void change(int hideThumb, @Nullable ProfileInfoModel.ThumbInfoItem thumbInfo, boolean useWhite, @Nullable String uid, @Nullable PreviewLikeView.LIKE_TYPE likeType, @Nullable String resourceId) {
                getSlideA().idLike.setData(hideThumb, thumbInfo, true, uid, likeType, resourceId, sendLikeBean, mSendLikeBeanItemData, false, false,new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        return null;
                    }
                });
                getSlideB().idLike.setData(hideThumb, thumbInfo, true, uid, likeType, resourceId, sendLikeBean, mSendLikeBeanItemData, false,false, new Function0<Unit>() {
                    @Override
                    public Unit invoke() {
                        return null;
                    }
                });
            }
        });
    }

    private ABFace item;
    public void setData(ABFace item,OnProgressListener progressListener) {
        this.item = item;
        getSlideA().ivPhoto.loadProgress(item.photoA, progressListener);
        getSlideA().tvContent.setText(item.titleA);
        getSlideB().ivPhoto.loadProgress(item.photoB, new OnProgressListener() {
            @Override
            public void onProgress(boolean isComplete, int percentage, long bytesRead, long totalBytes) {

            }

            @Override
            public void onSuccess(File file) {

            }

            @Override
            public void onFile(Throwable throwable) {

            }
        });
        getSlideB().tvContent.setText(item.titleB);

    }


    /**
     * 完全暴露时 执行动画
     * 首次看到ab面时，icon旋转动画，卡片整体左右晃动，出现气泡引导
     * 引导气泡不再出现后，每次用户ab面在画面中完全露出，均播放icon动画，同时卡片轻微转动
     */
    public void showAnimator() {
        if (!animationEngine.isRunning() && guideRunning.compareAndSet(false, true)) {
            shakeAnimator = new BaseViewAnimator() {
                @Override
                protected void prepare(View target) {
                    getAnimatorAgent().playTogether(
                            ObjectAnimator.ofFloat(target, "rotationY", 0, -3, 3, 0)
                    );
                }
            };
            YoYo.AnimationComposer composer = YoYo.with(shakeAnimator).duration(450).withListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    super.onAnimationEnd(animation);
                    guideRunning.set(false);
                    if (animationEngine.isSideA()) {
                        getSlideB().getRoot().setVisibility(View.VISIBLE);
                    }
                }
            }).interpolate(new DecelerateInterpolator());
            if (animationEngine.isSideA()) {
                getSlideB().getRoot().setVisibility(View.INVISIBLE);
                getSlideA().animationViewA.playAnimation();
                composer.playOn(getSlideA().getRoot());
            } else {
                getSlideB().animationViewA.playAnimation();
                composer.playOn(getSlideB().getRoot());
            }
            boolean showGuide;
            showGuide = SpManager.get().user().getBoolean(Constants.HAS_SHOW_PREVIEW_AB_FACE, false);
            if (!showGuide) {
                SpManager.putUserBoolean(Constants.HAS_SHOW_PREVIEW_AB_FACE, true);

                guideAnimator = new BaseViewAnimator() {
                    @Override
                    protected void prepare(View target) {
                        getAnimatorAgent().playTogether(
                                ObjectAnimator.ofFloat(target, "scaleX", 0.0f, 1),
                                ObjectAnimator.ofFloat(target, "scaleY", 0.0f, 1),
                                ObjectAnimator.ofFloat(target, "alpha", 0, 1)
                        );
                    }
                };
                getSlideA().ivGuide.setVisibility(VISIBLE);
                int height = getSlideA().ivGuide.getHeight();
                YoYo.with(guideAnimator).pivotY(height).duration(300).withListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        super.onAnimationEnd(animation);
                        Message msg = Message.obtain();
                        msg.what = 1;
                        handler.sendMessageDelayed(msg, 2000);
                    }
                }).interpolate(InterpolatorUtil.createDefaultOvershootInterpolator()).playOn(getSlideA().ivGuide);
            }
        }
    }

    private void guideFinishAnim() {
        guideAnimator = new BaseViewAnimator() {
            @Override
            protected void prepare(View target) {
                getAnimatorAgent().playTogether(
                        ObjectAnimator.ofFloat(target, "scaleX", 1, 0.0f),
                        ObjectAnimator.ofFloat(target, "scaleY", 1, 0.0f),
                        ObjectAnimator.ofFloat(target, "alpha", 1, 0.0f)
                );
            }
        };
        int height = getSlideA().ivGuide.getHeight();
        YoYo.with(guideAnimator).pivotY(height).duration(300).withListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                super.onAnimationEnd(animation);
                getSlideA().ivGuide.setVisibility(View.GONE);
            }
        }).interpolate(InterpolatorUtil.createDefaultOvershootInterpolator()).playOn(getSlideA().ivGuide);
    }

    public interface TurnClickListener {
        void onClick();
    }
}
