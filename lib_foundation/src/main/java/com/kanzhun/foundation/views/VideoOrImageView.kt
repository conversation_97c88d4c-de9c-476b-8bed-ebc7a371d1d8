package com.kanzhun.foundation.views

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import com.kanzhun.common.kotlin.ext.dp
import com.kanzhun.common.kotlin.ext.dpI
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.views.image.OImageView
import com.kanzhun.foundation.api.bean.UserInfoStoryBean
import com.kanzhun.foundation.api.callback.SendLikeBean
import com.kanzhun.foundation.api.callback.SendLikeBeanItemData
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.api.model.VideoExtendInfo
import com.kanzhun.foundation.databinding.MePreviewVideoOrImageViewBinding
import com.kanzhun.foundation.facade.TempTaskType
import com.kanzhun.foundation.kotlin.ktx.toVideoOrAudioDuration
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.player.OPlayerHelper
import com.kanzhun.foundation.utils.StringUtil
import com.kanzhun.utils.GsonUtils
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.techwolf.lib.tlog.TLog


class VideoOrImageView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : ConstraintLayout(context, attrs, defStyleAttr) {
    private val mBinding = MePreviewVideoOrImageViewBinding.inflate(LayoutInflater.from(context), this, true)

    private var mData: UserInfoStoryBean? = null

    private var isRealPlayer = false

    fun setData(story: ProfileInfoModel.Story, hideThumb: Int, uid: String?, likeType: PreviewLikeView.LIKE_TYPE?, sendLikeBean: SendLikeBean) {
        val data = story.toUserInfoStoryBean(isRealPlayer)
        this.mData = data
        showPicture(data)
        mBinding.apply {
            idLike.setData(hideThumb,story.thumbInfo,true,uid,
                likeType,story.id,sendLikeBean, SendLikeBeanItemData(url = data.story.photo)
            )
            if(likeType == PreviewLikeView.LIKE_TYPE.INTRO_PHOTO){
                ServiceManager.getInstance().tempTask.getTempTaskRepository()?.handlerView(idLottieAnimationView,
                    TempTaskType.CP_Preview_IntroImg,uid,story.id)
            }else if(likeType == PreviewLikeView.LIKE_TYPE.INTEREST_PHOTO){
                ServiceManager.getInstance().tempTask.getTempTaskRepository()?.handlerView(idLottieAnimationView,
                    TempTaskType.CP_Preview_InterestImg,uid,story.id)
            }else if(likeType == PreviewLikeView.LIKE_TYPE.STORY_PHOTO){
                ServiceManager.getInstance().tempTask.getTempTaskRepository()?.handlerView(idLottieAnimationView,
                    TempTaskType.CP_Preview_StoryImg,uid,story.id)
            }else{
                idLottieAnimationView.gone()
            }


            if (data.story != null && data.story.type == ProfileInfoModel.Story.TYPE_LIVE_PHOTO) {
                idLivePhoto.visible()
                tvDuration.gone()
//                tvDuration.textOrGone(data.durationStr)
            } else {
                tvDuration.gone()
                idLivePhoto.gone()
            }
            if (data.story != null && data.story.text.isNullOrEmpty()) {
                idText.gone()
            } else {
                idText.visible()
                idTextContent.text = data.story.text
            }

        }
    }


    private fun ProfileInfoModel.Story.toUserInfoStoryBean(isRealPlayer: Boolean): UserInfoStoryBean {
        val storyBean = UserInfoStoryBean()
        if (video.isNullOrBlank()) {
            //照片故事
            storyBean.story = this
        } else {
            //视频故事
            storyBean.story = this
            storyBean.playVideo = StringUtil.getPlayVideoUrl(video)
            TLog.debug(OPlayerHelper.TAG, "VideoOrImageView playVideo = ${storyBean.playVideo}")
            if (!this.extendInfo.isNullOrBlank()) {
                val extendInfo = GsonUtils.getGson().fromJson(this.extendInfo, VideoExtendInfo::class.java)
                storyBean.durationStr = extendInfo.duration?.toVideoOrAudioDuration()
            }
        }
        return storyBean
    }

    fun startPlay() {

    }

    fun pause() {

    }

    fun stop() {

    }

    private fun showPicture(data: UserInfoStoryBean) {
        var view: OImageView? = mBinding.ivPhotoV
        val width = QMUIDisplayHelper.getScreenWidth(context) - 24.dpI
        if(data.story.width != 0 && data.story.height != 0){
            var f = data.story.width.toFloat() / data.story.height.toFloat()
            if(f >= 3.0f/1.0f){
                //3:1
                val lp = view?.layoutParams
                var pHeight = width / (3.0f/1.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }  else if(f <= 3.0f/4.0f){
                //预览时最大按3:4展示
                val lp = view?.layoutParams
                var pHeight = width / (3.0f/4.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }else if(f >= 4.0f/3.0f){
                //预览时最大按3:4展示
                val lp = view?.layoutParams
                var pHeight = width / (4.0f/3.0f)
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            } else {
                val lp = view?.layoutParams
                var pHeight = width / f
                if(pHeight.toInt() > 560.dpI){
                    pHeight = 560.dp
                }
                if(pHeight.toInt() < 160.dpI){
                    pHeight = 160.dp
                }
                lp?.height = pHeight.toInt()
                view?.layoutParams = lp
            }

        }else{
            var f = 3.0f / 4.0f
            val lp = view?.layoutParams
            var pHeight = width / f
            lp?.height = pHeight.toInt()
            view?.layoutParams = lp
        }
        //照片故事
        view?.visible()
        mBinding.apply {
            flError.gone()
            tvDuration.gone()
        }
        view?.load(data.story.photo)
    }

    fun release() {
    }

    fun setRealPlayer(isRealPlayer: Boolean) {
//        this.isRealPlayer = isRealPlayer
        this.isRealPlayer = false
    }

}
