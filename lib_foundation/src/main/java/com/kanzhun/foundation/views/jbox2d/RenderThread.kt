package com.kanzhun.foundation.views.jbox2d

import android.content.Context
import android.graphics.Color
import android.graphics.PorterDuff
import android.view.SurfaceHolder
import android.view.View
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.bean.RecommendTagIdealPartnerMatchBean
import com.kanzhun.foundation.bean.RecommendTagIdealPartnerMatchResponse
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.BUBBLE_COUNT
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.BUBBLE_SIZE
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.BUBBLE_TEXT_SIZE
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.H_MARGIN_L
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.H_MARGIN_R
import com.kanzhun.foundation.views.jbox2d.BubbleConfig.Companion.MAX_ITEM
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.utils.T
import com.techwolf.lib.tlog.TLog
import com.thecodeyard.playground.jbox2d.Bubble
import com.thecodeyard.playground.jbox2d.BubbleView
import com.thecodeyard.playground.jbox2d.BubbleWorld
import org.jbox2d.dynamics.Body
import java.util.Random
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CopyOnWriteArrayList

class RenderThread(val context:Context,val surfaceHolder: SurfaceHolder,val list: MutableList<RecommendTagIdealPartnerMatchBean>,val userPop:Boolean): Thread(), BubbleWorld.Listener {
    private var measuredWidth: Int = 0
    private var measuredHeight: Int = 0
    private val bubbleViews = ConcurrentHashMap<Int, BubbleViewInfoViewDraw>()

    var scrollOffsetX = 0f
    var addBubbleCount = 0
    var maxWidth = 0

    var newlist = CopyOnWriteArrayList<RecommendTagIdealPartnerMatchBean>()

    @Volatile
    private var isRunning = false
    val selectList = CopyOnWriteArrayList<RecommendTagIdealPartnerMatchBean>()

    fun setWorldSize(measuredWidth: Int, measuredHeight: Int) {
        this.measuredWidth = measuredWidth
        this.measuredHeight = measuredHeight
        // Create the world if it's not already created.
        selectList.clear()
    }

    private fun initBubbles() {
        isRunning = false
        list.forEachIndexed { index, recommendTagIdealPartnerMatchBean ->
            val sizeRandom = Random().nextInt(BUBBLE_SIZE.size)
            var x = measuredWidth - MAX_ITEM
            if(list[index].isSelect){
                x = measuredWidth -MAX_ITEM*2
            }
            val bubble = createBubble(list[index].content?:"",x,(index % BUBBLE_COUNT) * MAX_ITEM + MAX_ITEM,sizeRandom,list[index].isSelect,list[index])
            if(list[index].isSelect){
                selectList.add(bubble.data)
            }
            newlist.addAll(list)
            addBubbleView(bubble)
        }
        if (selectList.size >= 3){
            AppThreadFactory.runOnUiThread {
                update(true)
            }
        }
        isRunning = true
    }

    private fun createBubble(str:String,x: Float, y: Float,sizeRandom:Int,select:Boolean = false,data:RecommendTagIdealPartnerMatchBean,parentId: Int = 0, useAnim:Boolean = false): Bubble {
        return Bubble(
            viewId = View.generateViewId(),
            viewSize = BUBBLE_SIZE[sizeRandom].toInt(),
            textSize = BUBBLE_TEXT_SIZE[sizeRandom],
            viewText = str,
            viewX = x,
            viewY = y,
            sizeStyle = sizeRandom,
            bgStyle = Random().nextInt(2),
            bgSelectStyle = Random().nextInt(3),
            select = select,
            data = data,
            parentId = parentId,
            useAnim = useAnim
        )
    }

    override fun onSimulationUpdate(bubble: Bubble) {

    }
    private var world: BubbleWorld? = null
    var pause = false
    var minx = 0
    var maxX = 0
    var startForce = 100
    var lastForce = 0L
    override fun run() {
        if (world == null){
            world = BubbleWorld(this)
            if (world?.state == BubbleWorld.State.IDLE) {
                world?.create(measuredWidth, measuredHeight)
                initBubbles()
            }
        }
        while (isRunning) {
            while (!pause){
                val canvas = surfaceHolder.lockCanvas() ?: continue
                try {
                    synchronized(surfaceHolder) {
                        // 使用 CLEAR 模式清除画布
                        canvas.drawColor(Color.TRANSPARENT, PorterDuff.Mode.CLEAR)
                        // 绘制白色背景
                        canvas.drawColor(Color.WHITE)
                        canvas.translate(-scrollOffsetX, 0f)
                        world?.update{bubble->
                            bubbleViews[bubble.viewId]?.let { bubbleInfo ->
                                bubbleInfo.x = bubble.viewX
                                bubbleInfo.y = bubble.viewY
                                bubbleInfo.draw(canvas)
                                minx = Math.min(minx, bubble.viewX.toInt())
                                maxX = Math.max(maxX, bubble.viewX.toInt())
                            }
                        }

                        if(startForce> 0 ){
                            lastForce = System.currentTimeMillis()
                            startForce--
                            world?.applyMagneticForce()
                        }else if (System.currentTimeMillis() - lastForce > 2500){
                            lastForce = System.currentTimeMillis()
                            world?.applyMagneticForce(0.005f)
                        }
                        if(world?.areBubblesSettled() == true){
                            if(maxWidth == 0 ){
                                world?.updateWorldBounds(-addBubbleCount * MAX_ITEM + H_MARGIN_L,maxWidth)
                                lastForce = System.currentTimeMillis()
                            }
                            maxWidth = (world?.getBubblesWidth(minx)?:0)+ (H_MARGIN_L + H_MARGIN_R).toInt()
                            maxX = maxWidth + minx
                        }

                        while (!taskQueue.isEmpty()) {
                            taskQueue.poll()?.run()
                            sleep(16)
                        }
//                        sleep(16)
                    }
                } catch (e: Exception){

                }finally {
                    try {
                        surfaceHolder.unlockCanvasAndPost(canvas)
                    }catch (e: Exception){

                    }
                }

            }

            sleep(16)
        }
        releaseRun()

    }

    var releaseRun:()->Unit = {}

    private val taskQueue = ConcurrentLinkedQueue<Runnable>()

    // 添加任务提交方法
    fun postTask(task: Runnable) {
        taskQueue.offer(task)
    }

    fun release(releaseRun:()->Unit){
        synchronized(surfaceHolder) {
            if(isRunning){
                this.releaseRun =releaseRun
                isRunning = false
                pause()
            }else{
                releaseRun()
            }
//            world?.destroyWorld()
        }

    }

    fun getBodyList(): Body? {
        return world?.getBodyList()
    }

    private fun initSelectStyle(bubble: Bubble, bubbleInfo: BubbleViewInfoViewDraw) {
        bubbleInfo.updateBackground()
    }

    private fun initStyle(bubble: Bubble, bubbleInfo: BubbleViewInfoViewDraw) {
        bubbleInfo.updateBackground()
    }

    /**
     * Adds a new [BubbleView] and tells the 2D world to add a body that corresponds to this view.
     */
    private fun addBubbleView(bubble: Bubble) {
        val bubbleInfo = BubbleViewInfoViewDraw(bubble, context)
        bubbleViews[bubble.viewId] = bubbleInfo

        // 设置初始状态
        if(bubble.select) {
            initSelectStyle(bubble, bubbleInfo)
        } else {
            initStyle(bubble, bubbleInfo)
        }

        // 设置点击监听
        bubbleInfo.setOnClickListener {
            if (bubble.select) {

                //删除选择
                postTask({
                    removeBubbles(bubble.viewId)
                })
                selectList.remove(bubble.data)
                if (selectList.size < 3){
                    AppThreadFactory.runOnUiThread {
                        update(false)
                    }
                }
                bubble.select = !bubble.select
                bubbleInfo.updateBackground()

                reportPoint("myinfo-idealtype-label-page-click"){
                    source = if (userPop) "首页全屏弹窗" else "个人资料编辑页"
                    type = "取消选中标签"
                }
            } else {
                if(selectList.size >= 30){
                    TLog.print("zhangz","setOnClickListener unselect size："+selectList.size)
                    T.ss("最多只能选30项哦")
                    return@setOnClickListener
                }
                //选择
                selectList.add(bubble.data)
                if (selectList.size >= 3){
                    AppThreadFactory.runOnUiThread {
                        update(true)
                    }
                }
                TLog.print("zhangz","setOnClickListener unselect size："+selectList.size)
                bubble.select = !bubble.select
                bubbleInfo.updateBackground()

                HttpExecutor.execute<RecommendTagIdealPartnerMatchResponse>(
                    RetrofitManager.getInstance()
                        .createApi<FoundationApi>(FoundationApi::class.java)
                        .requestRecommendTagIdealPartnerMatch(bubble.data.id),
                    object : BaseRequestCallback<RecommendTagIdealPartnerMatchResponse?>(false) {

                        override fun handleInChildThread(data: RecommendTagIdealPartnerMatchResponse?) {
                            super.handleInChildThread(data)
                            postTask({
                                reportPoint("myinfo-idealtype-label-page-click"){
                                    source = if (userPop) "首页全屏弹窗" else "个人资料编辑页"
                                    type = "选中标签"
                                    actionp2 = bubble.data.content
                                    actionp3 = data?.rows?.joinToString(","){
                                        it.content.toString()
                                    }
                                }
                                data?.let {
                                    val newRows = it.rows.filterNot { row ->
                                        newlist.any { selectedItem ->
                                            selectedItem.id == row.id
                                        }
                                    }
                                    newlist.addAll(newRows)
                                    addBubbles(newRows, bubbleInfo.x, bubbleInfo.y, bubble.viewId,bubble.viewSize,true)
                                }
                            })
                        }
                        override fun onSuccess(data: RecommendTagIdealPartnerMatchResponse?) {

                        }

                        override fun dealFail(reason: com.kanzhun.http.error.ErrorReason) {
                        }
                    })


            }

        }

        world?.createBubble(bubble)
    }

    private fun removeBubbles(id: Int) {
        sleep(40)
        for (i in 0 until 4) {
            val bubble = world?.removeBubble(id)
            bubble?.let {
                bubbleViews.remove(it.viewId)
            }
            newlist.forEach {recommendTagIdealPartnerMatchBean ->
                if(recommendTagIdealPartnerMatchBean.id == bubble?.data?.id){
                    newlist.remove(recommendTagIdealPartnerMatchBean)
                }
            }
        }
        initForce()
    }

    val positionListX = listOf(0,1,0,1)
    val positionListY = listOf(0,0,1,1)

    private fun addBubbles(tmpList: List<RecommendTagIdealPartnerMatchBean>, x1: Float, y1: Float, parentId: Int,radius:Int, useAnim:Boolean) {
//        synchronized(surfaceHolder) {
//            pause()
            if (tmpList.isNotEmpty()){
                addBubbleCount++
                maxWidth = maxWidth + addBubbleCount * MAX_ITEM.toInt()
                world?.updateWorldBounds(-addBubbleCount * MAX_ITEM + H_MARGIN_L,maxWidth)
            }

            tmpList.forEachIndexed { index, recommendTagIdealPartnerMatchBean ->
                val sizeRandom = Random().nextInt(BUBBLE_SIZE.size)
                val x_new = x1 + positionListX[index % 4] * BUBBLE_SIZE[sizeRandom]
                val y_new = y1 + positionListY[index % 4] * BUBBLE_SIZE[sizeRandom]
                val bubble = createBubble(tmpList[index].content?:"",x_new,y_new,sizeRandom = sizeRandom,
                    select = false, data = tmpList[index],parentId = parentId, useAnim = useAnim)
                addBubbleView(bubble)
            }

//            unPause()
            initForce()
//        }

    }

    fun initForce(){
        startForce = 100
    }

    private fun pause(){
        world?.pause()
        pause = true
    }

    private fun unPause(){
        world?.unPause()
        pause = false
    }

    var lastClick = 0L

    fun checkBubbleClick(x: Float, y: Float) {
        if (maxWidth == 0){
            return
        }
        if(lastClick != 0L && (System.currentTimeMillis()- lastClick) < 500){
            return
        }
        lastClick = System.currentTimeMillis()

        postTask({
            bubbleViews.values.forEach { bubbleInfo ->
                if (isPointInBubble(x + scrollOffsetX, y, bubbleInfo)) {
                    bubbleInfo.clickListener?.invoke()
                    //开启模拟
//                    unPause()
                    return@forEach
                }
            }
        })
//        synchronized(surfaceHolder) {
//            //停止模拟
////            pause()
//
//            //开启模拟
////            unPause()
//        }
    }

    private fun isPointInBubble(x: Float, y: Float, bubbleInfo: BubbleViewInfoViewDraw): Boolean {
        val centerX = bubbleInfo.x + bubbleInfo.bubble.viewSize / 2
        val centerY = bubbleInfo.y + bubbleInfo.bubble.viewSize / 2
        val radius = bubbleInfo.bubble.viewSize / 2

        val distance = Math.sqrt(
            Math.pow((x - centerX).toDouble(), 2.0) +
                    Math.pow((y - centerY).toDouble(), 2.0)
        )

        return distance <= radius
    }

    fun addScrollOffset(delta: Float):Boolean {
        // 计算新的偏移量，限制在0到最大可滚动范围之间
        val tmp = (scrollOffsetX + delta)
        if(delta > 0){
            // 向右滚动
            if (tmp > (maxX + H_MARGIN_R)){
                return false
            }else{
                scrollOffsetX = tmp
            }
        }else{
            // 向左滚动
            if (tmp < (minx - H_MARGIN_L)){
                return false
            }else{
                scrollOffsetX = tmp
            }
        }
        return true
    }

    var update: (Boolean) -> Unit = {}

    fun update(update: (Boolean) -> Unit) {
        this.update = update
    }

    fun onPause() {
        synchronized(surfaceHolder) {
            pause()
        }
    }

    fun onResume() {
        synchronized(surfaceHolder) {
            unPause()
        }
    }

}