package com.kanzhun.foundation.views.jbox2d

import com.kanzhun.common.kotlin.ext.dp

class BubbleConfig {
    companion object {
        val BUBBLE_SIZE = listOf(100.dp,115.dp,130.dp)
        val BUBBLE_TEXT_SIZE = listOf(16f,18f,18f)

        val MAX_ITEM = BUBBLE_SIZE[BUBBLE_SIZE.size-1]

        /**
         * The amount of bubbles to create.
         */
        const val BUBBLE_COUNT = 3
        val H_MARGIN_L = 30.dp
        val H_MARGIN_R = 0.dp
    }
}