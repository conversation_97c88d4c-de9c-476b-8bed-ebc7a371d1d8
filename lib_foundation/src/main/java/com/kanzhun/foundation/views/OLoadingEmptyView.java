package com.kanzhun.foundation.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.BindingAdapter;

import com.kanzhun.common.keyboard.util.DensityUtil;
import com.kanzhun.foundation.R;
import com.kanzhun.foundation.databinding.CommonLayoutLoadingEmptyBinding;
import com.kanzhun.utils.L;
import com.kanzhun.utils.views.OnMultiClickListener;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/7/26
 */
public class OLoadingEmptyView extends ConstraintLayout {
    public static final int LOADING = 1;
    public static final int ERROR = 2;
    public static final int NO_DATA = 3;
    public static final int SUCCESS = 4; //一般成功之后隐藏布局


    private int status = LOADING;
    private CommonLayoutLoadingEmptyBinding emptyBinding;
    private String loadingContent;
    private String errorContent;
    private String noDataContent;
    private int errorResourceId;
    private int noDataResourceId;
    private RetryListener listener;


    public OLoadingEmptyView(@NonNull Context context) {
        this(context, null);
    }

    public OLoadingEmptyView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public OLoadingEmptyView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        emptyBinding = CommonLayoutLoadingEmptyBinding.inflate(LayoutInflater.from(context), this, true);
        loadingContent = getResources().getString(R.string.common_loading);
        errorContent = getResources().getString(R.string.common_loading_error);
        noDataContent = getResources().getString(R.string.common_loading_no_data);
//        errorResourceId = R.mipmap.common_icon_load_error;
//        noDataResourceId = R.mipmap.common_ic_list_empty;

        if (attrs != null) {
            TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.o_loading_empty_view, 0, 0);
            if (a.hasValue(R.styleable.o_loading_empty_view_o_loading_text)) {
                loadingContent = a.getString(R.styleable.o_loading_empty_view_o_loading_text);
            }
            if (a.hasValue(R.styleable.o_loading_empty_view_o_error_text)) {
                errorContent = a.getString(R.styleable.o_loading_empty_view_o_error_text);
            }
            if (a.hasValue(R.styleable.o_loading_empty_view_o_no_data_text)) {
                noDataContent = a.getString(R.styleable.o_loading_empty_view_o_no_data_text);
            }
            if(a.hasValue(R.styleable.o_loading_empty_view_o_loading_text_size)){
                emptyBinding.tvDesc.getPaint().setTextSize(a.getDimension(R.styleable.o_loading_empty_view_o_loading_text_size, DensityUtil.dp2px(getContext(), 18.0f)));
            }

            if(a.hasValue(R.styleable.o_loading_empty_view_o_loading_text_color)){
                emptyBinding.tvDesc.setTextColor(a.getColor(R.styleable.o_loading_empty_view_o_loading_text_color,getResources().getColor(R.color.common_color_000000_80)));
            }

            int resourceId = a.getResourceId(R.styleable.o_loading_empty_view_o_no_data_img, 0);
            if (resourceId > 0) {
                noDataResourceId = resourceId;
            }

            int resourceId2 = a.getResourceId(R.styleable.o_loading_empty_view_o_error_img, 0);
            if (resourceId2 > 0) {
                errorResourceId = resourceId;
            }

            a.recycle();
        }

        emptyBinding.btnRetry.setOnClickListener(new OnMultiClickListener() {
            @Override
            public void OnNoMultiClick(View v) {
                if (emptyBinding.ivError.getVisibility() == View.VISIBLE) {
                    showLoading();
                    if (listener != null) {
                        listener.onRetry();
                    }
                }
            }
        });
    }

    public void showLoading() {
        if (status == LOADING) {
            return;
        }
        setStatusUI(LOADING);
    }

    public void showError() {
        if (status == ERROR) {
            return;
        }
        setStatusUI(ERROR);
    }

    public void showNoData() {
        if (status == NO_DATA) {
            return;
        }
        setStatusUI(NO_DATA);
    }

    public void showNoData(String noData) {
        if (status == NO_DATA && TextUtils.equals(noData, noDataContent)) {
            return;
        }
        noDataContent = noData;
        setStatusUI(NO_DATA);
    }

    public void setNoData(String noData) {
        noDataContent = noData;
    }

    private void setStatusUI(int status) {
        this.status = status;
        switch (status) {
            case LOADING:
                emptyBinding.ivError.setVisibility(INVISIBLE);
                emptyBinding.btnRetry.setVisibility(GONE);
//                emptyBinding.avLoad.setVisibility(VISIBLE);
                emptyBinding.avLoadBg.setVisibility(VISIBLE);
                emptyBinding.tvDesc.setText(loadingContent);
                break;
            case ERROR:
                emptyBinding.ivError.setVisibility(VISIBLE);
                emptyBinding.ivError.setAnimation("net_error/lost.json");
                emptyBinding.ivError.setImageAssetsFolder("net_error/images");
                emptyBinding.ivError.playAnimation();
//                emptyBinding.ivError.setImageResource(errorResourceId);
                emptyBinding.btnRetry.setVisibility(VISIBLE);
//                emptyBinding.avLoad.setVisibility(GONE);
                emptyBinding.avLoadBg.setVisibility(GONE);
                emptyBinding.tvDesc.setText(errorContent);
                break;
            case NO_DATA:
                emptyBinding.ivError.setVisibility(VISIBLE);
                emptyBinding.ivError.setAnimation("nocontent/nocontent.json");
                emptyBinding.ivError.setImageAssetsFolder("nocontent/images");
                emptyBinding.ivError.playAnimation();
//                emptyBinding.ivError.setImageResource(noDataResourceId);
                emptyBinding.btnRetry.setVisibility(GONE);
//                emptyBinding.avLoad.setVisibility(GONE);
                emptyBinding.avLoadBg.setVisibility(GONE);
                emptyBinding.tvDesc.setText(noDataContent);
                break;
            default:
                break;
        }
    }

    public void setRetryListener(RetryListener listener) {
        this.listener = listener;
    }

    public interface RetryListener {
        void onRetry();
    }

    @BindingAdapter({"loadingEmptyStatus"})
    public static void setLoadingEmptyStatus(OLoadingEmptyView loadingEmptyView, int status) {
        L.e("TAG", "setLoadingEmptyStatus = " + status);
        switch (status) {
            case LOADING:
                loadingEmptyView.setVisibility(View.VISIBLE);
                loadingEmptyView.showLoading();
                break;
            case ERROR:
                loadingEmptyView.setVisibility(View.VISIBLE);
                loadingEmptyView.showError();
                break;
            case NO_DATA:
                loadingEmptyView.setVisibility(View.VISIBLE);
                loadingEmptyView.showNoData();
                break;
            default:
                loadingEmptyView.setVisibility(View.GONE);
                break;
        }
    }
}
