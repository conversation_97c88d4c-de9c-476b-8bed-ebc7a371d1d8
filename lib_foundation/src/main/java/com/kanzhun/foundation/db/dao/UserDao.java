package com.kanzhun.foundation.db.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;

import com.kanzhun.foundation.model.User;

import java.util.List;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/1/29
 */
@Dao
public interface UserDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insert(com.kanzhun.foundation.model.User user);

    @Query("SELECT * FROM TB_USER WHERE userId = :userId")
    com.kanzhun.foundation.model.User getUser(String userId);

    @Query("SELECT * FROM TB_USER")
    List<com.kanzhun.foundation.model.User> getUsers();

    @Query("UPDATE TB_USER SET nickName = :nickName, nickNamePy = :nickNamePy, avatar = :avatar, tinyAvatar = :tinyAvatar, liveVideo = :liveVideo, school = :school, schoolLevel = :schoolLevel, schoolArea = :schoolArea, gender = :gender, birthday = :birthday, phase = :phase , communityLocked = :communityLocked , profileLocked = :profileLocked , modeCode = :modeCode , modeName = :modeName , modeIcon = :modeIcon , shakeLocked = :shakeLocked ,degree = :degree,loveGoal = :loveGoal ,addressCode = :addressCode ,hukouCode =:hukouCode, hometownCode =:hometownCode,industryCode =:industryCode,schoolTime=:schoolTime WHERE userId = :userId")
    void update(String userId, String nickName, String nickNamePy, String avatar, String tinyAvatar, String liveVideo, String school, int schoolLevel, int schoolArea, int gender, String birthday, int phase, int communityLocked, int profileLocked, String modeCode, String modeName, String modeIcon, int shakeLocked,int degree,int loveGoal,String addressCode,String hukouCode,String hometownCode,String industryCode,int schoolTime);

    @Query("UPDATE TB_USER SET childInfoGender =:childInfoGender,childInfoAddressCode =:childInfoAddressCode,childInfoAddress =:childInfoAddress ,childInfoBirthdayStr =:childInfoBirthdayStr WHERE userId = :userId")
    void updateChildInfo(String userId,int childInfoGender,String childInfoAddressCode,String childInfoAddress,String childInfoBirthdayStr);

    @Query("UPDATE TB_USER SET matchStatus = :matchStatus WHERE userId = :userId")
    void updateMatchStatus(String userId, int matchStatus);

    @Query("SELECT * FROM TB_USER WHERE userId = :userId")
    LiveData<User> getUserLiveData(String userId);

    @Query("UPDATE TB_USER SET addressCode = :addressCode WHERE userId = :userId")
    void updateAddressCode(String userId, String addressCode);
}
