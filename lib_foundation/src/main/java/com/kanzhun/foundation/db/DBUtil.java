package com.kanzhun.foundation.db;

import static com.kanzhun.foundation.TestKt.isQaDebugUser;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.kanzhun.foundation.db.migration.MigrationFrom10To11;
import com.kanzhun.foundation.db.migration.MigrationFrom11To12;
import com.kanzhun.foundation.db.migration.MigrationFrom1To2;
import com.kanzhun.foundation.db.migration.MigrationFrom2To3;
import com.kanzhun.foundation.db.migration.MigrationFrom3To4;
import com.kanzhun.foundation.db.migration.MigrationFrom4To5;
import com.kanzhun.foundation.db.migration.MigrationFrom5To6;
import com.kanzhun.foundation.db.migration.MigrationFrom6To7;
import com.kanzhun.foundation.db.migration.MigrationFrom7To8;
import com.kanzhun.foundation.db.migration.MigrationFrom8To9;
import com.kanzhun.foundation.db.migration.MigrationFrom9To10;
import com.tencent.wcdb.database.SQLiteCipherSpec;
import com.tencent.wcdb.room.db.WCDBOpenHelperFactory;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/1/28
 */
public class DBUtil {
    @NonNull
    public static <T extends RoomDatabase> RoomDatabase.Builder<T> createDatabaseBuilder(
        @NonNull Context context, @NonNull Class<T> klass, @NonNull String name, String password) {
        SQLiteCipherSpec cipherSpec = new SQLiteCipherSpec()  // 指定加密方式，使用默认加密可以省略
            .setPageSize(4096)
            .setKDFIteration(64000);

        WCDBOpenHelperFactory factory = new WCDBOpenHelperFactory()
            .passphrase(password.getBytes())  // 指定加密DB密钥，非加密DB去掉此行
            .cipherSpec(cipherSpec)               // 指定加密方式，使用默认加密可以省略
            .writeAheadLoggingEnabled(true)       // 打开WAL以及读写并发，可以省略让Room决定是否要打开
            .asyncCheckpointEnabled(true);        // 打开异步Checkpoint优化，不需要可以省略

        RoomDatabase.Builder<T> builder = Room.databaseBuilder(context, klass, name)
            .allowMainThreadQueries();

        if (!isQaDebugUser()) {
            builder.openHelperFactory(factory); // 重要：使用WCDB打开Room
        }

        builder.setTransactionExecutor(new ThreadPoolExecutor(0, Integer.MAX_VALUE,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>()) {
            @Override
            public void execute(Runnable command) {
                super.execute(new RunWrapper(command));
            }
        });

        builder.setQueryExecutor(new ThreadPoolExecutor(0, Integer.MAX_VALUE,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>()) {
            @Override
            public void execute(Runnable command) {
                super.execute(new RunWrapper(command));
            }
        });

        // 数据库升级
        builder.addMigrations(new MigrationFrom1To2());
        builder.addMigrations(new MigrationFrom2To3());
        builder.addMigrations(new MigrationFrom3To4());
        builder.addMigrations(new MigrationFrom4To5());
        builder.addMigrations(new MigrationFrom5To6());
        builder.addMigrations(new MigrationFrom6To7());
        builder.addMigrations(new MigrationFrom7To8(context));
        builder.addMigrations(new MigrationFrom8To9(context));
        builder.addMigrations(new MigrationFrom9To10(context));
        builder.addMigrations(new MigrationFrom10To11(context));
        builder.addMigrations(new MigrationFrom11To12(context));
        return builder;
    }

    private static class RunWrapper implements Runnable {
        private final Runnable mRunnable;

        public RunWrapper(Runnable runnable) {
            this.mRunnable = runnable;
        }

        @Override
        public void run() {
            try {
                mRunnable.run();
            } catch (Exception e) {
                //noinspection CallToPrintStackTrace
                e.printStackTrace();
            }
        }
    }
}
