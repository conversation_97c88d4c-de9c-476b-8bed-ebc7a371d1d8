package com.kanzhun.foundation.db.migration

import android.content.Context
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.db.DBConstants
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.sp.SpManager


class MigrationFrom1To2 : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE user ADD COLUMN birthdayStr TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_MESSAGE} ADD COLUMN `deleted` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom2To3 : Migration(2, 3) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `icon` TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `relationStatus` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom3To4 : Migration(3,  4) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `systemId` INTEGER NOT NULL DEFAULT 0")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `iconShowType` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom4To5 : Migration(4,  5) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `moodIcon` TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `moodTitle` TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `moodIcon` TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `moodTitle` TEXT")
    }
}

class MigrationFrom5To6 : Migration(5,  6) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `securityId` TEXT")
        SpManager.get().user().edit().putLong(Constants.CONTACT_UPDATE_TIMES, 0).apply()
    }
}


class MigrationFrom6To7 : Migration(6,  7) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_USER} ADD COLUMN `schoolTime` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom7To8(val context:Context) : Migration(7,  8) {
    override fun migrate(database: SupportSQLiteDatabase) {
        ServiceManager.clearCookies(context)
        SpManager.get().user().edit().putLong(Constants.CONTACT_UPDATE_TIMES, 0).apply()
    }
}

class MigrationFrom8To9(val context:Context) : Migration(8,  9) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `jumpProto` TEXT")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `showFilmRedPoint` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom9To10(val context:Context) : Migration(9,  10) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `modeType` INTEGER NOT NULL DEFAULT 1")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `modeMinSeq` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom10To11(val context:Context) : Migration(10,  11) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONTACT} ADD COLUMN `protectMeetStatus` INTEGER NOT NULL DEFAULT 0")
    }
}

class MigrationFrom11To12(val context:Context) : Migration(11,  12) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `sort` INTEGER NOT NULL DEFAULT 0")
        database.execSQL("ALTER TABLE ${DBConstants.TAB_CONVERSATION} ADD COLUMN `shield` INTEGER NOT NULL DEFAULT 0")
    }
}

