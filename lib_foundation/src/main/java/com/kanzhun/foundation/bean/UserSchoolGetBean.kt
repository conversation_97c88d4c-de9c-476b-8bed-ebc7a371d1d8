package com.kanzhun.foundation.bean

import java.io.Serializable

data class UserSchoolGetBean(
    val certStatus: Int,// 表单审核状态 1-审核中，2-已驳回，3-已通过
    val eduCertStatus: Int,// 学历认证状态，
    val degree: Int,
    val rejectCode: Int,
    val rejectReason: String?,
    val schoolName: String?,
    val schoolArea:Int,//0-不定，1-国内，2-海外
    val eduCertType:Int,// 10 学信网在线验证码 20 认证方式：毕业证/学位证编号 30 认证方式：毕业证/学位证照片 40 认证方式：教留服证书编号  可空
    val eduCertInfo:String?,// 2 驳回时返回
    val eduCertImgUrl:String?,// 认证图片地址
    val schoolTime:Int,//1：全日制 2:非全日制

    val eduTicketId:String?,//认证工单id
    val certAdviceStatus:Int,//是否下发建议 1:下发 其他的不下发

    /*

        https://api.weizhipin.com/project/1975/interface/api/677633

            FALLBACK(-1, "", "你的学历认证信息未通过审核，请修改后重新提交。", ""),
            OTHER(1,"其他", "你的学历认证信息未通过审核。","。"), // 其他
            NOT_SELF(2,"学历信息不是本人","你的学历认证信息未通过审核，请提供本人的学历信息。","，请提供本人的学历信息。"), // 学历信息不是本人
            SCHOOL_LEVEL_NOT_MATCH(3,"学校不符","你的学历认证信息未通过审核，认证学校与填写学校不一致，请修改后重新提交。",
                    "，认证学校与填写学校不一致，请修改后重新提交。"), // 学校与认证信息不符
            LEVEL_NOT_MATCH(4, "学历等级不符", "你的学历认证信息未通过审核，认证的学历等级与填写等级不一致，请修改后重新提交。",
                    "，认证的学历等级与填写等级不一致，请修改后重新提交。"), // 本科及以上与认证信息不符
            CODE_ERROR(7, "在线验证码错误", "你提供的学信网在线验证码不正确，无法验证学历。请核实验证码后重新提交。",
                    "不正确，无法验证学历。请核实验证码后重新提交。"),//在线验证码错误
            CODE_EXPIRE(8, "在线验证码过期", "你提供的学信网在线验证码已过期，无法验证学历。请登录学信网，延长验证码有效期后重新提交。",
                    "已过期，无法验证学历。请登录学信网，延长验证码有效期后重新提交。"),//在线验证码过期
            NUMBER_ERROR(9, "证书编号错误", "你提供的学历证件编号错误，无法验证学历。请核实编号后重新提交。",
                    "编号错误，无法验证学历。请核实编号后重新提交。"),//学历证件编号
            PHOTO_VAGUE(10, "照片模糊", "你提供的学历证件照片模糊，无法验证学历。请确保拍摄的照片清晰。",
                    "照片模糊，无法验证学历。请确保拍摄的照片清晰。"),//照片模糊
            PHOTO_LACK(11, "照片拍摄不全", "你拍摄的学历证件信息不全，请确保证书编号拍摄完整、清晰。", ""),//照片拍摄不全
            PHOTO_NOT_DIPLOMA(12, "照片不是学历证件", "你提供的照片未显示学历及编号信息，无法验证学历，请重新提交。", ""),//照片不是学历证件
            OVERSEAS_NUMBER_ERROR(13, "证书编号错误", "你提供的教留服证书编号错误，无法验证学历。请核实编号后重新提交。", ""),//留教服证书编号错误
            SCHOOL_NAME_ERROR(14, "", "学校名称不太合适，请修改后重新提交哦～", "。"), // 因为待审核池中学历信息审核不通过，导致的学历认证失败

            EDU_RESET(15, "", "经用户举报及平台核实，你的学历认证违规，相关信息已被驳回，请重新提交", "。"), // 学历重置
            EDU_INFO_RESET(16, "", "经用户举报及平台核实，你的学历信息违规，相关信息已被驳回，请重新提交", "。"), // 因学历信息引起的认证重置
            SCHOOL_TIME_ERROR(17, "学制信息不符", "你的学历认证信息未通过审核，认证的学制信息（是否为全日制）与填写信息不一致，请修改后重新提交。", ""),
            MATERIAL_NOT_MATCH(18, "材料图片不符", "你的学历认证信息未通过审核，上传的图片错误无法认证，请修改后重新提交。", ""),

            @Deprecated
            LEVEL_LOW(5, "", "你的学历认证信息未通过审核，认证的学历等级与填写等级不一致，请修改后重新提交。", ""), // 本科以下与认证信息不符
            @Deprecated
            NOT_FOUND(6, "", "你的学历认证信息未通过审核，请重新上传。", ""),//官网未查到

     */
    val certRejectCode:Int,//下发类型:  3:学校  4:学历 17:学制信息 18-材料图片不符
    val certAdviceSchoolName:String?,//建议学校名称
    val certAdviceDegree:Int,//建议学历
    val certAdviceSchoolTime:Int,//建议学制
):Serializable