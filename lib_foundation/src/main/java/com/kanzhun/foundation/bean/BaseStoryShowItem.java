package com.kanzhun.foundation.bean;

import android.os.Handler;
import android.os.Looper;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableInt;


/**
 * <AUTHOR>
 * @date 2022/4/20.
 */
public class BaseStoryShowItem extends BaseStoryItem {
    private ObservableInt upLoadPercent = new ObservableInt(0);
    private ObservableInt downLoadPercent = new ObservableInt(0);
    private ObservableBoolean downLoadFailed = new ObservableBoolean(false);
    private ObservableBoolean addFailed = new ObservableBoolean(false);//true: 添加失败；false：添加成功
    private ObservableBoolean uploaded = new ObservableBoolean(false);//上传成功的状态，仅用于首次上传，重新上传，成功的监听
    private String url;//服务端路径
    private String tinyUrl;//服务端压缩路径
    private String token;
    private ObservableInt certStatus = new ObservableInt(0);// 审核状态，0-初始化,1-审核中,2-已驳回,3-通过  空时作为通过处理，初始化作为审核中处理
    private String certInfo;
    private int textReject;
    public int photoReject;//照片故事图片驳回 1:正常 2:驳回
    public int profileType;//个人页字段类型 321 自我介绍  322 兴趣爱好  323 家庭介绍

    public ObservableInt getUpLoadPercent() {
        return upLoadPercent;
    }

    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    public void setUpLoadPercent(int upLoadPercent) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                BaseStoryShowItem.this.upLoadPercent.set(upLoadPercent);
            }
        });
    }

    public ObservableInt getDownLoadPercent() {
        return downLoadPercent;
    }

    public void setDownLoadPercent(int downLoadPercent) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                BaseStoryShowItem.this.downLoadPercent.set(downLoadPercent);
            }
        });
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTinyUrl() {
        return tinyUrl;
    }

    public void setTinyUrl(String tinyUrl) {
        this.tinyUrl = tinyUrl;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public ObservableBoolean getAddFailed() {
        return addFailed;
    }

    public void setAddFailed(boolean addFailed) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                BaseStoryShowItem.this.addFailed.set(addFailed);
            }
        });
    }

    public ObservableBoolean getDownLoadFailed() {
        return downLoadFailed;
    }

    public void setDownLoadFailed(boolean downLoadFailed) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                BaseStoryShowItem.this.downLoadFailed.set(downLoadFailed);
            }
        });
    }

    public void setCertStatus(int certStatus) {
        this.certStatus.set(certStatus);
    }

    public ObservableInt getCertStatus() {
        return certStatus;
    }

    public String getCertInfo() {
        return certInfo;
    }

    public void setCertInfo(String certInfo) {
        this.certInfo = certInfo;
    }

    public int getTextReject() {
        return textReject;
    }

    public void setTextReject(int textReject) {
        this.textReject = textReject;
    }

    public int getPhotoReject() {
        return photoReject;
    }

    public void setPhotoReject(int photoReject) {
        this.photoReject = photoReject;
    }


    public int getProfileType() {
        return profileType;
    }

    public void setProfileType(int profileType) {
        this.profileType = profileType;
    }

    public ObservableBoolean getUploaded() {
        return uploaded;
    }

    public void setUploaded(boolean uploaded) {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                BaseStoryShowItem.this.uploaded.set(uploaded);
            }
        });
    }
}
