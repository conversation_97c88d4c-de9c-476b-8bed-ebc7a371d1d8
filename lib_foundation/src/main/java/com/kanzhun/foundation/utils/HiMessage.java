package com.kanzhun.foundation.utils;

import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;
import com.kanzhun.foundation.model.message.MessageForTime;

import java.util.LinkedList;
import java.util.List;

public final class HiMessage {
    private List<ChatMessage> mOriginMessages;
    private List<ChatMessage> mChatMessages;
    private boolean hasMore = true;
    private boolean isLatest = false;

    public HiMessage(List<ChatMessage> originMessages, boolean hasMore, boolean isLatest) {
        setChatMessages(originMessages);
        this.hasMore = hasMore;
        this.isLatest = isLatest;
    }

    public HiMessage(List<ChatMessage> originMessages) {
        setChatMessages(originMessages);
    }

    public void setChatMessages(List<ChatMessage> chatMessages) {
        mChatMessages = addMessageTime(chatMessages);
        mOriginMessages = chatMessages;
    }

    List<ChatMessage> getOriginMessages() {
        return mOriginMessages;
    }

    public void setOriginMessages(List<ChatMessage> originMessages) {
        mOriginMessages = originMessages;
    }

    public List<ChatMessage> getChatMessages() {
        return mChatMessages;
    }

    public boolean hasMore() {
        return hasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }

    public boolean isLatest() {
        return isLatest;
    }

    public void setLatest(boolean latest) {
        isLatest = latest;
    }

    private List<ChatMessage> addMessageTime(List<ChatMessage> chatMessages) {
        ChatMessage previous = null;
        LinkedList<ChatMessage> chatMessageList = new LinkedList<>();
        if (!chatMessages.isEmpty()) {
            synchronized (chatMessages) {
                int startIndex = chatMessages.size() - 1;
                for (int i = startIndex; i >= 0; i--) {//倒序
                    ChatMessage chatMessage = chatMessages.get(i);
                    if (previous != null) {
                        if (previous.getTime() - chatMessage.getTime() > MessageConstants.MSG_SHOW_TIME_INTERVAL) {
                            chatMessageList.addFirst(createTimeMessage(previous));
                        }
                    }

                    previous = chatMessage;
                    chatMessageList.addFirst(chatMessage);
                }
            }
        }

        if (previous != null) {
            chatMessageList.addFirst(createTimeMessage(previous));
        }

        return chatMessageList;
    }

    private static MessageForTime createTimeMessage(ChatMessage chatMessage) {
        MessageForTime messageForTime = new MessageForTime();
        messageForTime.setMid(-chatMessage.getTime());
        messageForTime.setTime(chatMessage.getTime());
        return messageForTime;
    }
}
