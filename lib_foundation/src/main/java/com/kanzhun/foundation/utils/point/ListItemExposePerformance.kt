package com.kanzhun.foundation.utils.point

import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnScrollListener
import com.chad.library.adapter.base.BaseQuickAdapter
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.techwolf.lib.tlog.TLog

abstract class ListItemExposePerformance() : AbsPerformance() {

    /**
     * 记录每个item是否曝光过
     */
    val hasShowItems = mutableMapOf<Int, Boolean>()

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        val layoutManager = getRecyclerView().layoutManager
        if (layoutManager is LinearLayoutManager) {
            getRecyclerView().addOnScrollListener(object : OnScrollListener() {
                override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                    super.onScrollStateChanged(recyclerView, newState)
                    if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                       checkVisibleItem()
                    }
                }
            })
        }
        getRecyclerView().adapter?.registerAdapterDataObserver(object : RecyclerView.AdapterDataObserver() {
            override fun onChanged() {
                super.onChanged()
                hasShowItems.clear()
                getRecyclerView().postDelayed({ checkVisibleItem() },200)
                TLog.info("UserPreviewItemExposePerformance","onChanged")

            }
        })
    }

    private fun checkVisibleItem() {
        val layoutManager = getRecyclerView().layoutManager
        val adapter = getRecyclerView().adapter as BaseQuickAdapter<*, *>
        if (layoutManager is LinearLayoutManager) {
            val firstVisiblePosition = layoutManager.findFirstVisibleItemPosition()
            val lastVisiblePosition = layoutManager.findLastVisibleItemPosition()
            if(firstVisiblePosition >=0 && lastVisiblePosition >=0 && lastVisiblePosition>= firstVisiblePosition){
                for (i in firstVisiblePosition..lastVisiblePosition) {
                    if(i < adapter.data.size){
                        val code = adapter.getItem(i).hashCode()
                        if (hasShowItems[code] != true) {
                            hasShowItems[code] = true
                            onReportExpose(i)
                        }
                    }

                }
            }

        }
    }

    abstract fun getRecyclerView(): RecyclerView

    abstract fun onReportExpose(position: Int)
}