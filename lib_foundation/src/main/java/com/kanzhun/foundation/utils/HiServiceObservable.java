package com.kanzhun.foundation.utils;

import java.util.List;
import java.util.WeakHashMap;

import com.kanzhun.foundation.model.message.ChatMessage;
import com.kanzhun.foundation.model.message.MessageConstants;

public class HiServiceObservable {
    private static final String TAG = "HiServiceObservable";
    private WeakHashMap<String, HiMessageLiveData> obs = new WeakHashMap<>();
    private WeakHashMap<String, HiMessageLiveData> groupObs = new WeakHashMap<>();
    private WeakHashMap<String, HiMessageLiveData> replyObs = new WeakHashMap<>();

    public HiServiceObservable() {
    }

    public HiMessageLiveData getObserver(String id, int type) {
        HiMessageLiveData hiMessageLiveData = getObs(type).get(id);
        if (hiMessageLiveData == null) {
            hiMessageLiveData = new HiMessageLiveData(id);
            getObs(type).put(hiMessageLiveData.getChatId(), hiMessageLiveData);
        }
        return hiMessageLiveData;
    }

    private WeakHashMap<String, HiMessageLiveData> getObs(int type) {
        WeakHashMap<String, HiMessageLiveData> weakHashMap;
        if (type == MessageConstants.MSG_GROUP_CHAT) {
            weakHashMap = groupObs;
        } else if (type == MessageConstants.MSG_SINGLE_CHAT) {
            weakHashMap = obs;
        } else {
            weakHashMap = replyObs;
        }
        return weakHashMap;
    }

    public boolean hasKey(String id, int type) {
        return getObs(type).containsKey(id);
    }

    public void onChanged(String id, int type, HiMessage t) {
        HiMessageLiveData mutableLiveData = getValue(id, type);
        if (mutableLiveData != null) {
            mutableLiveData.postValue(t);
        }
    }

    public void onChanged(String id, int type, List<ChatMessage> t) {
        HiMessageLiveData mutableLiveData = getValue(id, type);
        if (mutableLiveData != null) {
            mutableLiveData.postValue(t);
        }
    }

    public void onChanged(String id, int type, List<ChatMessage> t, boolean isLastest, boolean hasMore) {
        HiMessageLiveData mutableLiveData = getValue(id, type);
        if (mutableLiveData != null) {
            mutableLiveData.postValue(t, isLastest, hasMore);
        }
    }

    public List<ChatMessage> getData(String id, int type) {
        HiMessageLiveData mutableLiveData = getValue(id, type);
        if (mutableLiveData != null) {
            HiMessage hiMessage = mutableLiveData.getValue();
            if (hiMessage != null) {
                return hiMessage.getOriginMessages();
            }
        }
        return null;
    }

    private HiMessageLiveData getValue(String id, int type) {
        return getObs(type).get(id);
    }

    public String getChatId(String chatId, int type) {
        HiMessageLiveData hiMessageLiveData = getValue(chatId, type);
        if (hiMessageLiveData != null) {
            return hiMessageLiveData.getChatId();
        }
        return chatId;
    }

    public void removeCache(long chatId, int type) {
        getObs(type).remove(chatId);
    }
}
