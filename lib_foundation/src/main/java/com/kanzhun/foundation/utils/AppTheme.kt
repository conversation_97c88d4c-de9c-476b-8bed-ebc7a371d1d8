package com.kanzhun.foundation.utils

import com.kanzhun.foundation.router.MainPageRouter
import com.kanzhun.foundation.sp.SpManager

object AppTheme{

    fun getTheme(): THEME {
        return when(SpManager.get().user().getInt("APP_THEME",0)){
            0 -> THEME.NORMAL
            else -> THEME.CHRISTMAS
        }
    }

    var systemChristmasId :String? = ""

    //1-展示圣诞活动皮肤，0-不展示
    var christmasShinShow = 0
        set(value) {
            field = value
            if (value == 0){
                setTheme(THEME.NORMAL)
            }
        }

    fun changeToChristmas(){
        if (canChangeToChristmas()){
            setTheme(THEME.CHRISTMAS)
        }
    }

    fun setTheme(theme: THEME) {
        when(theme){
            THEME.NORMAL -> {
                SpManager.get().user().edit().putInt("APP_THEME",0).apply()
            }
            THEME.CHRISTMAS -> {
                SpManager.get().user().edit().putInt("APP_THEME",1).apply()
            }
        }
    }

    fun isNormalTheme():Boolean{
        return christmasShinShow == 0
    }

    fun canChangeToChristmas(): Boolean {
        return christmasShinShow == 1 && getTheme() != THEME.CHRISTMAS
    }
}

enum class THEME() {
    NORMAL(),
    CHRISTMAS()
}
