package com.kanzhun.foundation.ui;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.page.VideoAudioFloatPageInterface;

public class Navigation {

    private static Info gInfo;

    public static void initialized(Info info) {
        gInfo = info;
    }

    private static Class getLoginClass() {
        return gInfo.loginClass;
    }

    private static Class getHomeClass() {
        return gInfo.homeClass;
    }

    private static Class getVideoChatClass() {
        return gInfo.videoChatClass;
    }

    public static Class getChatClass() {
        return gInfo.chatClass;
    }

    public static Class getFloatPageClass() {
        return gInfo.floatPageClass;
    }

    public static Intent getJumpIntent(Context context) {
        Intent intent = null;
        if (AccountHelper.getInstance().isLogin()) {
            intent = new Intent(context, getHomeClass());
            return intent;

        }
        if (intent == null) {
            intent = new Intent(context, getLoginClass());
        }
        return intent;
    }

    public static Intent getVideoChatActivity(Context context) {
        Intent intent = new Intent(context, getVideoChatClass());
        return intent;
    }

    public static class Info {
        Class<? extends Activity> loginClass;
        Class<? extends Activity> homeClass;
        Class<? extends Activity> chatClass;
        Class<? extends Activity> videoChatClass;
        Class<? extends VideoAudioFloatPageInterface> floatPageClass;

        public Info setLoginClass(Class<? extends Activity> loginClass) {
            this.loginClass = loginClass;
            return this;
        }

        public Info setHomeClass(Class<? extends Activity> homeClass) {
            this.homeClass = homeClass;
            return this;
        }

        public Info setChatClass(Class<? extends Activity> chatClass) {
            this.chatClass = chatClass;
            return this;
        }

        public Info setVideoChatClass(Class<? extends Activity> videoChatClass) {
            this.videoChatClass = videoChatClass;
            return this;
        }

        public Info setFloatPageClass(Class<? extends VideoAudioFloatPageInterface> floatPageClass) {
            this.floatPageClass = floatPageClass;
            return this;
        }
    }

}
