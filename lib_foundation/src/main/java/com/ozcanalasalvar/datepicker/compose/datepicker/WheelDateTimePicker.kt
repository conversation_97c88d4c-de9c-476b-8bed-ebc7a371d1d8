package com.ozcanalasalvar.datepicker.compose.datepicker

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.android.material.timepicker.TimeFormat
import com.ozcanalasalvar.datepicker.compose.component.SelectorView
import com.ozcanalasalvar.datepicker.model.Date
import com.ozcanalasalvar.datepicker.model.Time
import com.ozcanalasalvar.datepicker.utils.DateUtils
import com.ozcanalasalvar.datepicker.utils.daysOfDate
import com.ozcanalasalvar.datepicker.utils.monthsOfDate
import com.ozcanalasalvar.datepicker.utils.withDay
import com.ozcanalasalvar.datepicker.utils.withMonth
import com.ozcanalasalvar.wheelview.SelectorOptions
import com.ozcanalasalvar.wheelview.WheelView
import java.util.Locale

@Composable
fun WheelDateTimePicker(
    offset: Int = 2,
    textSize: Int = 16,

    startDate: Date = Date(DateUtils.getCurrentTime()),
    selectorEffectEnabled: Boolean = true,
    onDateChanged: (Int, Int, Int, Long) -> Unit = { _, _, _, _ -> },

    timeFormat: Int = TimeFormat.CLOCK_24H,
    startTime: Time = Time(DateUtils.getCurrentHour(), DateUtils.getCurrentMinute()),
    onTimeChanged: (Int, Int, String?) -> Unit = { _, _, _ -> },
) {

    var selectedDate by remember { mutableStateOf(startDate) }
    val months = selectedDate.monthsOfDate()
    val days = selectedDate.daysOfDate()

    var selectedTime by remember { mutableStateOf(startTime) }
    val hours = mutableListOf<Int>().apply {
        for (hour in 0..if (timeFormat == TimeFormat.CLOCK_24H) 23 else 12) {
            add(hour)
        }
    }
    val minutes = mutableListOf<Int>().apply {
        for (minute in 0..59) {
            add(minute)
        }
    }

    LaunchedEffect(selectedDate) {
        onDateChanged(selectedDate.day, selectedDate.month, selectedDate.year, selectedDate.date)
    }

    LaunchedEffect(selectedTime) {
        onTimeChanged(selectedTime.hour, selectedTime.minute, selectedTime.format)
    }

    val fontSize = maxOf(13, minOf(19, textSize))

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Max),
        contentAlignment = Alignment.Center
    ) {
        val height = (fontSize + 11).dp
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                selection = maxOf(months.indexOf(selectedDate.month), 0),
                itemCount = months.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = selectorEffectEnabled,
                    enabled = false
                ),
                onFocusItem = {
                    selectedDate = selectedDate.withMonth(months[it])
                },
                content = {
                    Text(
                        text = String.format(Locale.getDefault(), "%02d", months[it] + 1),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth(),
                        fontSize = fontSize.sp,
                        color = Color(0xFF191919)
                    )
                })

            Text(
                text = "月",
                style = TextStyle(
                    fontSize = 15.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                )
            )

            key(days.size) {
                WheelView(
                    modifier = Modifier.weight(1f),
                    itemSize = DpSize(30.dp, height),
                    selection = maxOf(days.indexOf(selectedDate.day), 0),
                    itemCount = days.size,
                    rowOffset = offset,
                    selectorOption = SelectorOptions().copy(
                        selectEffectEnabled = selectorEffectEnabled,
                        enabled = false
                    ),
                    onFocusItem = {
                        selectedDate = selectedDate.withDay(days[it])
                    },
                    content = {
                        Text(
                            text = days[it].toString(),
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(),
                            fontSize = fontSize.sp,
                            color = Color(0xFF191919)
                        )
                    })
            }

            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                selection = 0,
                itemCount = hours.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = selectorEffectEnabled,
                    enabled = false
                ),
                onFocusItem = {
                    selectedTime = selectedTime.copy(hour = hours[it])
                },
                content = {
                    Text(
                        text = if (hours[it] < 10) "0${hours[it]}" else "${hours[it]}",
                        textAlign = TextAlign.Center,
                        modifier = Modifier.width(50.dp),
                        fontSize = fontSize.sp,
                        color = Color(0xFF191919)
                    )
                })

            Text(
                text = ":",
                style = TextStyle(
                    fontSize = 15.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF191919),
                    textAlign = TextAlign.Center,
                )
            )

            WheelView(
                modifier = Modifier.weight(1f),
                itemSize = DpSize(30.dp, height),
                selection = 0,
                itemCount = minutes.size,
                rowOffset = offset,
                selectorOption = SelectorOptions().copy(
                    selectEffectEnabled = selectorEffectEnabled,
                    enabled = false
                ),
                onFocusItem = {
                    selectedTime = selectedTime.copy(minute = minutes[it])
                },
                content = {
                    Text(
                        text = if (minutes[it] < 10) "0${minutes[it]}" else "${minutes[it]}",
                        textAlign = TextAlign.Center,
                        modifier = Modifier.width(100.dp),
                        fontSize = fontSize.sp,
                        color = Color(0xFF191919)
                    )
                })

        }

        SelectorView(darkModeEnabled = false, offset = offset)
    }
}

@Preview(showBackground = true)
@Composable
fun DateTimePickerPreview() {
    WheelDateTimePicker()
}