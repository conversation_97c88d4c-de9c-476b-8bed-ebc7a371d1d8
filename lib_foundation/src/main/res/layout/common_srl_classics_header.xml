<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:paddingTop="20dp"
    tools:paddingBottom="20dp"
    tools:background="#eee"
    tools:parentTag="android.widget.RelativeLayout">

    <ImageView
        android:id="@+id/srl_classics_arrow"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_centerInParent="true"
        android:contentDescription="@android:string/untitled"
        />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/srl_classics_progress"
        android:layout_width="30dp"
        android:layout_height="30dp"
        app:lottie_loop="true"
        app:lottie_imageAssetsFolder="default_refresh_loading/images"
        app:lottie_fileName="default_refresh_loading/drop.json"
        android:layout_centerInParent="true"
        android:contentDescription="@android:string/untitled"
        app:common_src="@drawable/common_refresh_loading"
        />

</merge>