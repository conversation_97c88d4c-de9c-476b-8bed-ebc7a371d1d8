<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="MissingDefaultResource">

    <com.qmuiteam.qmui.layout.QMUIConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@color/common_color_191919_70"
        android:maxWidth="245dp"
        android:paddingLeft="10dp"
        android:paddingTop="12dp"
        android:paddingRight="10dp"
        app:qmui_radius="12dp"
        android:paddingBottom="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        >

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_white"
            android:textSize="14dp"
            android:ellipsize="end"
            android:maxLines="2"
            tools:text="添加兴趣爱好" />

    </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
