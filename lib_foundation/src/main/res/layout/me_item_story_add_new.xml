<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.views.UniformHWConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_margin="2.5dp"
    android:background="@drawable/me_ic_story_pic_add_bg">

    <ImageView
        android:id="@+id/ivAdd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="center"
        android:src="@drawable/me_ic_story_add_pic"
        app:layout_constraintBottom_toTopOf="@+id/tvAdd"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvIcon"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="2dp"
        android:layout_marginBottom="4dp"
        android:layout_marginRight="2dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/common_color_797979"
        android:textSize="@dimen/common_text_sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvAdd"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="模糊文案"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvAdd"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="2dp"
        android:layout_marginTop="2dp"
        android:layout_marginRight="2dp"
        android:ellipsize="end"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/common_color_797979"
        android:textSize="@dimen/common_text_sp_12"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivAdd"
        tools:text="模糊文案"
        tools:visibility="visible" />

    <ImageView
        android:id="@+id/idLogoAdd"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintTop_toBottomOf="@+id/tvAdd"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tvAdd"
        android:src="@drawable/me_ic_story_add_logo_icon"
        android:layout_width="34dp"
        android:layout_height="25dp"/>


</com.kanzhun.common.views.UniformHWConstraintLayout>
