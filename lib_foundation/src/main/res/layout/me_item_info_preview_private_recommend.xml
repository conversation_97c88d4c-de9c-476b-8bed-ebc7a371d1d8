<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_marginEnd="6dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:textColor="@color/common_color_4D4D4D"
            android:textSize="@dimen/common_text_sp_12"
            app:qmui_backgroundColor="@color/common_color_F4F4F6"
            app:qmui_radius="18dp"
            android:text="@{item}"
            android:singleLine="true"
            tools:text="生活在北京，来自新疆备份来自新疆备份来自新疆备份来自新疆备份来自新疆备份来自新疆备份来自新疆备份来自新疆备份来自新疆备份"/>

    </FrameLayout>

    <data>
        <variable
            name="item"
            type="String" />
    </data>
</layout>