<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="tips"
            type="com.kanzhun.foundation.model.OrangeObservableString" />
    </data>

    <LinearLayout
        android:id="@+id/fl_report_tip"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/common_bg_conor_12_color_ffd2d9"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginTop="2dp"
            android:src="@drawable/foundation_ic_error_notice"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tv_report_tip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="12dp"
            android:layout_weight="1"
            android:drawablePadding="6dp"
            android:gravity="start"
            android:text="@{tips.stringContent}"
            android:textColor="@color/common_color_292929"
            android:textSize="@dimen/common_text_sp_14"
            tools:ignore="SpUsage"
            tools:text="该用户资料违规，正在修改。该用户资料违规，正在修改。" />

    </LinearLayout>

</layout>