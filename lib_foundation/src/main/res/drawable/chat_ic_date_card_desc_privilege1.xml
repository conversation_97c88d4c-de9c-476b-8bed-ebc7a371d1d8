<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="40"
    android:viewportHeight="40">
  <path
      android:pathData="M20,20m-20,0a20,20 0,1 1,40 0a20,20 0,1 1,-40 0"
      android:strokeWidth="1"
      android:fillType="evenOdd"
      android:strokeColor="#00000000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="40"
          android:startY="7.248"
          android:endX="-0"
          android:endY="40"
          android:type="linear">
        <item android:offset="0" android:color="#FFFFE3E2"/>
        <item android:offset="1" android:color="#FFFDFDEB"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M23.667,24.889C22.619,25.66 21.353,26.111 19.989,26.111C18.635,26.111 17.376,25.666 16.333,24.905"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#191919"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20,13.889l0,2.452l-2.444,2.081l2.444,1.381l0,1.42"
      android:strokeLineJoin="round"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#191919"
      android:fillType="evenOdd"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20,20m-11,0a11,11 0,1 1,22 0a11,11 0,1 1,-22 0"
      android:strokeWidth="2"
      android:fillColor="#00000000"
      android:strokeColor="#191919"
      android:fillType="evenOdd"/>
</vector>
