<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/idStateLayout"
    android:background="@color/common_white">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <fragment
            android:id="@+id/fragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="match_parent"
            app:defaultNavHost="true"
            app:navGraph="@navigation/new_user_task_navigation"
            android:layout_height="match_parent"
            />

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </FrameLayout>

</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>
