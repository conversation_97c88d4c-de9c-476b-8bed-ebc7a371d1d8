<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idStateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/tv_phone_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_bottom_padding="14dp"
            app:title_icon="@drawable/login_ic_icon_activate_label"
            app:title_text="我的个性标签"
            app:title_text_sub="请选择3个以上的标签，帮你更快找到匹配的人" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/idNestedScrollView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:orientation="vertical"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@+id/idFragmentLoginKeySelect"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_title">

            <com.kanzhun.marry.module_new_task.fragment.abtest.interest.old.NewTaskFlowLayoutOld
                android:id="@+id/idFlowLayout"
                android:layout_width="match_parent"
                android:layout_height="0dp" />

        </androidx.core.widget.NestedScrollView>


        <fragment
            android:id="@+id/idFragmentLoginKeySelect"
            android:name="com.kanzhun.marry.module_new_task.fragment.abtest.interest.old.NewTaskLabelViewPagerSelectFragment"
            android:layout_width="match_parent"
            android:layout_height="310dp"
            android:layout_marginBottom="16dp"
            android:tag="idFragmentLoginKeySelect"
            app:layout_constraintBottom_toTopOf="@+id/btn_next" />


        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/button_large_next_page_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="下一步（0/30）"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


