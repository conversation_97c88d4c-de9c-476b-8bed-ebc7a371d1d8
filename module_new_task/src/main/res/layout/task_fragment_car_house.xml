<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/idStateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/idTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_icon="@drawable/me_ic_house_car_page"
            app:title_text="@string/me_house_car_question_title" />


        <TextView
            android:id="@+id/tv_has_house"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            app:layout_constraintTop_toBottomOf="@+id/idTitle"
            android:text="房产情况"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/fl_house"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            android:layout_marginTop="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_has_house"
            >

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/idBtnHasHouse"
                android:text="有房"
                android:textColor="@color/common_color_292929"
                android:layout_marginRight="5dp"
                app:layout_constraintRight_toRightOf="@id/guideline"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                android:gravity="center"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:layout_width="0dp"
                android:layout_height="46dp"/>


            <TextView
                android:id="@+id/idBtnNoHouse"
                android:text="无房"
                android:textColor="@color/common_color_292929"
                android:layout_marginLeft="5dp"
                android:textStyle="bold"
                android:gravity="center"
                app:layout_constraintLeft_toLeftOf="@+id/guideline"
                app:layout_constraintRight_toRightOf="parent"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:layout_width="0dp"
                android:layout_height="46dp"/>

        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <TextView
            android:id="@+id/tv_has_car"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginTop="40dp"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textStyle="bold"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            app:layout_constraintTop_toBottomOf="@+id/fl_house"
            android:text="车产情况"/>

        <com.qmuiteam.qmui.layout.QMUIConstraintLayout
            android:id="@+id/fl_car"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:layout_marginLeft="@dimen/app_layout_page_left_padding"
            android:layout_marginRight="@dimen/app_layout_page_right_padding"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_has_car"
            >

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline2"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/idBtnHasCar"
                android:text="有车"
                android:textColor="@color/common_color_292929"
                android:layout_marginRight="5dp"
                app:qmui_radius="23dp"
                android:minHeight="46dp"
                app:layout_constraintRight_toRightOf="@+id/guideline2"
                android:layout_gravity="center"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                android:gravity="center"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:layout_width="0dp"
                android:layout_height="46dp"/>

            <TextView
                android:id="@+id/idBtnNoCar"
                android:text="无车"
                android:textColor="@color/common_color_292929"
                app:qmui_radius="23dp"
                android:minHeight="46dp"
                android:layout_gravity="center"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginLeft="5dp"
                app:layout_constraintLeft_toLeftOf="@+id/guideline2"
                app:layout_constraintRight_toRightOf="parent"
                android:background="@drawable/common_bg_corner_23_color_f5f5f5"
                android:layout_width="0dp"
                android:layout_height="46dp"/>
        </com.qmuiteam.qmui.layout.QMUIConstraintLayout>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/button_large_next_page_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:enabled="false"
            android:layout_marginBottom="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


