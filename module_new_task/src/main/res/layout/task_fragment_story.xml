<?xml version="1.0" encoding="utf-8"?>
<com.kanzhun.common.kotlin.ui.statelayout.StateLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/idStateLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/idTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_icon="@drawable/common_ic_icon_avatar_page"
            app:title_text="分享我的生活"
            app:title_text_sub="@string/me_drag_assist" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_story"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingLeft="14dp"
            android:paddingRight="14dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/idTitle" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clSuggest"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:paddingLeft="18dp"
            android:paddingRight="18dp"
            android:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/rv_story"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/ivTip"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:src="@drawable/ic_new_task_tip"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSuggestText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="4dp"
                android:text="清晰丰富的照片类型更容易获得关注。"
                android:textColor="@color/common_color_707070"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintLeft_toRightOf="@+id/ivTip"
                app:layout_constraintRight_toLeftOf="@+id/tvSuggestSwitch"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvSuggestSwitch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:text="隐藏提示"
                android:textColor="@color/common_color_191919"
                android:textSize="@dimen/common_text_sp_12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/tvSuggestText"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!--        <androidx.constraintlayout.widget.ConstraintLayout-->
        <!--            android:id="@+id/clDragAssist"-->
        <!--            android:layout_width="0dp"-->
        <!--            android:layout_height="wrap_content"-->
        <!--            android:layout_marginTop="17dp"-->
        <!--            android:paddingLeft="18dp"-->
        <!--            android:paddingRight="18dp"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintLeft_toLeftOf="parent"-->
        <!--            app:layout_constraintRight_toRightOf="parent"-->
        <!--            app:layout_constraintTop_toBottomOf="@+id/clSuggest"-->
        <!--            tools:visibility="visible">-->

        <!--            <ImageView-->
        <!--                android:id="@+id/ivDragTip"-->
        <!--                android:layout_width="18dp"-->
        <!--                android:layout_height="18dp"-->
        <!--                android:src="@drawable/ic_new_task_tip"-->
        <!--                app:layout_constraintBottom_toBottomOf="parent"-->
        <!--                app:layout_constraintLeft_toLeftOf="parent"-->
        <!--                app:layout_constraintTop_toTopOf="parent" />-->

        <!--            <TextView-->
        <!--                android:id="@+id/tv_drag_assist"-->
        <!--                android:layout_width="wrap_content"-->
        <!--                android:layout_height="wrap_content"-->
        <!--                android:layout_marginLeft="4dp"-->
        <!--                android:text="@string/me_drag_assist"-->
        <!--                android:textColor="@color/common_color_707070"-->
        <!--                android:textSize="@dimen/common_text_sp_12"-->
        <!--                app:layout_constraintBottom_toBottomOf="parent"-->
        <!--                app:layout_constraintLeft_toRightOf="@+id/ivDragTip"-->
        <!--                app:layout_constraintTop_toTopOf="parent" />-->

        <!--        </androidx.constraintlayout.widget.ConstraintLayout>-->


        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            style="@style/button_large_next_page_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="28dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</com.kanzhun.common.kotlin.ui.statelayout.StateLayout>


