package com.kanzhun.marry.module_new_task.fragment.abtest.interest

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf

data class Bean(
    var id: Int? = 0,
    var icon: String? = "",
    var content: String? = "",
    var title: String? = "",
    var isOpen: MutableState<Boolean> = mutableStateOf(true), // 最外层默认展开
    var isShowMore: MutableState<Boolean> = mutableStateOf(false),
    var isSelected: MutableState<Boolean> = mutableStateOf(false),
    var subTag: List<Bean>? = emptyList<Bean>(),
    var isLocalSelect: Boolean = false
)