package com.kanzhun.marry.module_new_task.fragment

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.os.Bundle
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.kanzhun.common.app.AppThreadFactory
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.decoration.VerticalDividerItemDecoration
import com.kanzhun.common.dialog.BottomListDialogNew
import com.kanzhun.common.dialog.model.SelectBottomBean
import com.kanzhun.common.kotlin.ext.enable
import com.kanzhun.common.kotlin.ext.gone
import com.kanzhun.common.kotlin.ext.liveEventObserve
import com.kanzhun.common.kotlin.ext.toResourceColor
import com.kanzhun.common.kotlin.ext.visible
import com.kanzhun.common.kotlin.ui.onClick
import com.kanzhun.foundation.Constants
import com.kanzhun.foundation.adapter.MultiTypeBindingAdapter
import com.kanzhun.foundation.adapter.buildMultiTypeAdapterByType
import com.kanzhun.foundation.adapter.replaceData
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.CompanyNameBean
import com.kanzhun.foundation.api.model.SuggestBean
import com.kanzhun.foundation.api.model.TempSuggestBean
import com.kanzhun.foundation.kotlin.ktx.contentShowRange
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.foundation.utils.point.reportPoint
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.SimpleRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.dialog.showCompanyRangeBottomDialog
import com.kanzhun.marry.me.point.MePointAction
import com.kanzhun.marry.module_new_task.R
import com.kanzhun.marry.module_new_task.databinding.TaskFragmentCompanyNameBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemAuthSeachCompanyBinding
import com.kanzhun.marry.module_new_task.databinding.TaskItemAuthSeachCompanyHelpBinding
import com.kanzhun.marry.module_new_task.point.NewTaskPointAction
import com.kanzhun.marry.module_new_task.viewmodel.NewUserTaskNewViewModel
import com.kanzhun.utils.SettingBuilder
import com.kanzhun.utils.T
import com.kanzhun.utils.base.LList
import com.kanzhun.utils.configuration.UserSettingConfig
import com.qmuiteam.qmui.util.QMUIDisplayHelper
import com.qmuiteam.qmui.util.QMUIKeyboardHelper
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers
import io.reactivex.rxjava3.disposables.Disposable
import io.reactivex.rxjava3.schedulers.Schedulers
import io.reactivex.rxjava3.subjects.PublishSubject
import java.util.concurrent.TimeUnit

class NewTaskCompanyNameFragment : NewTaskBaseFragment<TaskFragmentCompanyNameBinding>() {
    override fun preInit(arguments: Bundle) {
    }

    var temWriteName = ""

    override fun initView() {
        mBinding.btnNext.onClick {
            getStateLayout().showLoading()
            save()
            reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_NEXT_CLICK) {
                step = mBinding.idTitle.getTitle()
                source = activityViewModel.getPageSourceStr()
            }
        }
        showFragmentContent()
        activityViewModel.searchCompanyLiveData.observe(
            this
        ) { data ->
            adapter?.replaceData(data)
            mBinding.recyclerView.visible()
        }

        liveEventObserve("companyNameEdit"){it:String->
            if (TextUtils.isEmpty(it))return@liveEventObserve
            AppThreadFactory.getMainHandler().postDelayed({
                temWriteName = it
                mBinding.editText.setText(it)
                mBinding.editText.setSelection(mBinding.editText.length())
                mBinding.editText.enable(true)

            },200)

        }

        mBinding.idTitle.setTaskProgress(childFragmentManager,this::class.java.simpleName)
    }

    private fun save() {
        activityViewModel.updateOccupation(
            mBinding.editText.getText().toString().trim(),
            successRunnable = {
                getStateLayout().showContent()
                gotoNext()
            },
            object : NewUserTaskNewViewModel.Callback {
                override fun error(error: String) {
                    getStateLayout().showContent()
                    setErrorText(error)
                }
            })
    }

    fun setErrorText(string: String) {
        if (string.isEmpty()) {
            mBinding.tvErrorDesc.gone()
        } else {
            mBinding.tvErrorDesc.visible()
        }
        mBinding.tvErrorDesc.text = string
    }

    var adapter: MultiTypeBindingAdapter<CompanyNameBean, ViewBinding>? = null
    var search: Boolean = false

    @SuppressLint("RestrictedApi")
    private fun showFragmentContent() {
        search = false
        initSearchSubject()
        activityViewModel.allPage.observe(this) {
            mBinding.idTitle.setAllStep("/" + activityViewModel.allPage.value)
            mBinding.idTitle.setNowStep(
                "${
                    activityViewModel.mNavController?.currentBackStack?.value?.size?.minus(
                        1
                    )
                }"
            )
        }
        if (activityViewModel.initIndustryName == "在校学生" || activityViewModel.initIndustryName == "自由职业") {
            mBinding.idTitle.setRightSkip {
                save()
                reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_SKIP) {
                    step = mBinding.idTitle.getTitle()
                    source = activityViewModel.getPageSourceStr()
                }
            }
        }
        QMUIKeyboardHelper.setVisibilityEventListener(activity,
            QMUIKeyboardHelper.KeyboardVisibilityEventListener { isOpen, heightDiff ->
                if (activity != null && host != null && activity?.isDestroyed == false && this.isResumed) {
                    mBinding.idTitle.setInputVisibility(isOpen)
                }
                false
            })

        val content0 = "请填写"
        val content1 = "完整的公司或单位全称"
        val content2 = "，不规范的名称将会被审核驳回哦"
        val indexSpannableString = SpannableString(content0+content1+content2);
        indexSpannableString.setSpan(ForegroundColorSpan(ContextCompat.getColor(requireContext(), R.color.common_color_0046BD)), content0.length, (content0+content1).length, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
        mBinding.idTitle.setSubTitle(indexSpannableString)

        mBinding.viewLine.setBackgroundColor(R.color.common_color_191919.toResourceColor())
        mBinding.editText.setText(activityViewModel.initCompanyName)
        mBinding.editText.setSelection(activityViewModel.initCompanyName.length)
        checkEnable(mBinding.editText.text.toString())
        mBinding.recyclerView.layoutManager = LinearLayoutManager(context)
        val itemDecoration = VerticalDividerItemDecoration(QMUIDisplayHelper.dpToPx(1))
        itemDecoration.dividerColor =
            ContextCompat.getColor(requireContext(), R.color.common_color_EBEBEB)
        mBinding.recyclerView.addItemDecoration(itemDecoration)
        adapter = buildMultiTypeAdapterByType {
            layout(TaskItemAuthSeachCompanyBinding::inflate) { position:Int, bean: SuggestBean ->
                if (!LList.isEmpty<SuggestBean.SuggestLightBean>(bean.highlights)) {
                    val s = SpannableString(bean.name)
                    for (light in bean.highlights!!) {
                        val start = light!!.start
                        val end = light.end
                        if (s.length >= start && end <= s.length && start <= end) {
                            s.setSpan(
                                StyleSpan(Typeface.BOLD),
                                start,
                                end,
                                Spannable.SPAN_INCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                    binding.tvContent.text = s
                } else {
                    binding.tvContent.text = bean.name
                }
                binding.root.onClick {
                    search = false
                    mBinding.editText.setText(bean.name)
                    mBinding.editText.setSelection(bean.name?.length ?: 0)
                    temWriteName = bean.name?:""
                    mBinding.recyclerView.gone()
                    search = true
                    mBinding.btnNext.enable(true)
                    reportPoint(MePointAction.COMPANY_NAMEFILL_SUGGESTION_CLICK){
                        source = "新手阶段"
                        idx = position
                    }
                }
            }
            layout(TaskItemAuthSeachCompanyHelpBinding::inflate) { _, bean: TempSuggestBean ->
                binding.root.onClick {
                    MePageRouter.jumpToCompanyNameCustomActivity(requireContext(),mBinding.editText.text.toString(),
                        PageSource.NEW_USER_TASK_ACTIVITY,"")
                    reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK){
                        source = "新手阶段"
                        type = "去自定义"
                    }
                }
            }
        }
        mBinding.recyclerView.adapter = adapter
        mBinding.editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {

            }

            override fun afterTextChanged(s: Editable?) {
                if (s.toString().length > 46) {
                    mBinding.editText.setText(s?.substring(0, 46))
                    mBinding.editText.setSelection(mBinding.editText.length())
                    T.ss("请输入46个字以内的公司名")
                } else {
                    if (search) {
                        searchObservable(s.toString().trim { it <= ' ' })
                    }
                }
                checkEnable(mBinding.editText.text.toString())
                setErrorText("")
            }

        })
        setErrorText(activityViewModel.baseInfoLiveData.value?.baseInfo?.workCompanyCertInfo ?: "")
        if (mBinding.tvErrorDesc.text.toString().isNotEmpty()) {
            mBinding.btnNext.enable(false)
        }

        var i = if (SettingBuilder.getInstance()
                .getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE)
                .isEmpty()
        ) 0 else SettingBuilder.getInstance()
            .getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE).toInt()
        mBinding.idSubText.text = i.contentShowRange()

        mBinding.idSubText.onClick {
            reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK){
                source = "新手阶段"
                type = "设置可见范围"
            }
            showCompanyRangeBottomDialog(requireContext(), i, false,
                object : BottomListDialogNew.OnBottomItemClickListener {
                    override fun onBottomItemClick(
                        view: View?,
                        pos: Int,
                        bottomBean: SelectBottomBean?,
                    ) {
                        i = pos
                    }
                }, {
                    requestCompanyHide(
                        i
                    )
                    reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_CLICK){
                        source = "新手阶段"
                        type = i.contentShowRange()
                    }
                })

        }

        search = true


    }

    fun requestCompanyHide(select: Int) {
        val observable = RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).settingUpdate(
            UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE,
            select
        )
        HttpExecutor.requestSimple(observable, object : SimpleRequestCallback() {
            override fun onSuccess() {
                SettingBuilder.Builder().addUserSetting(
                    UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE,
                    UserSettingConfig(
                        UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE,
                        select.toString()
                    )
                ).build()
                ServiceManager.getInstance().settingService.matchVisibleLiveData.value = false
                var i = if (SettingBuilder.getInstance()
                        .getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE)
                        .isEmpty()
                ) 0 else SettingBuilder.getInstance()
                    .getUserSettingValue(UserSettingConfig.PERSONALITY_SETTING_COMPANY_INVISIBLE)
                    .toInt()
                mBinding.idSubText.text = i.contentShowRange()
            }

            override fun dealFail(reason: ErrorReason) {}
            override fun onComplete() {
                super.onComplete()
            }
        })
    }

    private fun checkEnable(s: String) {
        if (activityViewModel.initIndustryName.isNotEmpty()) {
            if (activityViewModel.initIndustryName == "在校学生" || activityViewModel.initIndustryName == "自由职业") {
                mBinding.btnNext.enable(true)
                return
            }
        }

        if (s.isEmpty()) {
            mBinding.btnNext.enable(false)
        }else{
            if(SpManager.get().user().getInt(Constants.CUSTOM_COMPANY, 0) == 1){
                if (s.equals(temWriteName)){
                    mBinding.btnNext.enable(true)
                }else{
                    mBinding.btnNext.enable(false)
                }
            }else{
                mBinding.btnNext.enable(true)
            }
        }
    }

    private fun searchObservable(content: String) {
        if (TextUtils.equals(content, activityViewModel.searchContent)) {
            return
        }
        activityViewModel.searchContent = content
        if (!TextUtils.isEmpty(content)) {
            searchSubject?.onNext(content)
        } else {
            activityViewModel.searchCompanyLiveData.setValue(null)
        }
    }

    private var searchSubject: PublishSubject<String>? = null
    private var searchDisposable: Disposable? = null

    private fun initSearchSubject() {
        searchSubject = PublishSubject.create<String>()
        searchSubject!!.debounce(500, TimeUnit.MILLISECONDS)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(object : io.reactivex.rxjava3.core.Observer<String> {
                override fun onSubscribe(d: Disposable) {
                    searchDisposable = d
                }

                override fun onNext(s: String) {
                    activityViewModel.searchCompany(s)
                }

                override fun onError(e: Throwable) {}
                override fun onComplete() {}
            })
    }

    override fun onDestroy() {
        super.onDestroy()
        if (searchDisposable != null) {
            searchDisposable!!.dispose()
        }
    }

    override fun onResume() {
        super.onResume()
        reportPoint(NewTaskPointAction.CHILD_NEWGUIDANCE_INFO_COMPLETE_PAGE_EXPO) {
            step = mBinding.idTitle.getTitle()
            source = activityViewModel.getPageSourceStr()
        }
        reportPoint(MePointAction.COMPANY_NAMEFILL_PAGE_EXPO) {
            source = "新手阶段"
        }
    }

    override fun initData() {

    }

    override fun onRetry() {

    }

    override fun getStateLayout() = mBinding.idStateLayout

}