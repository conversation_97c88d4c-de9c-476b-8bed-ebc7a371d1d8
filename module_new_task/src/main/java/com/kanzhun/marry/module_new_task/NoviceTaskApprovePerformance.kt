package com.kanzhun.marry.module_new_task

import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.common.performance.AbsPerformance
import com.kanzhun.foundation.logic.service.ServiceManager
import com.kanzhun.marry.module_new_task.dialog.NoviceTaskApproveSuccessDialog

/**
 * 新手任务全部审核通过
 */
class NoviceTaskApprovePerformance : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        ServiceManager.getInstance().noviceTaskService.getNoviceTassApproveFinishEvent()?.observe(owner) { success ->
            if (success) {
                ServiceManager.getInstance().noviceTaskService.resetNoviceTassApproveFinishEvent()
                if(!AccountHelper.getInstance().isParent){//父母模式下不允许弹框
                    NoviceTaskApproveSuccessDialog(owner as FragmentActivity).show()
                }
            }
        }
    }
}