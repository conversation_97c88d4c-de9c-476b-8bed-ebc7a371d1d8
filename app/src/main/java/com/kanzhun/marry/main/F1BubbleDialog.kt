package com.kanzhun.marry.main

import androidx.appcompat.app.AppCompatActivity
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.Constants.ONE_DAY
import com.kanzhun.foundation.SystemConfigInstance
import com.kanzhun.foundation.api.FoundationApiK
import com.kanzhun.foundation.api.response.GetUserTagResponse
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.kotlin.ktx.globalSp
import com.kanzhun.foundation.router.MePageRouter.jumpToUpdateIntroActivity
import com.kanzhun.foundation.sp.SpManager
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.matching.fragment.home.performance.SP_KEY_HOME_USER_COVER_GUIDE
import com.techwolf.lib.tlog.TLog

const val SP_KEY_F1BubbleDialog = "SP_KEY_F1BubbleDialog_1"

class F1BubbleDialog(activity: AppCompatActivity) : AbsF1DialogHandler(activity) {
    override fun handler() {
        getData()
    }

    override fun tabChange(index: Int) {

    }

    fun getData() {
        if (globalSp().getBoolean(SP_KEY_HOME_USER_COVER_GUIDE, true)) {
            //没有谈过蒙层 不出下面弹窗
            return
        }
        TLog.print("F1Dialog", "F1BubbleDialog getData")
        if (indexLocal == 0 && isOver() && SystemConfigInstance.isOpenIdealPartnerTagGray() && AccountHelper.getInstance().phase >= 5
        ) {
            val baseResponseObservable = RetrofitManager.getInstance().createApi(
                FoundationApiK::class.java
            ).getUserTagUserTag("2")
            HttpExecutor.execute(
                baseResponseObservable,
                object : BaseRequestCallback<GetUserTagResponse>() {

                    override fun onSuccess(data: GetUserTagResponse) {
                        if(data.idealPartnerTags?.isNotEmpty() == true){
                            doNext()
                            return
                        }
                        SpManager.get().user().edit().putLong(SP_KEY_F1BubbleDialog, System.currentTimeMillis()).apply()
                        val content = data.idealPartnerDesc
                        val rejectReason = data.idealPartnerDescInfo
                        val certStatus = data.idealPartnerDescStatus.toString()
                        jumpToUpdateIntroActivity(activity,content = content,
                            certInfo = rejectReason, pageSource = PageSource.F1_DIALOG,
                            type = 2,certStatus = certStatus)
                    }

                    override fun dealFail(reason: ErrorReason?) {
                        doNext()
                    }

                })

        } else {
            TLog.print("F1Dialog", "F1BubbleDialog doNext")
            doNext()
        }
    }

    private fun isOver(): Boolean {
        val lastTime = SpManager.get().user()
            .getLong(SP_KEY_F1BubbleDialog, 0)
        return (lastTime - System.currentTimeMillis()) > ONE_DAY || lastTime == 0L
    }

}
