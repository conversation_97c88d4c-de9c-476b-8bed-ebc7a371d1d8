package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.router.MePageRouter

class GraduatePicAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        MePageRouter.jumpToIdentifyPicActivity(
            context = context,
            pageSource = if ((params["pageSource"]?.toInt()
                    ?: -1) == PageSource.AUTH_RECERT_ACTIVITY.ordinal
            ) PageSource.AUTH_RECERT_ACTIVITY else PageSource.NONE
        )
    }
}