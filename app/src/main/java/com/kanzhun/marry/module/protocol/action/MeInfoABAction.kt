package com.kanzhun.marry.module.protocol.action

import android.content.Context
import android.content.Intent
import com.kanzhun.common.constract.BundleConstants
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.api.FoundationApi
import com.kanzhun.foundation.api.model.ABFace
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.info.activity.ABImpressionActivity

//AB面编辑页
class MeInfoABAction:IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val id = params["id"]
        val baseResponseObservable = RetrofitManager.getInstance().createApi(
            FoundationApi::class.java
        ).getABbyId(id)
        HttpExecutor.execute(baseResponseObservable, object : BaseRequestCallback<ABFace>(){
            override fun onSuccess(data: ABFace?) {
                if(data==null)return
                val intent = Intent(context, ABImpressionActivity::class.java)
                intent.putExtra(BundleConstants.BUNDLE_A_B_IMPRESSION, data)
                AppUtil.startActivityForResult(
                    context,
                    intent,
                    RequestCodeConstants.REQUEST_CODE_ME_AB_FACE_IMPRESSION
                )
            }

            override fun dealFail(reason: ErrorReason?) {
            }

        })
    }
}