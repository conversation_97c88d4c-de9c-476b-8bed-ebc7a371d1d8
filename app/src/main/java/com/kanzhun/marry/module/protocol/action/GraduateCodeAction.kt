package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.ActivityPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.utils.T

class GraduateCodeAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        MePageRouter.jumpToIdentifyCodeActivity(context)
    }
}