package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.foundation.bean.SerializableMap
import com.kanzhun.foundation.kernel.account.AccountHelper
import com.kanzhun.foundation.router.MatchingPageRouter
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.utils.T

class LiveActivityAction : IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        if(AccountHelper.getInstance().isCanMatch){
            val serializableMap = SerializableMap()
            serializableMap.map = params
            MatchingPageRouter.jumpToNewGuestActivity(context,serializableMap)
        }else{
            T.ss("请先完成新手任务才能查看活动嘉宾哦")
        }
    }
}