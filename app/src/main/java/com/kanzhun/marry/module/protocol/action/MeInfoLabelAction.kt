package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.foundation.api.model.ProfileInfoModel
import com.kanzhun.foundation.router.LoginPageRouter

//我的个性标签页
class MeInfoLabelAction:IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val tag = params["tagId"]?.split(",")
        val bean: MutableList<ProfileInfoModel.Label> = mutableListOf()
        tag?.forEach { j:String?->
            if(j.isNullOrEmpty()) return@forEach
            bean.add(ProfileInfoModel.Label().also {
                it.tagId = j.toInt()
            })
        }
        LoginPageRouter.jumpToKeyLabel(
            context,
            bean,
            RequestCodeConstants.REQUEST_CODE_SELECT_LABEL,
            1
        )
    }
}