package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.router.MePageRouter
import com.kanzhun.http.HttpExecutor
import com.kanzhun.http.RetrofitManager
import com.kanzhun.http.callback.BaseRequestCallback
import com.kanzhun.http.error.ErrorReason
import com.kanzhun.marry.me.api.MeApi
import com.kanzhun.marry.me.api.model.CompanyCertInfoModelNew

class ComCertProtocolAction :IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val responseObservable = RetrofitManager.getInstance().createApi(
            MeApi::class.java
        ).getCompanyCertInfo()
        HttpExecutor.execute<CompanyCertInfoModelNew>(
            responseObservable,
            object : BaseRequestCallback<CompanyCertInfoModelNew?>(true) {
                override fun onSuccess(data: CompanyCertInfoModelNew?) {
                    if(data?.companyCertStatus != 3){
                        val from = params[ProtocolHelper.SOURCE]
                        MePageRouter.jumpToMeCompanyAuthActivity(context,PageSource.PROTOCOL,from?:"", btnText = "提交，去认证")
                    }
                }

                override fun dealFail(reason: ErrorReason) {
                }
            })

    }
}