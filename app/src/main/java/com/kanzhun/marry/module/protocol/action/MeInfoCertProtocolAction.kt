package com.kanzhun.marry.module.protocol.action

import android.content.Context
import com.kanzhun.common.base.PageSource
import com.kanzhun.common.util.ProtocolHelper
import com.kanzhun.foundation.model.UserGuideBlockInfoBean
import com.kanzhun.foundation.router.TaskPageRouter

class MeInfoCertProtocolAction :IProtocolAction {
    override fun startAction(context: Context, params: Map<String, String>) {
        val from = params[ProtocolHelper.SOURCE]
       TaskPageRouter.jumpToTaskNewUserActivity(context,
           UserGuideBlockInfoBean(true,true),
           PageSource.PROTOCOL,from?:"")
    }
}