<?xml version="1.0" encoding="utf-8"?>
<layout>

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_7F7F7F"
            android:layout_marginLeft="28dp"
            android:layout_marginRight="28dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:textSize="@dimen/common_text_sp_16"/>
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/common_color_EBEBEB"
            android:layout_marginLeft="28dp"
            android:layout_marginRight="28dp"/>
    </LinearLayout>
</layout>