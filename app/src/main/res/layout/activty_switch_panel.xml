<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/cus_title_bar"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:visibility="visible">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="left|center_vertical"
                android:paddingLeft="20dp"
                android:text="自定义标题栏"
                android:textColor="@android:color/white"
                android:textSize="20sp"
                android:textStyle="bold" />
        </RelativeLayout>

        <com.kanzhun.keyboard.switchpanel.view.PanelSwitchLayout
            android:id="@+id/panel_switch_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:animationSpeed="standard">

            <!-- 内容区域 -->
            <!-- edit_view 指定一个 EditText 用于输入 ，必须项-->
            <!-- empty_view 指定用户点击该 ID 对应的 View 时实现面板或者输入法隐藏，非必须项 -->
            <com.kanzhun.keyboard.switchpanel.view.content.RelativeContentContainer
                android:id="@+id/content_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                app:edit_view="@id/edit_text"
                tools:layout_height="500dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/bottom_action"
                    android:tag="recycler_view" />

                <TextView
                    android:id="@+id/tip_view_top"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_marginTop="100dp"
                    android:background="@android:color/holo_blue_bright"
                    android:gravity="center"
                    android:padding="10dp"
                    android:tag="tip_view_top"
                    android:text="干预滑动，偏顶部布局滑动时不被其滑走"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tip_view"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="200dp"
                    android:layout_marginRight="100dp"
                    android:background="@android:color/holo_blue_bright"
                    android:gravity="center"
                    android:padding="10dp"
                    android:text="不做干预，默认 ContentContainer 内的布局会随面板展开而滑动"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/tip_view_bottom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_above="@+id/bottom_action"
                    android:layout_alignParentRight="true"
                    android:layout_marginBottom="100dp"
                    android:background="@android:color/holo_blue_bright"
                    android:gravity="center"
                    android:padding="10dp"
                    android:tag="tip_view_bottom"
                    android:text="干预滑动，偏底部布局滑动时让其不被盖住"
                    android:textColor="@android:color/white"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:typeface="sans"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/bottom_action"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:background="@color/white"
                    android:gravity="bottom"
                    android:minHeight="50dp"
                    android:orientation="horizontal"
                    android:paddingLeft="@dimen/dp_10"
                    android:paddingRight="@dimen/dp_10"
                    android:paddingBottom="8dp">

                    <!-- 更多入口 -->
                    <ImageView
                        android:id="@+id/add_btn"
                        android:layout_width="35dp"
                        android:layout_height="35dp"
                        android:layout_marginRight="@dimen/dp_10"
                        android:src="@drawable/icon_add" />

                    <!-- 输入入口 -->
                    <EditText
                        android:id="@+id/edit_text"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/dp_10"
                        android:layout_marginRight="@dimen/dp_10"
                        android:layout_weight="1"
                        android:imeOptions="actionSearch"
                        android:maxLines="5"
                        android:minHeight="35dp"
                        android:paddingLeft="3dp"
                        android:paddingTop="8dp"
                        android:paddingRight="3dp"
                        android:paddingBottom="3dp"
                        android:textSize="16dp" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="35dp"
                        android:orientation="horizontal">

                        <!-- 表情入口 -->
                        <ImageView
                            android:id="@+id/emotion_btn"
                            android:layout_width="35dp"
                            android:layout_height="35dp"
                            android:layout_marginEnd="@dimen/dp_10"
                            android:layout_marginRight="@dimen/dp_10"
                            android:src="@drawable/selector_emotion_btn" />

                        <TextView
                            android:id="@+id/send"
                            android:layout_width="50dp"
                            android:layout_height="35dp"
                            android:background="@color/qmui_config_color_50_blue"
                            android:gravity="center"
                            android:text="发送"
                            android:textColor="@color/white"
                            android:textSize="15dp" />
                    </LinearLayout>

                </LinearLayout>

            </com.kanzhun.keyboard.switchpanel.view.content.RelativeContentContainer>


            <!-- 面板区域，仅能包含PanelView-->
            <com.kanzhun.keyboard.switchpanel.view.panel.PanelContainer
                android:id="@+id/panel_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#ebebeb"
                tools:layout_height="0dp"
                tools:layout_weight="1">

                <!-- 每一项面板 -->
                <!-- panel_layout 用于指定面板该 ID 对应的布局 ，必须项-->
                <!-- panel_trigger 用于用户点击该 ID 对应的 View 时切换到该面板 -->
                <!-- panel_toggle  用于当该面板显示时 ，用户再次点击 panel_trigger 对应的 View 时是否回切输入法-->
                <com.kanzhun.keyboard.switchpanel.view.panel.PanelView
                    android:id="@+id/panel_emotion"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:panel_layout="@layout/panel_emotion_layout"
                    app:panel_trigger="@id/emotion_btn" />

                <com.kanzhun.keyboard.switchpanel.view.panel.PanelView
                    android:id="@+id/panel_addition"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:panel_layout="@layout/panel_add_layout"
                    app:panel_trigger="@id/add_btn" />

            </com.kanzhun.keyboard.switchpanel.view.panel.PanelContainer>
        </com.kanzhun.keyboard.switchpanel.view.PanelSwitchLayout>

    </LinearLayout>
</layout>