<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="viewModel"
            type="com.kanzhun.marry.login.viewmodel.LoginViewModel" />
    </data>
<FrameLayout
    android:id="@+id/fl_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_white">

    <fragment
        android:id="@+id/fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:defaultNavHost="true"
        />
    <!--        app:navGraph="@navigation/login_navigation"-->
</FrameLayout>
</layout>