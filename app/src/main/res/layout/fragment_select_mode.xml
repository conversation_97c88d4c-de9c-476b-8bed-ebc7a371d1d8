<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/tv_phone_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_icon="@drawable/img_ic_select_role"
            app:title_text="您的目的是？" />

        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:id="@+id/idSelfFindLayout"
            android:layout_width="150dp"
            android:layout_height="100dp"
            android:layout_marginStart="28dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/idHelpChildFindLayout"
            app:qmui_radius="12dp">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/idSelfFindIV"
                android:src="@mipmap/img_ic_child_mode_unselect"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY"/>

            <ImageView
                android:id="@+id/idSelfFindIVRectangle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:src="@drawable/me_bg_activate_house_car_sel"
                />

            <ImageView
                android:id="@+id/idSelfFindISelect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:src="@drawable/login_icon_activate_sex_sel"
                android:layout_gravity="end"
                />
        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

        <com.qmuiteam.qmui.layout.QMUIFrameLayout
            android:id="@+id/idHelpChildFindLayout"
            app:layout_constraintLeft_toRightOf="@+id/idSelfFindLayout"
            android:layout_width="150dp"
            android:layout_height="100dp"
            android:layout_marginEnd="28dp"
            android:layout_marginStart="20dp"
            app:layout_constraintTop_toTopOf="@+id/idSelfFindLayout"
            app:layout_constraintRight_toRightOf="parent"
            app:qmui_radius="12dp">
            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/idHelpChildFindIV"
                android:src="@mipmap/img_ic_parent_mode_unselect"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="fitXY" />

            <ImageView
                android:id="@+id/idHelpChildFindIVRectangle"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_height="match_parent"
                android:src="@drawable/me_bg_activate_house_car_sel"
                />

            <ImageView
                android:id="@+id/idHelpChildFindIVSelect"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:src="@drawable/login_icon_activate_sex_sel"
                android:layout_gravity="end"
                />
        </com.qmuiteam.qmui.layout.QMUIFrameLayout>

        <TextView
            android:id="@+id/idSelfFind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginTop="12dp"
            app:layout_constraintLeft_toLeftOf="@+id/idSelfFindLayout"
            app:layout_constraintRight_toRightOf="@+id/idSelfFindLayout"
            app:layout_constraintTop_toBottomOf="@+id/idSelfFindLayout"
            android:fontFamily="sans-serif-condensed-medium"
            android:text="自己找"/>

        <TextView
            android:id="@+id/idHelpChildFind"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_color_191919"
            android:textSize="@dimen/common_text_sp_16"
            android:layout_marginTop="12dp"
            app:layout_constraintLeft_toLeftOf="@+id/idHelpChildFindLayout"
            app:layout_constraintRight_toRightOf="@+id/idHelpChildFindLayout"
            app:layout_constraintTop_toBottomOf="@+id/idSelfFindLayout"
            android:fontFamily="sans-serif-condensed-medium"
            android:text="帮人找"/>

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            android:id="@+id/btn_next"
            android:layout_marginEnd="28dp"
            android:layout_marginBottom="28dp"
            app:layout_constraintLeft_toLeftOf="parent"
            style="@style/button_large_next_page_style"
            android:enabled="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>