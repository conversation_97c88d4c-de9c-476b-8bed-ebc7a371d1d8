<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <variable
            name="viewModel"
            type="com.kanzhun.marry.login.viewmodel.LoginCodeViewModel" />

        <variable
            name="activityViewModel"
            type="com.kanzhun.marry.login.viewmodel.LoginViewModel" />

        <variable
            name="callback"
            type="com.kanzhun.marry.login.callback.LoginCodeCallback" />
        <import type="android.view.View" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.kanzhun.foundation.views.CommonPageTitleView
            android:id="@+id/tv_phone_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:title_text_sub="@string/login_code_to_phone_number"
            app:title_icon="@drawable/login_ic_icon_login_phone"
            app:title_text="@string/login_input_sms_code" />

        <FrameLayout
            android:id="@+id/fl_edit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/tv_phone_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_marginLeft="30dp"
            android:layout_marginRight="30dp"
            android:onClick="@{v->callback.showInput()}">

        <com.kanzhun.common.views.edittext.SplitEditTextView
            android:id="@+id/edit_text"
            android:layout_width="match_parent"
            android:layout_height="55dp"
            android:inputType="number"
            android:longClickable="false"
            android:textSize="32sp"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:textColor="@color/common_black"
            android:fontFamily="sans-serif-condensed-medium"
            app:common_borderSize="1dp"
            app:common_contentNumber="6"
            app:common_contentShowMode="text"
            app:common_inputBoxStyle="underline"
            app:common_cursorColor="@color/common_color_191919"
            app:common_spaceSize="10dp"
            app:common_underlineFocusColor="@color/common_color_191919"
            app:common_underlineNormalColor="@color/common_color_191919"
            app:common_underlineErrorColor="@color/common_color_FF3F4B"/>
        </FrameLayout>

        <TextView
            android:id="@+id/tv_re_send"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/fl_edit"
            android:layout_marginTop="43dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/tv_re_send_count"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:textSize="@dimen/common_text_sp_14"
            android:text="@string/login_re_send"
            android:enabled="@{activityViewModel.timeCount &lt;= 0 ? true : false}"
            android:textColor="@{activityViewModel.timeCount &lt;= 0 ? @color/common_color_191919 : @color/common_color_7F7F7F}"
            tools:text="重新发送"/>

        <TextView
            android:id="@+id/tv_re_send_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="@+id/tv_re_send"
            app:layout_constraintBottom_toBottomOf="@+id/tv_re_send"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tv_re_send"
            app:layout_constraintHorizontal_chainStyle="packed"
            android:textSize="@dimen/common_text_sp_14"
            android:textColor="@color/common_color_191919"
            android:layout_marginLeft="5dp"
            android:visibility="@{activityViewModel.timeCount>0 ? View.VISIBLE : View.GONE}"
            android:text="@{@string/login_re_send_num(activityViewModel.timeCount)}"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>