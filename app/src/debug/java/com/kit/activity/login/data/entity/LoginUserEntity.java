package com.kit.activity.login.data.entity;

import androidx.annotation.NonNull;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "login_users")
public class LoginUserEntity {
    @PrimaryKey
    @NonNull
    private String userId;
    private String avatar;
    private String nickname;
    private String phone;
    private int gender;
    private long lastLoginTime;

    public LoginUserEntity(@NonNull String userId, String avatar, String nickname, String phone, int gender, long lastLoginTime) {
        this.userId = userId;
        this.avatar = avatar;
        this.nickname = nickname;
        this.phone = phone;
        this.gender = gender;
        this.lastLoginTime = lastLoginTime;
    }

    @NonNull
    public String getUserId() {
        return userId;
    }

    public void setUserId(@NonNull String userId) {
        this.userId = userId;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getGender() {
        return gender;
    }

    public void setGender(int gender) {
        this.gender = gender;
    }

    public long getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
}