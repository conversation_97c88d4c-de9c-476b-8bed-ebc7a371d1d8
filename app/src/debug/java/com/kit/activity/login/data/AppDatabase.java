package com.kit.activity.login.data;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;

import com.kit.activity.login.data.dao.LoginUserDao;
import com.kit.activity.login.data.entity.LoginUserEntity;

@Database(entities = {LoginUserEntity.class}, version = 1)
public abstract class AppDatabase extends RoomDatabase {
    public abstract LoginUserDao loginUserDao();

    private static volatile AppDatabase INSTANCE;

    public static AppDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AppDatabase.class,
                            "app_database"
                    ).build();
                }
            }
        }
        return INSTANCE;
    }
}