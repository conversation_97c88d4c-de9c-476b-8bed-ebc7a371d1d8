package com.kit;

import android.app.Application;

import com.kit.floatpage.FloatPageManager;
import com.kit.util.FloatIconUtil;
import com.kit.util.KitForegroundUtil;


/**
 * create by guofeng
 * date on 2021/7/27
 */

public class Kit  {

    private static final Kit instance = new Kit();

    private Kit() {
    }

    public static Kit getInstance() {
        return instance;
    }


    private Application application;

    public Application getApplication() {
        return application;
    }

    public void install(Application application) {
        this.application = application;
        //初始化WindowManager
        FloatPageManager.getInstance().init(application);
        //注册页面监控
        KitForegroundUtil.getInstance().register(application);
        //注册监控生命周期
        FloatIconUtil.getInstance().registerMainLifeCircle();
    }

}