package com.kit.extra

import android.content.Intent
import androidx.compose.ui.graphics.Color
import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.util.AppUtil
import com.kit.UnlockActivity
import com.kit.baselibrary.IExtraBean

class UnlockExtraEntry : IExtraBean {
    override fun getName(): String {
        return "人脸解锁"
    }

    override fun textColor(): Color {
        return Color.Blue
    }

    override fun onClick(context: FragmentActivity) {
        AppUtil.startActivity(context, Intent(context, UnlockActivity::class.java))
    }
}