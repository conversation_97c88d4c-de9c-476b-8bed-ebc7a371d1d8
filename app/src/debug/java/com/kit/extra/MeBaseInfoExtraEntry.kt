package com.kit.extra

import androidx.fragment.app.FragmentActivity
import com.kanzhun.common.util.AppUtil
import com.kanzhun.foundation.RequestCodeConstants
import com.kanzhun.marry.me.info.activity.MeBaseInfoEditActivity
import com.kit.baselibrary.IExtraBean


class MeBaseInfoExtraEntry : IExtraBean {
    override fun getName(): String {
        return "我的基本信息"
    }

    override fun onClick(context: FragmentActivity) {

        AppUtil.startActivityForResult(
            context,
            MeBaseInfoEditActivity.createIntent(context),
            RequestCodeConstants.REQUEST_CODE_BASE_INFO
        )
    }


}