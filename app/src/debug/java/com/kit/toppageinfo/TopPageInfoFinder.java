package com.kit.toppageinfo;

import static com.kit.toppageinfo.FloatView.KEY_LAST_POSITION;

import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.view.Gravity;
import android.view.WindowManager;
import android.view.WindowManager.LayoutParams;

import androidx.annotation.Nullable;

import com.hpbr.apm.common.persistence.SharedPrefs;
import com.kanzhun.utils.L;
import com.kanzhun.utils.base.LText;
import com.kit.Kit;

public class TopPageInfoFinder {

    private static final String KEY_SHOW_TOP_PAGE_INFO = "key_show_top_page_info";

    private static final LayoutParams LAYOUTPARAMS;

    static {
        LayoutParams layoutParams = new LayoutParams();

        String lastPosition = SharedPrefs.get().getString(KEY_LAST_POSITION);
        String[] lastPositionXY = lastPosition.split(",");
        if (lastPositionXY.length == 2) {
            layoutParams.x = LText.getInt(lastPositionXY[0]);
            layoutParams.y = LText.getInt(lastPositionXY[1]);
            L.d(FloatView.TAG, "static: " + lastPosition);
        } else {
            layoutParams.x = 0;
            layoutParams.y = 0;
        }

        layoutParams.width = LayoutParams.WRAP_CONTENT;
        layoutParams.height = LayoutParams.WRAP_CONTENT;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            layoutParams.type = LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            layoutParams.type = LayoutParams.TYPE_PHONE;
        }
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.flags = LayoutParams.FLAG_NOT_TOUCH_MODAL | LayoutParams.FLAG_NOT_FOCUSABLE;
        LAYOUTPARAMS = layoutParams;
    }

    @Nullable
    private Boolean isOpen = null;
    private FloatView mFloatView;

    private static final TopPageInfoFinder INSTANCE = new TopPageInfoFinder();
    private final _PageInfoFinder.LifeCycleCallback lifeCycleCallback = new _PageInfoFinder.LifeCycleCallback();
    private final WindowManager windowManager;

    private TopPageInfoFinder() {
        windowManager = ((WindowManager) Kit.getInstance().getApplication().getSystemService(Context.WINDOW_SERVICE));
    }

    public static TopPageInfoFinder getInstance() {
        return INSTANCE;
    }

    public void toggleFloat(boolean isOpen) {
        if (isOpen) {
            showFloat();
        } else {
            hideFloat();
        }
    }

    public void showFloat() {
        openFloat();
        setIsOpen(true);
    }

    public void hideFloat() {
        closeFloat();
        setIsOpen(false);
    }

    public void shouldShowTopPageInfo() {
        if (isOpen()) {
            openFloat();
        }
    }

    private void setIsOpen(boolean isOpen) {
        this.isOpen = isOpen;
        SharedPrefs.get().putBoolean(KEY_SHOW_TOP_PAGE_INFO, isOpen);
    }

    public boolean isOpen() {
        if (isOpen == null) {
            isOpen = SharedPrefs.get().getBoolean(KEY_SHOW_TOP_PAGE_INFO, false);
        }
        return isOpen;
    }

    public void hideTemporary() {
        closeFloat();
    }

    private void openFloat() {
        if (checkAlertWindowPermission()) {
            addFloatView();
            Kit.getInstance().getApplication().registerActivityLifecycleCallbacks(lifeCycleCallback);
            updateDisplay("", "   使用说明\n   2秒内N次点击\n1次：刷新信息\n3次：切换最近一条或三条\n拖拽：View可以拖拽\n");
        }
    }

    private void closeFloat() {
        Kit.getInstance().getApplication().unregisterActivityLifecycleCallbacks(lifeCycleCallback);
        removeFloatView();
    }

    private void addFloatView() {
        try {
            if (mFloatView == null) {
                mFloatView = new FloatView(Kit.getInstance().getApplication());
                windowManager.addView(mFloatView, LAYOUTPARAMS);
            }
        } catch (Exception ignored) {

        }
    }

    private void removeFloatView() {
        if (mFloatView != null) {
            windowManager.removeView(mFloatView);
            mFloatView = null;
        }
    }

    void updateDisplay(String packageNameStr, String classNameStr) {
        if (mFloatView != null) {
            mFloatView.updateDisplay(packageNameStr, classNameStr);
        }
    }

    private boolean checkAlertWindowPermission() {
        final Context context = Kit.getInstance().getApplication();
        if (!Settings.canDrawOverlays(context)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION, Uri.parse("package:" + context.getPackageName()));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            return false;
        }
        return true;
    }

}
