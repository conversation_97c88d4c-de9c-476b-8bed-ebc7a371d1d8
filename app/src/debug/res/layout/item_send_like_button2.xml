<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical">

    <FrameLayout
        android:layout_width="120dp"
        android:layout_height="120dp"
        tools:background="@color/default_progress_color">


        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lavSendLike"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            app:lottie_autoPlay="false"
            app:lottie_fileName="send_like/chat.json"
            app:lottie_loop="false" />


    </FrameLayout>

    <Space
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1" />

    <Button
        android:id="@+id/btn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="btn"
        android:textAllCaps="false" />

</LinearLayout>
