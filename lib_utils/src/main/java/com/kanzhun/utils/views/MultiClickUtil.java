package com.kanzhun.utils.views;

import android.os.SystemClock;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/3/30
 * 防止快速点击
 */
public class MultiClickUtil {
    public static final int DELAY_DEFAULT = 1000;

    private static long mLastClickTime;


    public static boolean isMultiClick() {
       return isMultiClick(DELAY_DEFAULT);
    }

    /**
     * 判断是否是快速点击
     * @param duration 时间间隔
     * @return true 是  false 不是，表示正常点击
     */
    public static boolean isMultiClick(int duration) {
        long curClickTime = SystemClock.elapsedRealtime();
        if (curClickTime - mLastClickTime > duration) {
            mLastClickTime = curClickTime;
            return false;
        }
        return true;
    }
}
