package com.kanzhun.utils.file;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.media.MediaPlayer;
import android.media.ThumbnailUtils;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.provider.DocumentsContract;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Size;

import androidx.documentfile.provider.DocumentFile;

import com.kanzhun.utils.base.MD5;
import com.kanzhun.utils.platform.Utils;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.util.Locale;

import okio.BufferedSink;
import okio.BufferedSource;
import okio.Okio;
import okio.Sink;
import okio.Source;

/**
 * Created by wangtian on 2017/8/28.
 */

public class FileUtils {

    public static String read(File file) {
        if (file == null || !file.exists()) {
            return null;
        }
        String result = "";
        BufferedSource bufferedSource = null;
        try {
            Source source = Okio.source(file);
            bufferedSource = Okio.buffer(source);
            result = bufferedSource.readUtf8();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            closeCloseable(bufferedSource);
        }

        return result;
    }


    public static void write(File file, String content) throws IOException {
        if (file == null) {
            return;
        }
        if (!file.exists()) {
            File parent = file.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            file.createNewFile();
        }
        Sink sink;
        BufferedSink bufferedSink = null;

        try {
            sink = Okio.sink(file);
            bufferedSink = Okio.buffer(sink);
            bufferedSink.writeUtf8(content);

            bufferedSink.flush();
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            closeCloseable(bufferedSink);
        }
    }

    public static void write(File file, byte[] content) throws IOException {
        if (file == null) {
            return;
        }
        if (!file.exists()) {
            File parent = file.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            file.createNewFile();
        }
        Sink sink;
        BufferedSink bufferedSink = null;

        try {
            sink = Okio.sink(file);
            bufferedSink = Okio.buffer(sink);
            bufferedSink.write(content);
            bufferedSink.flush();
        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            closeCloseable(bufferedSink);
        }
    }


    public static File getCacheDir(Context context) {
        File mCacheFile = null;
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                || !Environment.isExternalStorageRemovable()) {
            mCacheFile = context.getExternalCacheDir();
        }
        if (mCacheFile == null) {
            mCacheFile = context.getCacheDir();
        }
        return mCacheFile;
    }

    public static File getTempFile(File source) throws RuntimeException {
        File file = createTempFile(source);
        if (copyFile(source, file)) {
            return file;
        }
        return null;
    }

    public static File createTempFile(File source) throws RuntimeException {
        File tempFile;
        try {
            String name = source.getName();
            int index = name.lastIndexOf(".");
            String prefix = index < 0 ? name : name.substring(0, index) + "_";
            String suffix = index < 0 ? null : name.substring(index);
            File directory = source.getParentFile();
            tempFile = File.createTempFile(prefix, suffix, directory);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return tempFile;
    }

    public static File getTempFile(File source,String prefix) throws RuntimeException {
        File file = createTempFile(source,prefix);
        if (copyFile(source, file)) {
            return file;
        }
        return null;
    }

    public static File createTempFile(File source,String prefix) throws RuntimeException {
        File tempFile;
        try {
            String name = source.getName();
            int index = name.lastIndexOf(".");
            String suffix = index < 0 ? null : name.substring(index);
            File directory = source.getParentFile();
            tempFile = File.createTempFile(prefix, suffix, directory);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return tempFile;
    }


    public static boolean copyFile(File source, File dest) throws RuntimeException {
        boolean ret = false;
        try {
            if (checkOnCopyFile(source, dest)) {
                Source s = null;
                BufferedSink bufferedSink = null;
                try {
                    s = Okio.source(source);
                    bufferedSink = Okio.buffer(Okio.sink(dest));
                    bufferedSink.writeAll(s);
                    bufferedSink.flush();
                    ret = true;
                } catch (IOException e) {
                    throw e;
                } finally {
                    closeCloseable(s);
                    closeCloseable(bufferedSink);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    public static boolean copyFile(InputStream source, File dest) throws RuntimeException {
        boolean ret = false;
        try {
            Source s = null;
            BufferedSink bufferedSink = null;
            try {
                s = Okio.source(source);
                bufferedSink = Okio.buffer(Okio.sink(dest));
                bufferedSink.writeAll(s);
                bufferedSink.flush();
                ret = true;
            } catch (IOException e) {
                throw e;
            } finally {
                closeCloseable(s);
                closeCloseable(bufferedSink);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    public static boolean copyFile(InputStream source, OutputStream dest) throws RuntimeException {
        boolean ret = false;
        try {
            Source s = null;
            BufferedSink bufferedSink = null;
            try {
                s = Okio.source(source);
                bufferedSink = Okio.buffer(Okio.sink(dest));
                bufferedSink.writeAll(s);
                bufferedSink.flush();
                ret = true;
            } catch (IOException e) {
                throw e;
            } finally {
                closeCloseable(s);
                closeCloseable(bufferedSink);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ret;
    }

    /**
     * 复制文件
     *
     * @param context    上下文对象
     * @param zipPath    源文件
     * @param targetPath 目标文件
     * @throws Exception
     */
    public static void copy(Context context, String zipPath, String targetPath) throws Exception {
        if (TextUtils.isEmpty(zipPath) || TextUtils.isEmpty(targetPath)) {
            return;
        }
        Exception exception = null;
        File dest = new File(targetPath);
        dest.getParentFile().mkdirs();
        InputStream in = null;
        OutputStream out = null;
        try {
            in = new BufferedInputStream(context.getAssets().open(zipPath));
            out = new BufferedOutputStream(new FileOutputStream(dest));
            byte[] buffer = new byte[1024];
            int length = 0;
            while ((length = in.read(buffer)) != -1) {
                out.write(buffer, 0, length);
            }
        } catch (FileNotFoundException e) {
            exception = new Exception(e);
        } catch (IOException e) {
            exception = new Exception(e);
        } finally {
            try {
                out.close();
                in.close();
            } catch (IOException e) {
                exception = new Exception(e);
            }
        }
        if (exception != null) {
            throw exception;
        }
    }

    /**
     * 拷贝assets文件下文件到指定路径
     *
     * @param context
     * @param assetDir  源文件/文件夹
     * @param targetDir 目标文件夹
     * @return
     * @throws Exception
     */
    public static boolean copyAssets(Context context, String assetDir, String targetDir) throws Exception {
        if (TextUtils.isEmpty(assetDir) || TextUtils.isEmpty(targetDir)) {
            return false;
        }
        String separator = File.separator;
        // 获取assets目录assetDir下一级所有文件以及文件夹
        String[] fileNames = context.getResources().getAssets().list(assetDir);
        // 如果是文件夹(目录),则继续递归遍历
        if (fileNames.length > 0) {
            File targetFile = new File(targetDir);
            if (!targetFile.exists() && !targetFile.mkdirs()) {
                return false;
            }
            for (String fileName : fileNames) {
                copyAssets(context, assetDir + separator + fileName, targetDir + separator + fileName);
            }
        } else { // 文件,则执行拷贝
            copy(context, assetDir, targetDir);
        }
        return false;
    }

    private static boolean checkOnCopyFile(File source, File dest) {
        if (source == null || dest == null
                || !source.isFile() || !source.exists()) {
            return false;
        }
        return true;
    }

    public static boolean isFileExists(final File file) {
        if (file == null) return false;
        if (file.exists()) {
            return true;
        }
        return isFileExists(file.getAbsolutePath());
    }

    /**
     * 判断文件是否存在
     */
    public static boolean isFileExists(final String filePath) {
        File file = getFileByPath(filePath);
        if (file == null) return false;
        if (file.exists()) {
            return true;
        }
        return isFileExistsApi29(filePath);
    }

    private static boolean isFileExistsApi29(String filePath) {
        if (Build.VERSION.SDK_INT >= 29) {
            try {
                Uri uri = Uri.parse(filePath);
                ContentResolver cr = Utils.getApp().getContentResolver();
                AssetFileDescriptor afd = cr.openAssetFileDescriptor(uri, "r");
                if (afd == null) return false;
                try {
                    afd.close();
                } catch (IOException ignore) {
                }
            } catch (FileNotFoundException e) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * 根据文件路径获取文件
     * <p>
     * 注意：如果创建不包含后缀名的文件路径，使用下面的getFileByPath，该方法多用于创建文件夹路径
     */
    public static File getFileByPath(final String filePath) {
        return isSpace(filePath) ? null : new File(filePath);
    }

    public static boolean isSpace(final String s) {
        if (s == null) return true;
        for (int i = 0, len = s.length(); i < len; ++i) {
            if (!Character.isWhitespace(s.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 存储raw文件到本地
     */
    public static String saveRawToSDCard(Context context, int rawResource, String fileName, File targetDir) {
        File existFile = new File(targetDir, fileName);
        if (existFile.exists()) return existFile.getPath();

        InputStream inStream = context.getResources().openRawResource(rawResource);
        File file = new File(FileUtils.getCacheDir(context), fileName);
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(file);//存入SDCard
            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, len);
            }
            inStream.close();
            fileOutputStream.flush();
            fileOutputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return file.getPath();
    }

    public static void closeCloseable(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void delete(String path, boolean ignoreDir) {
        if (path == null) return;
        File file = new File(path);
        delete(file, ignoreDir);
    }

    public static void delete(File file, boolean ignoreDir) {
        try {
            if (file == null || !file.exists()) {
                return;
            }
            if (file.isFile()) {
                file.delete();
                return;
            }

            File[] fileList = file.listFiles();
            if (fileList == null) {
                return;
            }

            for (File f : fileList) {
                delete(f.getAbsolutePath(), ignoreDir);
            }
            // delete the folder if need.
            if (!ignoreDir) file.delete();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //获取视频缩略图
    public static String getVideoThumbnail(Context context, Uri uri, int[] info) {
        File thumbnail = getVideoThumbnailFile(context, uri, info);
        if (thumbnail != null) {
            return thumbnail.getAbsolutePath();
        }
        return null;
    }

    //获取视频缩略图
    public static File getVideoThumbnailFile(Context context, Uri uri, int[] info) {
        File cacheDir = FileUtils.getCacheDir(context);
        if (cacheDir != null) {

            final String absoluteCachePath = cacheDir.getAbsolutePath() + "/videoThumbnails";
            File folder = new File(absoluteCachePath);
            if (!folder.exists()) { // 创建视频缩略图目录，如果目录不存在
                folder.mkdirs();
            }
            String filename = getFileNameFromUri(context, uri);
            File file = new File(absoluteCachePath, filename + ".jpg");
            if (file.exists()) { // 如果有同名文件，直接删除
                //noinspection ResultOfMethodCallIgnored
                file.delete();// 删除已有文件
                file = new File(absoluteCachePath, filename + ".jpg");
            }

            try {
                Bitmap bitmap = getVideoThumbnailBitmap(context, uri);
                if (bitmap != null) {
                    OutputStream outStream = new FileOutputStream(file);
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outStream);
                    if (info != null && info.length >= 2) {
                        info[0] = bitmap.getWidth();
                        info[1] = bitmap.getHeight();
                    }
                    outStream.flush();
                    outStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return file;

        }
        return null;
    }

    public static Bitmap getVideoThumbnailBitmap(Context context, Uri uri) {
        Bitmap bitmap = null;
        try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                if (ContentResolver.SCHEME_FILE.equals(uri.getScheme())) {
                    String filePath = getPath(context, uri);
                    if (!TextUtils.isEmpty(filePath)) {
                        bitmap = ThumbnailUtils.createVideoThumbnail(filePath, MediaStore.Video.Thumbnails.MINI_KIND);
                    }
                } else {
                    bitmap = context.getContentResolver().loadThumbnail(uri, new Size(300, 300), null);
                }
            } else {
                String filePath = getPath(context, uri);
                if (!TextUtils.isEmpty(filePath)) {
                    bitmap = ThumbnailUtils.createVideoThumbnail(filePath, MediaStore.Video.Thumbnails.MINI_KIND);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bitmap;
    }

    //获取视频时长,单位秒
    public static int getVideoDuration(Context context, Uri uri) {
        if (ContentResolver.SCHEME_FILE.equals(uri.getScheme())) {
            return getVideoDuration("file://" + uri.getPath());
        } else {
            Cursor cursor = null;
            try {
                cursor = context.getContentResolver().query(uri, null, null, null, null);
                if (cursor != null && cursor.moveToFirst()) {
                    int index = cursor.getColumnIndex(MediaStore.Video.VideoColumns.DURATION);
                    if (index >= 0) {
                        return cursor.getInt(index) / 1000;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                if(cursor!= null)cursor.close();
            }
        }
        return 0;
    }

    //获取视频时长,单位秒
    public static int getVideoDuration(String url) {
        MediaPlayer mediaPlayer = new MediaPlayer();
        int duration = 0;
        try {
            mediaPlayer.setDataSource(url);
            mediaPlayer.prepare();
            duration = mediaPlayer.getDuration();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return duration / 1000;
    }

    //删除该文件或者该文件夹下的文件，不递归
    public static void delete(File file) {
        try {
            if (file == null || !file.exists()) {
                return;
            }
            if (file.isFile()) {
                file.delete();
                return;
            }

            File[] fileList = file.listFiles();
            if (fileList == null) {
                return;
            }

            for (File f : fileList) {
                if (f.isFile()) {
                    f.delete();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 保存方法
     */
    public static File saveBitmapToFile(Bitmap bitmap, String path) {
        File f = new File(path);
        if (f.exists()) {
            f.delete();
        }
        FileOutputStream out = null;
        try {
            out = new FileOutputStream(f);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out);
            return f;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    public static String getFileSuffix(File file) {
        String end = file.getName().substring(file.getName().lastIndexOf(".") + 1, file.getName().length());
        if (!TextUtils.isEmpty(end)) {
            return end.toLowerCase(Locale.ROOT);
        }
        return "";
    }

    public static String getUriSuffix(Context context, Uri uri) {
        if (ContentResolver.SCHEME_FILE.equals(uri.getScheme())) {
            File file = new File(uri.getPath());
            if (file != null) {
                return getFileSuffix(file);
            }
        } else {
            Cursor cursor = null;
            try {
                cursor = context.getContentResolver().query(uri, new String[]{MediaStore.MediaColumns.MIME_TYPE}, null, null, null);
                if (cursor != null) {
                    if (cursor.moveToFirst()) {
                        final int index = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.MIME_TYPE);
                        return cursor.getString(index);
                    }

                }
            } finally {
                if (cursor != null) cursor.close();
            }
        }
        return "";
    }

    public static ContentValues getVideoContentValues(File paramFile, long paramLong) {
        ContentValues localContentValues = new ContentValues();
        localContentValues.put("title", paramFile.getName());
        localContentValues.put("_display_name", paramFile.getName());
        localContentValues.put("mime_type", "video/" + getFileSuffix(paramFile));
        localContentValues.put("datetaken", Long.valueOf(paramLong));
        localContentValues.put("date_modified", Long.valueOf(paramLong));
        localContentValues.put("date_added", Long.valueOf(paramLong));
        localContentValues.put("_data", paramFile.getAbsolutePath());
        localContentValues.put("_size", Long.valueOf(paramFile.length()));
        return localContentValues;
    }

    public static boolean createFolder(String folderPath) {
        if (!TextUtils.isEmpty(folderPath)) {
            File folder = new File(folderPath);
            return createFolder(folder);
        }
        return false;
    }

    public static boolean createFolder(File targetFolder) {
        if (targetFolder.exists()) {
            if (targetFolder.isDirectory()) {
                return true;
            }
            //noinspection ResultOfMethodCallIgnored
            targetFolder.delete();
        }
        return targetFolder.mkdirs();
    }

    /**
     * @param srcFile
     * @param index       从0开始
     * @param splitLength
     * @param tmpFolder
     * @return
     */
    public static File splitFile(File srcFile, long index, long splitLength, String tmpFolder) {
        RandomAccessFile raf = null;
        RandomAccessFile out = null;
        File tempFile = new File(tmpFolder, srcFile.getName().split("\\.")[0] + ".tmp");
        if (tempFile.exists()) {
            tempFile.delete();
        }
        createFolder(tmpFolder);
        try {
            raf = new RandomAccessFile(srcFile, "r");
            long offSet = splitLength * index;
            long end = Math.min(splitLength * (index + 1) - 1, srcFile.length() - 1);
            out = new RandomAccessFile(tempFile, "rw");
            byte[] buffer = new byte[1024 * 4];
            int len;
            raf.seek(offSet);
            while (raf.getFilePointer() <= end && (len = raf.read(buffer)) != -1) {
                out.write(buffer, 0, len);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (raf != null) {
                    raf.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return tempFile;
    }

    public static long getTotalSizeForFileDir(File file) {
        long totalSize = 0;
        try {
            if (file.exists()) {
                File[] listFiles = file.listFiles();
                if (listFiles == null) {
                    totalSize = file.length();
                } else {
                    long size;
                    for (File file2 : listFiles) {
                        if (file2.isDirectory()) {
                            size = getTotalSizeForFileDir(file2);
                        } else {
                            size = file2.length();
                        }
                        totalSize += size;
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return totalSize;
    }


    /**
     * 生成临时文件
     *
     * @param url
     * @param filePath
     * @return
     */
    public static File createTempFile(String url, String filePath) {
        String md5 = MD5.convert(url) + ".temp";
        return FileUtils.fileIsExists(filePath + "/" + md5);
    }

    /**
     * 判断指定路径的 文件 是否存在，不存在创建文件
     *
     * @param filePath
     * @return
     */
    public static File fileIsExists(String filePath) {
        File file = new File(filePath);
        try {
            if (!file.isFile()) {
                file.createNewFile();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return file;
    }

    /**
     * 获取临时文件 名称
     *
     * @param url
     * @param filePath
     * @return
     */
    public static File getTempFile(String url, String filePath) {
        String md5 = MD5.convert(url) + ".temp";
        return new File(filePath + "/" + md5);
    }

    /**
     * 根据 url 重命名 指定的 文件（路径）
     *
     * @param url
     * @param oldPath 路径
     */
    public static boolean reNameFile(String url, String oldPath) {
        String fileName = getFileName(url, null);
        File oldFile = new File(oldPath);
        return oldFile.renameTo(new File(oldFile.getParent(), fileName));
    }

    /**
     * 根据 url 和 指定的路径，获取文件
     *
     * @param url
     * @param path
     * @return
     */
    public static File getFile(String url, String path, String fileName) {
        String finalFileName = getFileName(url, fileName);
        return new File(path, finalFileName);
    }

    public static File getFile(String url, String path) {
        return getFile(url, path, null);
    }

    /**
     * "https://img.bosszhipin.com/v2/upload/bosshi/android/hi_prod_2.32.0.apk?t=1645523789945";
     * 根据 url 生成 文件名
     *
     * @param url
     */
    private static String getFileName(String url, String fileName) {
        String finalFileName;
        String md5Name = MD5.convert(url);
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?", -1));
        }
        if (TextUtils.isEmpty(fileName)) {
            try {
                int startIndex = url.lastIndexOf("/");
                int endIndex = url.lastIndexOf(".");
                fileName = url.substring(startIndex, endIndex);
            } catch (Exception e) {

            }
        }
        finalFileName = fileName + "-" + md5Name + url.substring(url.lastIndexOf("."));
        return finalFileName;
    }

    public static String getFileFormat(String url) {
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        return url.substring(url.lastIndexOf(".") + 1);
    }

    public static String getFileUrl(String url) {
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        return url;
    }

    /**
     * 获取文件名称
     *
     * @param context
     * @param uri
     * @return https://developer.android.com/training/secure-file-sharing/retrieve-info?hl=zh-cn#java
     */
    public static String getFileNameFromUri(final Context context, final Uri uri) {
        if (ContentResolver.SCHEME_FILE.equals(uri.getScheme())) {
            return new File(uri.getPath()).getName();
        } else {
            Cursor cursor = null;
            try {
                cursor = context.getContentResolver().query(uri, new String[]{MediaStore.MediaColumns.DISPLAY_NAME}, null, null, null);
                if (cursor != null) {
                    if (cursor.moveToFirst()) {
                        final int index = cursor.getColumnIndexOrThrow(MediaStore.MediaColumns.DISPLAY_NAME);
                        return cursor.getString(index);
                    }

                }
            } finally {
                if (cursor != null) cursor.close();
            }
        }
        return "";
    }

    /**
     * 获取文件的大小
     *
     * @param context
     * @param uri
     * @return https://developer.android.com/training/secure-file-sharing/retrieve-info?hl=zh-cn#java
     */
    public static long getFileSizeFromUri(final Context context, final Uri uri) {
        if (ContentResolver.SCHEME_FILE.equals(uri.getScheme())) {
            return new File(uri.getPath()).length();
        } else {
            try {
                ParcelFileDescriptor descriptor = context.getContentResolver().openFileDescriptor(uri, "r");
                return descriptor != null ? descriptor.getStatSize() : -1L;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return 0;
    }

    public static String getFileLocalUrl(File file) {
        StringBuilder stringBuilder = new StringBuilder("file://");
        if (file != null && file.exists()) {
            stringBuilder.append(file.getAbsolutePath());
            return stringBuilder.toString();
        }
        return "";
    }

    public static String getPath(final Context context, final Uri uri) {

        final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;

        // DocumentProvider
        if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
            // ExternalStorageProvider
            if (isExternalStorageDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];

                if ("primary".equalsIgnoreCase(type)) {
                    return Environment.getExternalStorageDirectory() + "/" + split[1];
                }

                // TODO handle non-primary volumes
            }
            // DownloadsProvider
            else if (isDownloadsDocument(uri)) {

                final String id = DocumentsContract.getDocumentId(uri);
                final Uri contentUri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"), Long.valueOf(id));

                return getDataColumn(context, contentUri, null, null);
            }
            // MediaProvider
            else if (isMediaDocument(uri)) {
                final String docId = DocumentsContract.getDocumentId(uri);
                final String[] split = docId.split(":");
                final String type = split[0];

                Uri contentUri = null;
                if ("image".equals(type)) {
                    contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
                } else if ("video".equals(type)) {
                    contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
                } else if ("audio".equals(type)) {
                    contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
                }

                final String selection = "_id=?";
                final String[] selectionArgs = new String[]{
                        split[1]
                };

                return getDataColumn(context, contentUri, selection, selectionArgs);
            }
        }
        // MediaStore (and general)
        else if ("content".equalsIgnoreCase(uri.getScheme())) {

            // Return the remote address
            if (isGooglePhotosUri(uri))
                return uri.getLastPathSegment();

            return getDataColumn(context, uri, null, null);
        }
        // File
        else if ("file".equalsIgnoreCase(uri.getScheme())) {
            return uri.getPath();
        }

        return null;
    }

    /**
     * *
     * Get the value of the data column for this Uri. This is useful for
     * MediaStore Uris, and other file-based ContentProviders.
     *
     * @param context       The context.
     * @param uri           The Uri to query.
     * @param selection     (Optional) Filter used in the query.
     * @param selectionArgs (Optional) Selection arguments used in the query.
     * @return The value of the _data column, which is typically a file path.
     */
    public static String getDataColumn(Context context, Uri uri, String selection,
                                       String[] selectionArgs) {

        Cursor cursor = null;
        final String column = "_data";
        final String[] projection = {
                column
        };

        try {
            cursor = context.getContentResolver().query(uri, projection, selection, selectionArgs,
                    null);
            if (cursor != null && cursor.moveToFirst()) {
                final int index = cursor.getColumnIndexOrThrow(column);
                return cursor.getString(index);
            }
        } finally {
            if (cursor != null)
                cursor.close();
        }
        return null;
    }


    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is ExternalStorageProvider.
     */
    public static boolean isExternalStorageDocument(Uri uri) {
        return "com.android.externalstorage.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is DownloadsProvider.
     */
    public static boolean isDownloadsDocument(Uri uri) {
        return "com.android.providers.downloads.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is MediaProvider.
     */
    public static boolean isMediaDocument(Uri uri) {
        return "com.android.providers.media.documents".equals(uri.getAuthority());
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is Google Photos.
     */
    public static boolean isGooglePhotosUri(Uri uri) {
        return "com.google.android.apps.photos.content".equals(uri.getAuthority());
    }

    public static boolean uriFileExist(final Context context, final Uri uri) {
        if (uri == null) {
            return false;
        }
        try {
            final boolean isKitKat = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;
            // DocumentProvider
            if (isKitKat && DocumentsContract.isDocumentUri(context, uri)) {
                DocumentFile documentFile = DocumentFile.fromSingleUri(context, uri);
                return documentFile.exists();
            }
            // MediaStore (and general)
            else if (ContentResolver.SCHEME_CONTENT.equalsIgnoreCase(uri.getScheme())) {
                // Return the remote address
                if (isGooglePhotosUri(uri))
                    return !TextUtils.isEmpty(uri.getLastPathSegment());

                return !TextUtils.isEmpty(getDataColumn(context, uri, null, null));
            }
            // File
            else if (ContentResolver.SCHEME_FILE.equalsIgnoreCase(uri.getScheme())) {
                return new File(uri.getPath()).exists();
            }
        } catch (Exception e) {
        }

        return false;
    }
}
