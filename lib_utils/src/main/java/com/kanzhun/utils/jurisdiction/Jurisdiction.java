package com.kanzhun.utils.jurisdiction;

public class Jurisdiction {
    private int identity;// 功能标识
    private int visible;// 是否显示，0-不显示，1-显示
    private String ext;//扩展属性

    public void params(Jurisdiction jurisdiction) {
        setExt(jurisdiction.getExt());
        setVisible(jurisdiction.getVisible());
        setIdentity(jurisdiction.getIdentity());
    }

    public Object getJurisdictionInfo() {
        return null;
    }

    public int getIdentity() {
        return identity;
    }

    public void setIdentity(int identity) {
        this.identity = identity;
    }

    public int getVisible() {
        return visible;
    }

    public void setVisible(int visible) {
        this.visible = visible;
    }

    public String getExt() {
        return ext;
    }

    public void setExt(String ext) {
        this.ext = ext;
    }
}
