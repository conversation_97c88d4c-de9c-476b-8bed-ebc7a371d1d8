package com.kanzhun.net;

import static com.kanzhun.mms.utils.TWLException.MMS_SERVER_PING_NO_FIND;

import android.os.SystemClock;
import android.util.Log;

import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.StringUtils;
import com.kanzhun.mms.utils.TWLException;
import com.twl.net.NetUtils;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * Created by yuch<PERSON><PERSON><PERSON> on 2017/4/22.
 */

public class TWLTraceRoute {
    private static final String TAG = "TWLTraceRoute";
    private static boolean gIsPingNoFind = false;
    public static final String COMMAND_SH = "sh";
    public static final String COMMAND_LINE_END = "\n";
    public static final String COMMAND_EXIT = "exit\n";


    private static final TWLTraceRoute gTWLTraceRoute = new TWLTraceRoute();

    private TWLTraceRoute() {
    }

    public static TWLTraceRoute getTWLTraceRoute() {
        return gTWLTraceRoute;
    }

    private static void close(Closeable close) {
        if (close != null) {
            try {
                close.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private String execPing(String command) {
        Process process = null;
        BufferedReader reader = null;
        DataOutputStream dos = null;
        StringBuilder content = StringUtils.obtainStringBuilder();
        try {
            process = Runtime.getRuntime().exec(COMMAND_SH);
            if (process != null) {
                dos = new DataOutputStream(process.getOutputStream());
                dos.write(command.getBytes());
                dos.writeBytes(COMMAND_LINE_END);
                dos.flush();
                dos.writeBytes(COMMAND_EXIT);
                dos.flush();

                process.waitFor();
                reader = new BufferedReader(new InputStreamReader(
                        process.getInputStream()));
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            } else {
                ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_PING_NO_FIND, new Exception("exec ping return null")));
                gIsPingNoFind = true;
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        } finally {
            close(dos);
            close(reader);
            if (process != null) {
                try {
                    process.destroy();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return content.toString();
    }

    /**
     * 执行traceroute功能
     *
     * @param host
     */
    public void traceRoute(String host, int port) {
        try {
            NetUtils.execTraceroute(host, port);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    /**
     * 判断IP是否能ping通
     *
     * @param ip
     * @return
     */
    public boolean isCanuse(String ip) {
        PingInfo info = getPingInfo(ip);
        BLog.d(TAG, "isCanuse() called with: ip = [%s]", info);
        return info == null || info.loss < 100;
    }

    /**
     * 等待网络畅通
     *
     * @param host        等待的host
     * @param maxWaitTime 最长等待时间
     */
    public boolean waitNetUnblocked(String host, long maxWaitTime) {
        long startTime = SystemClock.elapsedRealtime();
        do {
            PingInfo info = getPingInfo(host);
            if (info != null) {
                BLog.d(TAG, "PingInfo is [%s]", info);
                if (info.loss == 0.0) {
                    return true;
                }
            } else {
                BLog.d(TAG, "PingInfo is null");
            }
            if (SystemClock.elapsedRealtime() - startTime < maxWaitTime) {
                try {
                    Thread.sleep(5 * 1000);
                } catch (InterruptedException e) {
                    return false;
                }
            } else {
                return false;
            }
            if (gIsPingNoFind) {
                return false;
            }
        } while (true);
    }

    /**
     * 返回ping丢失百分比, 如果发生异常默认返回丢失率为0
     *
     * @param ip
     * @return
     */
    public PingInfo getPingInfo(String ip) {
        PingInfo pingInfo = null;
        try {
            if (gIsPingNoFind) {
                BLog.e(TAG, "getPacketLossSize: Ping no find.");
                return pingInfo;
            }
            String content = execPing(String.format("ping -c 2 -i 1 -w 8  %s", ip));
            if (0 < content.length()) {
                pingInfo = PingInfo.build(content);
                if (pingInfo != null && pingInfo.loss == 0.0) {
                    Log.d(TAG, content);
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return pingInfo;
    }
}
