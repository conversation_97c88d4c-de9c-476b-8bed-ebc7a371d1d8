package com.kanzhun.mms.service.mqtt;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.wifi.WifiInfo;
import android.os.Message;
import android.os.PowerManager;
import android.os.RemoteException;
import android.os.SystemClock;
import android.telephony.TelephonyManager;

import com.kanzhun.mms.ConnectionInfo;
import com.kanzhun.mms.IMMServicePushFilter;
import com.kanzhun.mms.MMSMessage;
import com.kanzhun.mms.ServerResponse;
import com.kanzhun.mms.common.CancelHelper;
import com.kanzhun.mms.common.MMSMessageFactory;
import com.kanzhun.mms.common.MqttOptions;
import com.kanzhun.mms.common.UserInfo;
import com.kanzhun.mms.service.AppStatus;
import com.kanzhun.mms.service.MMSServiceNative;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.MqttUtil;
import com.kanzhun.mms.utils.StringUtils;
import com.kanzhun.mms.utils.TWLException;
import com.kanzhun.mms.utils.ThreadManager;
import com.kanzhun.net.TWLTraceRoute;

import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.internal.wire.MqttPubAck;
import org.eclipse.paho.client.mqttv3.internal.wire.MqttWireMessage;
import org.eclipse.paho.client.mqttv3.logging.LoggerFactory;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;

import static android.content.Context.CONNECTIVITY_SERVICE;
import static android.content.Context.POWER_SERVICE;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_CODE_FAILED_AUTHENTICATION;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_FAILED;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_IN;
import static com.kanzhun.mms.common.MMSConstants.CONECTION_SUCCESS;
import static com.kanzhun.mms.service.AppStatus.sNetChanageTime;
import static com.kanzhun.mms.utils.TWLException.MMS_SERVER_SEND_MESSAGE;
import static org.eclipse.paho.client.mqttv3.MqttException.REASON_CODE_CLIENT_TIMEOUT;
import static org.eclipse.paho.client.mqttv3.MqttException.REASON_CODE_MAX_INFLIGHT;
import static org.eclipse.paho.client.mqttv3.MqttException.REASON_CODE_READ_TIMEOUT;
import static org.eclipse.paho.client.mqttv3.MqttException.REASON_CODE_WRITE_TIMEOUT;

/**
 * Created by yuchaofei on 2017/3/6.
 * 连接，发送，重连接，断开 串行执行
 */

class ConnectionHandler extends ThreadManager.MMSHandler implements MqttCallbackExtended, ThreadManager.OnSuicideListener {
    private static final String TAG = "ConnectionHandler";
    private static final String UNBLOCKED_KEY = "Unblocked2";
    private static final long DEFAULT_DELTA = 2000;//超时delta时间
    /**
     * 发送重连，单次等待网络恢复最长时间；
     * 所以一条消息最坏情况下最长等待时间 = (重试次数) * WAIT_NET_REC_TIME + 32s（连接等待） * 重试次数 + 32s(发送等待) * 重试次数 ;
     * 如果重试3次，最长需要等待的时间是6m6s；
     */
//    private static final long WAIT_NET_REC_TIME = 1 * 60 * 1000;
    private static final long WAIT_NET_REC_TIME = 5 * 1000;

    public static final int HANDE_CONNECTION_CONNECT = 0;
    public static final int HANDE_CONNECTION_SEND = 1;
    public static final int HANDE_CONNECTION_DISCONNECT = 2;
    public static final int HANDE_CONNECTION_RECONNECT = 3;

    public static final int HANDE_INITIALIZATION = 10;


    private Context mContext;
    private MqttClient mMqttClient;
    private volatile UserInfo mUserInfo;
    private IMMServicePushFilter mServicePushFilter;
    private MqttOptions mMqttOptions;

    private PowerManager.WakeLock mWakeLock = null;

    private ReconnectHelper mReconnectHelper = new ReconnectHelper();
    private CancelHelper mCancelHelper = new CancelHelper();
    private PingHelper mPingHelper;
    private long mConnectSuccessTime = -1;//订阅完成时间

    private Object mConnectLoack = new Object();

    private int mCurrentWhat = -1;
    private ConnectionInfo mConnectionInfo = new ConnectionInfo();

    public ConnectionHandler(Context context, MqttOptions mqttOptions) {
        super(ThreadManager.getConnectThreadLooper());
        mContext = context;
        mMqttOptions = mqttOptions;
        mPingHelper = new PingHelper();
        ThreadManager.monitorHandler(this, this);
        sendEmptyMessage(HANDE_INITIALIZATION);
    }

    public void cancelMessage(int id) {
        mCancelHelper.addCancel(id);
    }

    @Override
    public void handleMessage(Message msg) {
        try {
            acquireWakeLock();
            mCurrentWhat = msg.what;
            switch (msg.what) {
                case HANDE_INITIALIZATION:
                    BLog.d(TAG, "====================Init start=======================");
                    init();
                    BLog.d(TAG, "====================Init end  =======================");
                    break;
                case HANDE_CONNECTION_CONNECT:
                    BLog.d(TAG, "====================Connect start=======================");
                    UserInfo userInfo = (UserInfo) msg.obj;
                    if (userInfo == null) {
                        userInfo = mUserInfo;
                    }
                    if (userInfo != null) {
                        connect(userInfo);
                    }
                    BLog.d(TAG, "====================Connect end  =======================");
                    break;
                case HANDE_CONNECTION_SEND:
                    BLog.d(TAG, "====================Send start=======================");
                    MMSMessage mmsMessage = (MMSMessage) msg.obj;
                    send(mmsMessage);
                    checkCancelHelper();
                    BLog.d(TAG, "====================Send end  =======================");
                    break;
                case HANDE_CONNECTION_DISCONNECT:
                    BLog.d(TAG, "====================Active disconnect start=======================");
                    activeDisconnect();
                    BLog.d(TAG, "====================Active disconnect end  =======================");
                case HANDE_CONNECTION_RECONNECT:
                    BLog.d(TAG, "====================Reconnect start=======================");
                    reconnect();
                    BLog.d(TAG, "====================Reconnect end  =======================");
                    break;
            }
        } catch (RemoteException e) {
            e.printStackTrace();
        } catch (Throwable e) {
            throw e;
        } finally {
            mCurrentWhat = -1;
            releaseWakeLock();
        }
    }

    /**
     * 检查取消队列
     */
    private void checkCancelHelper() {
        if (!hasMessages(HANDE_CONNECTION_SEND)) {
            mCancelHelper.clear();
        }
    }

    /**
     * 预加载
     */
    private void init() {
        try {
            StringUtils.openCache();
            LoggerFactory.getLogger(LoggerFactory.MQTT_CLIENT_MSG_CAT, MqttAsyncClient.class.getName());
        } catch (Throwable exception) {
            BLog.d(TAG, "init [%s]", exception.toString());
        }
    }

    /**
     * 连接
     *
     * @param userInfo 用户信息
     * @throws RemoteException
     */
    private void connect(UserInfo userInfo) throws RemoteException {
        BLog.d(TAG, "connect() called with: userInfo = [%s], topic = [%s], isConnecd = [%b]", userInfo, mMqttOptions.getTopic(), isConnected());
        MqttClient mqttClient = null;
        try {
            if (userInfo.equals(mUserInfo) && isConnected()) {
                mServicePushFilter.onConnected(CONECTION_SUCCESS);
                return;
            } else if (isConnected()) {
                disconnect();
            }
            mServicePushFilter.onConnected(CONECTION_IN);
            mUserInfo = userInfo;
            onConnect();
            String url = IPManager.getInstance().getServerUrl();
            BLog.d(TAG, url);
            mqttClient = new MqttClient(url, userInfo.getClientId(),
                    new MemoryPersistence(), new AlarmPingSender(mContext.getApplicationContext(), mPingHelper));
            mqttClient.setCallback(this);
            mqttClient.setTimeToWait(mMqttOptions.getConnectionTimeout() * 1000 + DEFAULT_DELTA);
            mConnectSuccessTime = -1;
            long time = SystemClock.elapsedRealtime();
            mMqttClient = mqttClient;
            mqttClient.connect(getConnectOptions(userInfo,changeIp));
            mConnectionInfo.update(time);

//            mqttClient.subscribe(mMqttOptions.getTopic(), mMqttOptions.getQos());
            mConnectSuccessTime = SystemClock.elapsedRealtime() - time;
            BLog.d(TAG, "Subscribe Time = [%d]", mConnectSuccessTime);

            sendIdentifyData();

            mServicePushFilter.onConnected(CONECTION_SUCCESS);
            mReconnectHelper.onConnected();
        } catch (Exception e) {
            mServicePushFilter.onConnected(CONECTION_FAILED);
            handleNetException(e, false);
            BLog.printErrStackTrace(TAG, e, "connect error");
            internalClose(mqttClient);
            if (MqttUtil.isChanageType(e)) {
                IPManager.getInstance().onTimeOut();
            }
            IPManager.getInstance().checkHttp();
        }
    }

    private void sendIdentifyData() throws RemoteException, MqttException {
        byte[] data = mServicePushFilter.getIdentifyData();
        if (data != null) {
            MqttMessage mqttMessage = new MqttMessage(data);
            internalSend(mqttMessage);
        }
    }

    /**
     * 发送消息
     *
     * @param message 消息
     */
    private void send(MMSMessage message) throws RemoteException {
        try {
            if (mCancelHelper.containsAndRemove(message.getId())) {//如果任务被取消，直接返回
                return;
            }
            ServerResponse serverResponse = null;
            if (mUserInfo != null) {//如果用户为空
                MqttMessage mqttMessage = new MqttMessage(message.getData());
                mqttMessage.setId(message.getId());
                mqttMessage.setQos(mMqttOptions.getQos());
                int tryCount = 0;
                if (message.getTryCount() == 0) {//不重试，不重连
                    serverResponse = internalSend(mqttMessage, false);
                } else {
                    while (tryCount++ < message.getTryCount()) {
                        if (mCancelHelper.containsAndRemove(message.getId())) {//如果任务被取消，直接返回
                            return;
                        }
                        if (!isConnected()) {
                            try {
                                IPManager.getInstance().setReconnect(true);
                                resendConnWait(tryCount != 1);//重连之前先check网络，最多等1min，避免无谓的失败
                                connect(mUserInfo);
                            } catch (Throwable e) {
                                BLog.printErrStackTrace(TAG, e, "send connect error, tryCount = [%d]", tryCount);
                            } finally {
                                IPManager.getInstance().setReconnect(false);
                            }
                        }
                        try {
                            if ((serverResponse = internalSend(mqttMessage, tryCount != 1)) != null) {
                                break;
                            }
                        } catch (MqttException e) {
                            switch (e.getReasonCode()) {//如果发送和接受超时，不等待直接重连
                                case REASON_CODE_READ_TIMEOUT:
                                case REASON_CODE_WRITE_TIMEOUT:
                                case REASON_CODE_CLIENT_TIMEOUT:
                                case REASON_CODE_MAX_INFLIGHT:
                                    if (isConnected()) {
                                        disconnect();
                                        ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_SEND_MESSAGE, e));
                                    }
                                    break;
                            }
                            BLog.printErrStackTrace(TAG, e, "send error, tryCount = [%d]", tryCount);
                        }
                    }
                }
                MessageStatistics.recordMessage(serverResponse != null);
            } else {
                BLog.e(TAG, "Send error : Userinfo is null!");
                if (message.getTryCount() != 0) {
                    ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_SEND_MESSAGE,
                            new Exception("Send error : Userinfo is null!")), true);
                }
            }
            mServicePushFilter.onDelivered(message.getId(), serverResponse);
        } catch (Throwable e) {
            mServicePushFilter.onDelivered(message.getId(), MMSMessageFactory.createServerResponse(false));
        }
    }

    /**
     * 没有网络的情况，最多等待1min
     *
     * @param isOnlineCheck 有网情况是否检查
     */
    private void resendConnWait(boolean isOnlineCheck) {
        try {
            int tag = 0;
            do {
                tag++;
                if (!AppStatus.gIsOnline) {//没有网络的情况等待1分钟，等待网络恢复避不必要的尝试失败
                    synchronized (mConnectLoack) {
                        long time = SystemClock.elapsedRealtime();
                        if (!AppStatus.gIsOnline) {
                            mConnectLoack.wait(WAIT_NET_REC_TIME);
                        }
                        BLog.d(TAG, "Offline Wait Time = [%d], isOnline = [%b]", (SystemClock.elapsedRealtime() - time), AppStatus.gIsOnline);
                    }
                } else if (isOnlineCheck && isWaitUnblocked()) {
                    long time = SystemClock.elapsedRealtime();
                    if (!TWLTraceRoute.getTWLTraceRoute()
                            .waitNetUnblocked(IPManager.getInstance().getHost(), WAIT_NET_REC_TIME)) {//等待路由通畅，最长等待时间1min
                        if (!isOnline()) {
                            tag--;
                        }
                    }
                    BLog.d(TAG, "NetUnblocked Wait Time = [%d], isOnline = [%b]", (SystemClock.elapsedRealtime() - time), AppStatus.gIsOnline);
                }
            } while (tag++ <= 0);
        } catch (Exception e) {
        }
    }

    /**
     * 发送消息，超时使用长时间check
     *
     * @param mqttMessage
     * @return
     * @throws MqttException
     */
    private ServerResponse internalSend(MqttMessage mqttMessage) throws MqttException {
        return internalSend(mqttMessage, true);
    }

    /**
     * 发送消息设置超时check类型
     *
     * @param mqttMessage
     * @param isLongCheck
     * @return
     * @throws MqttException
     */
    private ServerResponse internalSend(MqttMessage mqttMessage, boolean isLongCheck) throws MqttException {
        ServerResponse response = null;
        MqttClient mqttClient = mMqttClient;
        if (mqttClient != null
                && mqttClient.isConnected()) {
            try {
                if (mConnectSuccessTime * mMqttOptions.getQos() >= AlarmPingSender.MIN_WRITE_TIME_OUT) {//如果订阅完成时间>最小超时时间，默认使用longcheck
                    isLongCheck = true;
                }
                AlarmPingSender.addTimeOutCheck(isLongCheck);//添加连接超时检查
                MqttWireMessage mqttWireMessage = mqttClient.publish(mMqttOptions.getTopic(), mqttMessage);
                if (mqttWireMessage instanceof MqttPubAck) {
                    MqttPubAck pubAck = (MqttPubAck) mqttWireMessage;
                    response = MMSMessageFactory.createServerResponse(true, pubAck.getServerId(), pubAck.getSequence());
                }else {
                    response = MMSMessageFactory.createServerResponse(true, 0, 0);
                }
            } catch (MqttException e) {
                throw e;
            } finally {
                AlarmPingSender.removeTimeOutCheck();//删除连接超时检查
            }
        }
        return response;
    }

    /**
     * 主动关闭连接
     */
    private void activeDisconnect() throws RemoteException {
        mUserInfo = null;
        disconnect();
        mServicePushFilter.onConnectionLost(0);
    }

    /**
     * 关闭连接
     */
    private void disconnect() {
        MqttClient client = mMqttClient;
        mMqttClient = null;
        internalClose(client);
    }

    private static void internalClose(MqttClient client) {
        if (client != null) {
            try {
                long time = SystemClock.elapsedRealtime();
                client.disconnectForcibly(1000, 1000);//强制关闭，2s超时
                BLog.d(TAG, "disconnectForcibly elapsedTime=[%d]", (SystemClock.elapsedRealtime() - time));
            } catch (Throwable e) {
            } finally {
                try {
                    client.close();
                } catch (Exception e) {
                }
            }
        }
    }

    /**
     * 执行重新连接操作
     *
     * @throws RemoteException
     */
    private void reconnect() throws RemoteException {
        if (isConnected()) {
            return;
        }
        UserInfo userInfo = mUserInfo;
        if (userInfo != null) {
            connect(userInfo);
        }
    }

    private MqttConnectOptions getConnectOptions(UserInfo userInfo,int position) {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setUserName(userInfo.getUserName());
        options.setPassword(userInfo.getPassword().toCharArray());
        options.setCleanSession(mMqttOptions.isCleanSession());
        options.setConnectionTimeout(mMqttOptions.getConnectionTimeout());
        options.setKeepAliveInterval(mMqttOptions.getKeepAliveInterval());
        options.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1);
        options.setSocketFactory(MMSServiceNative.getServerProfile().getServerInfo(position).getSocketFactory());
        return options;
    }

    /**
     * 是否已经连接
     *
     * @return
     */
    public boolean isConnected() {
        MqttClient mqttClient = mMqttClient;
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * 连接时调用
     */
    private void onConnect() {
        mReconnectHelper.onReconnect();//增加重连次数
        removeMessages(HANDE_CONNECTION_RECONNECT);//删除重连任务
    }

    @Override
    public void connectionLost(Throwable cause) {
        BLog.printErrStackTrace(TAG, cause, "connectionLost");
        handleNetException(cause, true);
        MessageStatistics.recordLost();
    }

    /**
     * mqtt官方回调，收到新消息
     *
     * @param topic   name of the topic on the message was published to
     * @param message the actual message.
     * @throws Exception
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        if (mServicePushFilter != null
                && mUserInfo != null && !hasMessages(HANDE_CONNECTION_DISCONNECT)) {//如果退出了丢弃消息
            MMSMessage mmsMessage = null;
            try {
                byte[] bytes = message.getPayload();
                BLog.d(TAG, "messageArrived data size = [%d]", bytes.length);
                mmsMessage = MMSMessageFactory.createMqttMessage(bytes);
                mServicePushFilter.onPush(mmsMessage);
            } catch (RemoteException e) {
                e.printStackTrace();
            } finally {
                if (mmsMessage != null) {
                    MMSMessage.relaseMessage(mmsMessage);
                }
            }
        } else {
            BLog.e(TAG, "Discard message on messageArrived. mUserInfo = [%s], hasDisconnect = [%b]", mUserInfo, hasMessages(HANDE_CONNECTION_DISCONNECT));
        }
        MessageStatistics.recordReceive();
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
    }

    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
    }

    public void setServicePushFilter(IMMServicePushFilter servicePushFilter) {
        mServicePushFilter = servicePushFilter;
    }

    private int changeIp = 0;
    /**
     * 处理连接异常与连接丢失的情况
     *
     * @param cause  异常
     * @param isLost 是否是丢失连接
     */
    private void handleNetException(Throwable cause, boolean isLost) {
        BLog.d(TAG, "handleNetException!,isLost = [%b], cause = [%s]", isLost, cause);
        changeIp();

        boolean isFailAuth = MqttUtil.isAccountErrorException(cause);
        if (isFailAuth) {
            mUserInfo = null;
        }
        if (mServicePushFilter != null) {
            try {
                if (isLost || isFailAuth) {
                    mServicePushFilter.onConnectionLost(isFailAuth ? CONECTION_CODE_FAILED_AUTHENTICATION : 0);
                }
            } catch (RemoteException e) {
                e.printStackTrace();
            }
        }
        if (!isFailAuth && isOnline()) {//有网络时才重试
            BLog.d(TAG, "Try reconnect!");
            this.sendEmptyMessageDelayed(HANDE_CONNECTION_RECONNECT, mReconnectHelper.getDelayedTime());
        } else {
            BLog.d(TAG, "No retry required! isForeground=[%b], mNetworkInfo=[%b]", AppStatus.isForeground(), mNetworkInfo != null);
            if (!isFailAuth && (AppStatus.isForeground() && mNetworkInfo != null)) {
                //前台&有网络，但是不稳定的情况下，需要重试
                this.sendEmptyMessageDelayed(HANDE_CONNECTION_RECONNECT, mReconnectHelper.getDelayedTime());
            }
        }
    }

    private void changeIp() {
        if (changeIp == 0){
            changeIp = 1;
        }else {
            changeIp = 0;
        }
        IPManager.position = changeIp;
    }

    private NetworkInfo mNetworkInfo;

    /**
     * 是否有网络
     *
     * @return
     */
    public boolean isOnline() {
        boolean ret = false;
        try {
            mNetworkInfo = null;
            ConnectivityManager cm = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = cm.getActiveNetworkInfo();
            if (networkInfo != null) {//没有开启网络时，返回null
                mNetworkInfo = networkInfo;
                String typeName = networkInfo.getTypeName(); // WIFI/MOBILE
                if (typeName != null) {
                    typeName = typeName.toLowerCase();
                    AppStatus.gIsMobileNet = !typeName.equals("wifi");
                }
                boolean isAvailable = networkInfo.isAvailable();
                boolean isConnected = networkInfo.isConnected();
                BLog.d(TAG, "net type = [%s], subType = [%s], isAvailable=[%b], isConnected=[%b]", typeName, networkInfo.getSubtypeName(), isAvailable, isConnected);
                ret = isAvailable
                        && isConnected;
                if (ret) {
                    synchronized (mConnectLoack) {
                        mConnectLoack.notifyAll();
                    }
                    AppStatus.initNetChanageTime();
                }
            } else {
                mConnectionInfo.setNetType(TelephonyManager.NETWORK_TYPE_UNKNOWN);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ret = true;
        }
        AppStatus.gIsOnline = ret;
        return ret;
    }

    /**
     * Acquires a partial wake lock for this client
     */
    private void acquireWakeLock() {
        try {
            if (mWakeLock == null) {
                PowerManager pm = (PowerManager) mContext.getSystemService(POWER_SERVICE);
                mWakeLock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, TAG);
            }
            mWakeLock.acquire();
        } catch (Throwable e) {
            BLog.printErrStackTrace(TAG, e, "acquireWakeLock");
        }

    }

    /**
     * Releases the currently held wake lock for this client
     */
    private void releaseWakeLock() {
        try {
            if (mWakeLock != null && mWakeLock.isHeld()) {
                mWakeLock.release();
            }
        } catch (Throwable e) {
            BLog.printErrStackTrace(TAG, e, "releaseWakeLock");
        }
    }

    /**
     * 注册网络Broadcast
     */
    void registerBroadcastReceivers() {
        if (mNetworkConnectionMonitor == null) {
            mNetworkConnectionMonitor = new NetworkConnectionIntentReceiver();
            IntentFilter filter = new IntentFilter();
            filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
//            filter.addAction(Intent.ACTION_SCREEN_ON);
            mContext.registerReceiver(mNetworkConnectionMonitor, filter);
        }
    }

    /**
     * 解除网络Broadcast
     */
    void unregisterBroadcastReceivers() {
        if (mNetworkConnectionMonitor != null) {
            mContext.unregisterReceiver(mNetworkConnectionMonitor);
            mNetworkConnectionMonitor = null;
        }
    }

    private int mUnblocked = -1;

    private boolean isWaitUnblocked() {
        if (mUnblocked == -1) {
            try {
                SharedPreferences sharedPreferences = mContext.getSharedPreferences(TAG, Context.MODE_PRIVATE);
                mUnblocked = sharedPreferences.getBoolean(UNBLOCKED_KEY, true) ? 1 : 0;
            } catch (Exception e) {
                mUnblocked = 0;
            }
        }
        return mUnblocked == 1;
    }

    @Override
    public void onSuicide() {
        try {
            if (mCurrentWhat == HANDE_CONNECTION_SEND) {
                SharedPreferences sharedPreferences = mContext.getSharedPreferences(TAG, Context.MODE_PRIVATE);
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean(UNBLOCKED_KEY, false);
                editor.commit();
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ConnectionInfo getConnectionInfo() {
        return mConnectionInfo;
    }

    private NetworkConnectionIntentReceiver mNetworkConnectionMonitor;

    private class NetworkConnectionIntentReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            try {
                // we protect against the phone switching off
                // by requesting a wake lock - we request the minimum possible wake
                // lock - just enough to keep the CPU running until we've finished
                switch (intent.getAction()) {
                    case ConnectivityManager.CONNECTIVITY_ACTION:
                        onNetChanage();
                        break;
                    case Intent.ACTION_SCREEN_ON:
                        AlarmPingSender.executePing();
                        break;
                }
            } catch (Throwable e) {
                e.printStackTrace();
            }
        }

        private void onNetChanage() {
            PowerManager pm = (PowerManager) mContext.getSystemService(POWER_SERVICE);
            PowerManager.WakeLock wl = pm
                    .newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "MQTT");
            wl.acquire();
            BLog.d(TAG, "Reconnect for Network recovery.");
            sNetChanageTime = SystemClock.elapsedRealtime();
            if (isOnline()) {
                BLog.d(TAG, "Online,tryReconnect.");
                MessageStatistics.clean();
                // we have an internet connection - have another try at
                // connecting
                synchronized (mConnectLoack) {
                    mConnectLoack.notifyAll();
                }
                IPManager.getInstance().onNetChanege();
                if (AppStatus.gIsMobileNet) {//无线网络延迟2s再连接
                    sendEmptyMessageDelayed(HANDE_CONNECTION_RECONNECT, 2000);
                } else {
                    sendEmptyMessage(HANDE_CONNECTION_RECONNECT);//wifi有网络马上连接
                }
            } else {
                BLog.d(TAG, "Offline,disconnect.");
                removeMessages(HANDE_CONNECTION_RECONNECT);//删除重连消息
                disconnect();
                mPingHelper.onNetChanage();
            }
            if (wl.isHeld()) {
                wl.release();
            }
        }
    }
}
