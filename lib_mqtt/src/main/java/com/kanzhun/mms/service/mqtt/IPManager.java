package com.kanzhun.mms.service.mqtt;

import android.os.Message;
import android.os.SystemClock;

import com.kanzhun.mms.common.DNS;
import com.kanzhun.mms.common.EndpointInfo;
import com.kanzhun.mms.service.AppStatus;
import com.kanzhun.mms.service.MMSServiceNative;
import com.kanzhun.mms.utils.BLog;
import com.kanzhun.mms.utils.ExceptionUtils;
import com.kanzhun.mms.utils.MqttUtil;
import com.kanzhun.mms.utils.StringUtils;
import com.kanzhun.mms.utils.TWLException;
import com.kanzhun.mms.utils.ThreadManager;

import org.eclipse.paho.client.mqttv3.internal.http.HttpRequest;

import java.lang.ref.SoftReference;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.kanzhun.mms.utils.TWLException.MMS_SERVER_HTTP_2_TCP;
import static com.kanzhun.mms.utils.TWLException.MMS_SERVER_TCP_2_HTTP;

/**
 * Created by yuchaofei on 16/10/19.
 */

class IPManager implements HttpRequest.IHttpDataWrapper {
    private static final String TAG = "IPManager";

    private static final int HANDLE_GET_IP = 1233;
    private static final int HANDLE_REFRESH_IP = 1234;

    private static final long DEFAULT_UPDATE_TIME = 1000 * 60 * 60;// 一个小时更新一下ip列表

    private static final long MIN_UPDATE_TIME = 5 * 60 * 60;// 最小更新ip列表时间

    public static int position = 0;

    public static EndpointInfo getSEndpointInfo(){
       return MMSServiceNative.getServerProfile().getServerInfo(position);
    }

    private boolean mIsUseHttp = false;

    private boolean mCurrentUseHttp;

    private static IPManager gIpConfig = new IPManager();

    public static IPManager getInstance() {
        return gIpConfig;
    }

    private IPHandler mIPHandler = new IPHandler();


    private IPManager() {
    }

    /**
     * 返回chaturl：协议头区分 http(以http协议为通道) 或者 tcp；
     * 地址部分返回任意一个备用ip，如果返回URL的话new InetSocketAddress 会进行不必要的DNS解析，耗时；
     *
     * @return
     */
    public String getServerUrl() {
        mIPHandler.waitDNSResolve();
        mIsTimeOut = false;
        mCurrentUseHttp = mIsUseHttp;
        if (mCurrentUseHttp) {
            mCurrentUseHttp = isSupportHttp();
        }
        EndpointInfo.Endpoint endpoint = mCurrentUseHttp ? getSEndpointInfo().getHttpEndpoint() : getSEndpointInfo().getEndpoint();
        if (endpoint.isEmpty()) {
            mIPHandler.sendEmptyMessage(HANDLE_REFRESH_IP);
        }
        return MqttUtil.formatUrl(endpoint);
    }

    /**
     * 是否支持Http
     *
     * @return
     */
    private boolean isSupportHttp() {
        return AppStatus.gIsOnline && !AppStatus.gIsMobileNet && getSEndpointInfo().isSupportHttp();
    }

    public String getHost() {
        return getSEndpointInfo().getEndpoint().getUrl();
    }

    /**
     * 有网+不是移动网络+连接超时+(网络稳定+且是活跃状态 或者 重试)，tcp -> http
     */
    public void checkHttp() {
        if (mIsUseHttp) {
            onTypeChanage(false);
        } else {
            if (AppStatus.gIsOnline
                    && !AppStatus.gIsMobileNet
                    && mIsTimeOut
                    && ((AppStatus.isNetStable() && MessageStatistics.isUseHTTP() && AppStatus.isActive()) || isReconnect)) {
                onTypeChanage(true);
            } else {
                onTypeChanage(false);
            }
        }
        if (isSupportHttp()) {
            MessageStatistics.printStatistics();
        }
    }

    private volatile boolean isReconnect = false;

    public void setReconnect(boolean isReconn) {
        isReconnect = isReconn;
    }

    private boolean mIsTimeOut = false;

    /**
     * 连接超时
     */
    void onTimeOut() {
        BLog.d(TAG, "onTimeOut() called");
        mIsTimeOut = true;
    }

    /**
     * 切换使用协议状态
     *
     * @param useHttp
     */
    public void onTypeChanage(boolean useHttp) {
        onTypeChanage(false, useHttp);
    }

    /**
     * 切换使用协议状态
     *
     * @param isNetChange
     * @param useHttp
     */
    public void onTypeChanage(boolean isNetChange, boolean useHttp) {
        if (getSEndpointInfo().isSupportHttp()) {
            HttpRequest.setHttpDataWrapper(this);
            if (useHttp) {//上报http使用情况
                ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_TCP_2_HTTP, new Exception("Use Http, Only for Statistics!")));
            } else if (mIsUseHttp && !isNetChange && AppStatus.gIsOnline && !AppStatus.gIsMobileNet) {//非网络改变的情况下，从http切换到tcp，上报并且上传log文件。
                ExceptionUtils.postCatchedException(new TWLException(MMS_SERVER_HTTP_2_TCP, new Exception("Changed from http to tcp, Only for Statistics!")));
            }

            mIsUseHttp = useHttp;
        }
    }

    private String mLastNetType = null;

    /**
     * 网络变更
     */
    public void onNetChanege() {
        onTypeChanage(true, false);
//        if (mLastNetType != null) {//网络不一致，刷新ip列表
//            mIPHandler.sendEmptyMessageDelayed(HANDLE_GET_IP, 2000);
//        }
        mLastNetType = "";
    }

    public boolean isCurrentUseHttp() {
        return mCurrentUseHttp;
    }

    /**
     * 根据index 返回ip,第一次返回上一次连接成功的ip
     * 根据不同的协议返回不一样列表
     *
     * @param index
     * @return
     */
    public InetSocketAddress getServerIP(int index) {
        EndpointInfo.Endpoint endpoint = getCurrentEndpoint();
        if (isReconnect) {//如果是重试的情况，直接重ip库里随机一个ip
            Random random = new Random();
            index = random.nextInt(20);
            return endpoint.getIP(index);
        }
        return endpoint.getIP(index);
    }

    private EndpointInfo.Endpoint getCurrentEndpoint() {

        EndpointInfo.Endpoint endpoint;
        if (mCurrentUseHttp) {
            endpoint = getSEndpointInfo().getHttpEndpoint();
        } else {
            endpoint = getSEndpointInfo().getEndpoint();
        }
        return endpoint;
    }

    /**
     * 连接成功，记录成功iP
     *
     * @param address
     */
    public void onConnectSuccess(InetAddress address) {
        if (address instanceof InetAddress) {
            BLog.d(TAG, "onConnectSuccess() address = [%s]", address);
        }
    }

    /**
     * 针对特定异常，删除IP
     *
     * @param address
     * @return
     */
    public boolean removeIP(InetSocketAddress address) {
        boolean ret = false;
        try {
            EndpointInfo.Endpoint endpoint = getCurrentEndpoint();
            ret = endpoint.removeIP(address);
            if (!mCurrentUseHttp
                    && ret
                    && endpoint.isEmpty()) {
                mIPHandler.sendEmptyMessage(HANDLE_GET_IP);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }

    /**
     * 简单cache ack长度的回报http头，避免大量临时变量产生
     */
    private final static int CACHE_DATA_SIZE = 4;
    private SoftReference<byte[]> mHttpHeadCache;

    @Override
    public byte[] getHttpHead(byte[] data, String host, int port) {
        byte[] headData = null;
        if (data.length == CACHE_DATA_SIZE && mHttpHeadCache != null) {
            headData = mHttpHeadCache.get();
        }

        if (headData == null) {
            StringUtils.openCache();
            StringBuilder head = StringUtils.obtainStringBuilder();
            head.append("POST / HTTP/1.1\r\n");
            head.append("Connection: Keep-Alive\r\n");
            head.append("Host: ").append(getSEndpointInfo().getHttpEndpoint().getServerUrl()).append("\r\n");
            head.append("Accept: */*\r\n");
            head.append("User-Agent: Boss-Android-Client\r\n");
            head.append("Content-Type: application/x-www-form-urlencoded\r\n");
            head.append("Content-Length: ").append(data.length).append("\r\n\r\n");
            headData = head.toString().getBytes();
            if (data.length == CACHE_DATA_SIZE) {
                mHttpHeadCache = new SoftReference<>(headData);
            }
        }
        return headData;
    }

    private static class IPHandler extends ThreadManager.MMSHandler {
        private static final long WAIT_DNS_RESOLVE_TIME = 30;//等待域名解析默认时间
        private static DNS sDns = new DNS();

        private Lock mIPLock = new ReentrantLock();
        private Condition mIPCondition = mIPLock.newCondition();
        private volatile boolean isVirgin = true;
        private long mLastUpdateTime = -1;

        public IPHandler() {
            super(ThreadManager.getSubThreadLooper());
        }

        @Override
        public void handleMessage(Message msg) {

            switch (msg.what) {
                case HANDLE_GET_IP:
//                    updateIPList();//默认使用系统的DNS
//                    break;
                case HANDLE_REFRESH_IP://当IP列表是null的，请求自定义DNS
                    executeResolve(getSEndpointInfo().getDNSResolve());
                    break;
                default:
                    break;
            }
        }

        /**
         * 等待域名解析完，最多等2s
         */
        public void waitDNSResolve() {
            if (isVirgin) {
                sendEmptyMessage(HANDLE_GET_IP);
                try {
                    long startTime = SystemClock.elapsedRealtime();
                    if (mIPLock.tryLock(WAIT_DNS_RESOLVE_TIME, TimeUnit.SECONDS)) {
                        try {
                            if (isVirgin) mIPCondition.await(WAIT_DNS_RESOLVE_TIME, TimeUnit.SECONDS);
                        } finally {
                            mIPLock.unlock();
                        }
                    } else {
                        BLog.e(TAG, "waitDNSResolve: Lock Fail");
                    }
                    BLog.e(TAG, "waitDNSResolve: [%d]", (SystemClock.elapsedRealtime() - startTime));
                } catch (Throwable e) {
                    e.printStackTrace();
                } finally {
                    isVirgin = false;
                }
            }
        }

        /**
         * 更新IP列表，使用系统的DNS
         */
        private void updateIPList() {
            executeResolve(new DNS());
            removeMessages(HANDLE_GET_IP);
            sendEmptyMessageDelayed(HANDLE_GET_IP, DEFAULT_UPDATE_TIME);
        }

        private void executeResolve(DNS dns){
            try {
                updateEndpoint(dns, getSEndpointInfo().getEndpoint());
                updateEndpoint(dns, getSEndpointInfo().getHttpEndpoint());
                checkEndpoint();
            } catch (Throwable e) {
            } finally {
                dnsNotify();
            }
        }

        private void checkEndpoint(){
            if (getSEndpointInfo().getEndpoint().isEmpty()) {
                BLog.d(TAG, "checkEndpoint: getEndpoint is empty");
                updateEndpoint(sDns, getSEndpointInfo().getEndpoint());
                updateEndpoint(sDns, getSEndpointInfo().getHttpEndpoint());
            }
        }

        /**
         * 唤醒dns等待
         */
        private void dnsNotify() {
            try {
                if (mIPLock.tryLock(WAIT_DNS_RESOLVE_TIME, TimeUnit.SECONDS)) {
                    try {
                        mIPCondition.signalAll();
                    } finally {
                        mIPLock.unlock();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                isVirgin = false;
            }
        }

        private static void updateEndpoint(DNS dns, EndpointInfo.Endpoint endpoint) {
            if (endpoint != null) {
                List<InetSocketAddress> ips = dns.dnsResolve(endpoint.getServerUrl(), getSEndpointInfo().isSupportIPV6());
                if (ips != null && ips.size() > 0) {
                    BLog.d(TAG, endpoint.getServerUrl().getHostName());
                    for (InetSocketAddress ip : ips) {
                        BLog.d(TAG, "dnsResolve ip = [%s]", ip);
                    }
                    endpoint.setIPList(ips);
                } else {
                    BLog.e(TAG, "%s resolve return null", endpoint.getServerUrl());
                }
            }
        }
    }
}
