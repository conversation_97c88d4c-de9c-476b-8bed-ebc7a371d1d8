package com.kanzhun.mms.common;

import androidx.annotation.NonNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/3/7.
 */

public interface IServerProfile {
    /**
     * 返回服务端配置信息
     * @return
     */
    EndpointInfo getServerInfo(int position);

    /**
     * 返回用户信息，如果为null，后台状态不连接，并退出
     * @return
     */
    UserInfo getUserInfo();

//    IConnection createConnection();


    /**
     * 后台进程独立启动的回调
     * @return
     */
    @NonNull
    ConnectionLifecycleCallback getConnectionCallback();


    /**
     * 返回mqtt 属性配置
     * @return
     */
    MqttOptions getMqttOptions();
}
