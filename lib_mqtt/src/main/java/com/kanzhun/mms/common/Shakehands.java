package com.kanzhun.mms.common;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

import androidx.core.content.ContextCompat;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/7/19.
 */

public class Shakehands {
    private static final String ACTION = "com.twl.shakehands";

    public static void sendShakehands(Context context){
        try {
            Intent intent = new Intent(ACTION);
            String permission = context.getPackageName()+ ".RECV_SH";
            context.sendBroadcast(intent, permission);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    public static void registerShakehands(Context context, BroadcastReceiver receiver){
        try {
            ContextCompat.registerReceiver(context, receiver, new IntentFilter(ACTION), ContextCompat.RECEIVER_NOT_EXPORTED);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

}
