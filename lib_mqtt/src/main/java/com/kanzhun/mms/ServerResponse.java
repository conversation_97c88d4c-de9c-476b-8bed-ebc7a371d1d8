package com.kanzhun.mms;

import android.os.Parcel;
import android.os.Parcelable;

public final class ServerResponse implements Parcelable {
    public final boolean success;
    public final long serverId;
    public final long sequence;

    protected ServerResponse(Parcel in) {
        success = in.readByte() != 0;
        serverId = in.readLong();
        sequence = in.readLong();
    }

    public ServerResponse(boolean success, long serverId, long sequence) {
        this.success = success;
        this.serverId = serverId;
        this.sequence = sequence;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte(success ? (byte) 1 : 0);
        dest.writeLong(serverId);
        dest.writeLong(sequence);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ServerResponse> CREATOR = new Creator<ServerResponse>() {
        @Override
        public ServerResponse createFromParcel(Parcel in) {
            return new ServerResponse(in);
        }

        @Override
        public ServerResponse[] newArray(int size) {
            return new ServerResponse[size];
        }
    };

    @Override
    public String toString() {
        return "ServerResponse{" +
                "success=" + success +
                ", serverId=" + serverId +
                ", sequence=" + sequence +
                '}';
    }
}
