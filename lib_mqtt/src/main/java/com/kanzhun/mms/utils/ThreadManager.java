package com.kanzhun.mms.utils;

import static com.kanzhun.mms.common.MMSConstants.DEFAULT_PROCESS_HUANGUP_DELTA_TIME;
import static com.kanzhun.mms.common.MMSConstants.MIN_PROCESS_HUANGUP_DELTA_TIME;
import static com.kanzhun.mms.utils.TWLException.MMS_WATCHER;

import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.os.SystemClock;
import android.util.Log;

import com.kanzhun.mms.service.AppStatus;

/**
 * Created by yuch<PERSON><PERSON><PERSON> on 16/10/19.
 */

public final class ThreadManager {
    private static final String TAG = "ThreadManager";

    private static final long MONITOR_CHECK_TIME = 10 * 60 * 1000;
    private static final long MONITOR_SUBMIT_TIME = 2 * 1000;
    private static final int HANDLE_SUBMIT = 0;//监视任务提交
    private static final int HANDLE_CHECK = 1;//监视任务检查
    /**
     * 副线程的Handle, 只有一个线程
     * 可以执行比较快但不能在ui线程执行的操作.
     */
    private static Handler SUB_THREAD_HANDLER;

    private static HandlerThread SUB_THREAD;


    /**
     * 获得副线程的Handler.<br>
     * 副线程可以执行比较快但不能在ui线程执行的操作.<br>
     *
     * @return handler
     */
    public static Handler getSubThreadHandler() {
        if (SUB_THREAD_HANDLER == null) {
            synchronized (ThreadManager.class) {
                SUB_THREAD = new HandlerThread("MMC_SUB");
                SUB_THREAD.start();
                SUB_THREAD_HANDLER = new MMSHandler(SUB_THREAD.getLooper());
            }
        }
        return SUB_THREAD_HANDLER;
    }

    /**
     * 获得副线程的Looper
     *
     * @return
     */
    public static Looper getSubThreadLooper() {
        return getSubThreadHandler().getLooper();
    }



    private static HandlerThread CONNECT_THREAD;

    /**
     * 获得接连处理线程的Looper
     *
     * @return
     */
    public static Looper getConnectThreadLooper() {
        if (CONNECT_THREAD == null) {
            synchronized (ThreadManager.class) {
                CONNECT_THREAD = new HandlerThread("MMC_CONECT", Thread.MAX_PRIORITY);
                CONNECT_THREAD.start();
            }
        }
        return CONNECT_THREAD.getLooper();
    }


    private static HandlerThread REPORT_THREAD;

    /**
     * 获得接连处理线程的Looper
     *
     * @return
     */
    public static Looper getReportThreadLooper() {
        if (REPORT_THREAD == null) {
            synchronized (ThreadManager.class) {
                REPORT_THREAD = new HandlerThread("REPORT");
                REPORT_THREAD.start();
            }
        }
        return REPORT_THREAD.getLooper();
    }

    /**
     * 监视Handler状态
     * @param handler
     */
    public static void monitorHandler(Handler handler, OnSuicideListener l){
        Message message = getWatcher(l).obtainMessage(HANDLE_SUBMIT, handler);
        getWatcher(l).sendMessageDelayed(message, MONITOR_SUBMIT_TIME);
    }

    public interface OnSuicideListener {
        void onSuicide();
    }

    private static Watcher sWatcher;
    private static MMSHandler getWatcher(OnSuicideListener l){
        if (sWatcher == null) {
            HandlerThread handlerThread = new HandlerThread("Watcher");
            handlerThread.start();
            sWatcher = new Watcher(handlerThread.getLooper(), l);
        }
        return sWatcher;
    }

    public static class MMSHandler extends Handler {

        public MMSHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void dispatchMessage(Message msg) {
            try {
                super.dispatchMessage(msg);
            } catch (Throwable throwable) {
                ExceptionUtils.postCatchedException(new TWLException(throwable));
            }
        }
    }

    /**
     * 监视者 监视线程状态
     */
    private static class Watcher extends MMSHandler {
        private OnSuicideListener mOnSuicideListener;
        public Watcher(Looper looper, OnSuicideListener suicideListener) {
            super(looper);
            this.mOnSuicideListener = suicideListener;
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case HANDLE_SUBMIT:
                    Handler handler;
                    final MonitorBlock block;
                    if (msg.obj instanceof Handler){
                        handler = (Handler) msg.obj;
                        block = new MonitorBlock(handler);
                    } else if (msg.obj instanceof MonitorBlock) {
                        block = (MonitorBlock) msg.obj;
                        block.mStatus = false;
                        handler = block.mHandler;
                    } else {
                        BLog.e(TAG, "Error msg.obj =[%s]", msg.obj);
                        break;
                    }
                    handler.postAtFrontOfQueue(new Runnable() {
                        @Override
                        public void run() {
                            block.update();
                        }
                    });
                    Message message = obtainMessage(HANDLE_CHECK, block);
                    sendMessageDelayed(message, MONITOR_CHECK_TIME);
                    break;
                case HANDLE_CHECK:
                    MonitorBlock monitorBlock = (MonitorBlock) msg.obj;
                    boolean isAdopt = monitorBlock.mStatus;
                    long elapsedTime = SystemClock.uptimeMillis() - msg.getWhen();
                    if (isAdopt == false && elapsedTime > DEFAULT_PROCESS_HUANGUP_DELTA_TIME) {//判断进程是否挂起
                        isAdopt = true;
                    }
                    if (isAdopt){
                        BLog.d(TAG, "Check through！ Thread Name = [%s], elapsedTime = [%d]", monitorBlock.mHandler.getLooper().getThread().getName(), elapsedTime);
                        obtainMessage(HANDLE_SUBMIT, monitorBlock).sendToTarget();
                    } else {
                        BLog.e(TAG, "=============MMS suicide!!!!=============");
                        Log.e(TAG, msg.toString());
                        try {
                            Thread.sleep(8000);//休眠8s，减少进程挂起的可读性
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (monitorBlock.mStatus || (elapsedTime >= MIN_PROCESS_HUANGUP_DELTA_TIME && !AppStatus.gIsOnline)){
                            obtainMessage(HANDLE_SUBMIT, monitorBlock).sendToTarget();
                        } else {//mms自杀了
                            BLog.e(TAG, "=============MMS real suicide!!!!=============");
                            ExceptionUtils.postCatchedException(new TWLException(MMS_WATCHER, new Exception("MMS suicide!!!!")), true);
                            try {
                                Thread.sleep(8000);//休眠8s，上报异常+log上传
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                            if (mOnSuicideListener != null) {
                                mOnSuicideListener.onSuicide();
                            }
                            Process.killProcess(Process.myPid());
                        }
                    }
                    break;
            }
        }
    }

    private static class MonitorBlock {
        private Handler mHandler;

        private volatile boolean mStatus = false;

        public MonitorBlock(Handler handler) {
            mHandler = handler;
        }

        public void update(){
            mStatus = true;
            BLog.d(TAG, "update call. Thread name = [%s]", Thread.currentThread().getName());
        }
    }

}