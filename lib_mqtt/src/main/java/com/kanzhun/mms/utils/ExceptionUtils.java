package com.kanzhun.mms.utils;

import android.util.SparseIntArray;

import org.eclipse.paho.client.mqttv3.MqttException;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/10/26.
 * 异常上报
 */
public class ExceptionUtils {
    private static final String TAG = "ExceptionUtils";
    private final static int REPORT_THRESHOLD = 50;
    private static SparseIntArray gThrowables = new SparseIntArray();
    private static PostExecption sPostExecption;

    public static void setPostExecption(PostExecption postExecption) {
        sPostExecption = postExecption;
    }

    public static void postCatchedException(MqttException thr) {
        postCatchedException(thr, false);
    }

    public static void postCatchedException(MqttException thr, boolean isReportLog) {
        boolean isReport = true;
        if (thr != null) {
            if (thr instanceof MqttException) {
                synchronized (ExceptionUtils.class) {
                    MqttException mqttException = thr;
                    int count = gThrowables.get(mqttException.getReasonCode());
                    isReport = count++ % REPORT_THRESHOLD == 0;//第一次和连续发生50次上报一次。
                    gThrowables.put(mqttException.getReasonCode(), count);
                    compressThrowables();
                }
            }
            if (isReport) {
                BLog.printErrStackTrace(TAG, thr, "");
                if (sPostExecption != null) {
                    sPostExecption.postCatchedException(thr, isReportLog);
                } else {
                    BLog.e(TAG, "sPostExecption  == null");
                }
            }
        }
    }

    public static void reportAction(String action){
        if (sPostExecption != null) {
            sPostExecption.reportAction(action);
        }
    }

    public interface PostExecption {
        void postCatchedException(Throwable throwable, boolean isReportLog);
        void reportAction(String action);
    }

    private static void compressThrowables() {
        if (gThrowables.size() > REPORT_THRESHOLD) {
            gThrowables.removeAt(0);
        }
    }

}
