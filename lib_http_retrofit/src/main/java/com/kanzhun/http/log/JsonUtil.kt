package com.kanzhun.http.log

import org.json.JSONException
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

/**
 * <AUTHOR>
 * @date 2018/10/15.
 * @desc
 */
object JsonUtil {
    private  val mDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss" + ".SSS", Locale.getDefault())
    /**
     * 合并 JSONObject
     *
     * @param source JSONObject
     * @param dest   JSONObject
     * @throws JSONException Exception
     */
    @Throws(JSONException::class)
    fun mergeJSONObject(source: JSONObject, dest: JSONObject) {
        try {
            val superPropertiesIterator = source.keys()
            while (superPropertiesIterator.hasNext()) {
                val key = superPropertiesIterator.next()
                val value = source.get(key)
                if (value is Date) {
                    synchronized(mDateFormat) {
                        dest.put(key, mDateFormat.format(value))
                    }
                } else {
                    dest.put(key, value)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun addIndentBlank(sb: StringBuilder, indent: Int) {
        try {
            for (i in 0 until indent) {
                sb.append('\t')
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun formatJson(jsonStr: String?): String {
        try {
            if (null == jsonStr || "" == jsonStr) {
                return ""
            }
            val sb = StringBuilder()
            var last = '\u0000'
            var current = '\u0000'
            var indent = 0
            var isInQuotationMarks = false
            for (i in 0 until jsonStr.length) {
                last = current
                current = jsonStr[i]
                when (current) {
                    '"' -> {
                        if (last != '\\') {
                            isInQuotationMarks = !isInQuotationMarks
                        }
                        sb.append(current)
                    }
                    '{', '[' -> {
                        sb.append(current)
                        if (!isInQuotationMarks) {
                            sb.append('\n')
                            indent++
                            addIndentBlank(sb, indent)
                        }
                    }
                    '}', ']' -> {
                        if (!isInQuotationMarks) {
                            sb.append('\n')
                            indent--
                            addIndentBlank(sb, indent)
                        }
                        sb.append(current)
                    }
                    ',' -> {
                        sb.append(current)
                        if (last != '\\' && !isInQuotationMarks) {
                            sb.append('\n')
                            addIndentBlank(sb, indent)
                        }
                    }
                    else -> sb.append(current)
                }
            }

            return sb.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }

    fun formatJson(jsonStr: String?, sBuilder : StringBuilder): String {
        try {
            if (null == jsonStr || "" == jsonStr) {
                return ""
            }
            var last = '\u0000'
            var current = '\u0000'
            var indent = 0
            var isInQuotationMarks = false
            for (i in 0 until jsonStr.length) {
                last = current
                current = jsonStr[i]
                when (current) {
                    '"' -> {
                        if (last != '\\') {
                            isInQuotationMarks = !isInQuotationMarks
                        }
                        sBuilder.append(current)
                    }
                    '{', '[' -> {
                        sBuilder.append(current)
                        if (!isInQuotationMarks) {
                            sBuilder.append('\n')
                            indent++
                            addIndentBlank(sBuilder, indent)
                        }
                    }
                    '}', ']' -> {
                        if (!isInQuotationMarks) {
                            sBuilder.append('\n')
                            indent--
                            addIndentBlank(sBuilder, indent)
                        }
                        sBuilder.append(current)
                    }
                    ',' -> {
                        sBuilder.append(current)
                        if (last != '\\' && !isInQuotationMarks) {
                            sBuilder.append('\n')
                            addIndentBlank(sBuilder, indent)
                        }
                    }
                    else -> sBuilder.append(current)
                }
            }

            return sBuilder.toString()
        } catch (e: Exception) {
            e.printStackTrace()
            return ""
        }
    }
}