package com.kanzhun.http.transformer;


import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.error.LoginException;
import com.kanzhun.http.error.ResponseException;
import com.kanzhun.http.error.SecurityNoticeException;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Observer;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/24
 */
public class BaseObserver<T> implements Observer<T> {

    public static BaseRequestCallback.OnLoginError gOnLoginError = null;
    public static BaseRequestCallback.OnSecurityNoticeError gOnSecurityNoticeError = null;

    public static void setOnLoginError(BaseRequestCallback.OnLoginError gOnLoginError) {
        BaseObserver.gOnLoginError = gOnLoginError;
    }
    public static void setOnSecurityNoticeError(BaseRequestCallback.OnSecurityNoticeError gOnSecurityNoticeError) {
        BaseObserver.gOnSecurityNoticeError = gOnSecurityNoticeError;
    }

    protected final BaseRequestCallback<T> callback;

    public BaseObserver(BaseRequestCallback<T> callback) {
        this.callback = callback;
    }

    @Override
    public void onSubscribe(@NonNull Disposable d) {
        if (callback != null) {
            callback.onStart(d);
        }
    }

    @Override
    public void onNext(@NonNull T t) {
        if (callback != null) {
            callback.onSuccess(t);
        }
    }

    @Override
    public void onError(@NonNull Throwable e) {
        if (e instanceof LoginException) {
            onLoginError();
            onComplete();
            return;
        }
        if (e instanceof SecurityNoticeException) {
            onSecurityNoticeError((SecurityNoticeException)e);
//            onComplete();
//            return;
        }
        if (e instanceof NullPointerException && callback != null) {
            //表示data 返回null 也是success
            callback.onSuccess(null);
        } else if (e instanceof Exception && callback != null) {
            ErrorReason errorReason;
            if (e instanceof ResponseException) {
                ResponseException exception = (ResponseException) e;
                errorReason = new ErrorReason(exception.getErrCode(), exception.getErrMsg(), exception);
            }else {
               errorReason = new ErrorReason(ResponseException.ERROR_UNKNOWN_CODE, ErrorReason.ERROR_UNKNOWN, (Exception) e);
            }
            callback.dealFail(errorReason);
            callback.showFailed(errorReason);
        }
        onComplete();
    }

    @Override
    public void onComplete() {
        if (callback != null) {
            callback.onComplete();
        }
    }

    /**
     * 登录异常
     */
    public void onLoginError() {
        BaseRequestCallback.OnLoginError onLoginError = gOnLoginError;
        if (onLoginError != null) {
            onLoginError.onLoginError();
        }
    }

    /**
     * 安全处置
     */
    public void onSecurityNoticeError(SecurityNoticeException e) {
        BaseRequestCallback.OnSecurityNoticeError onSecurityNoticeError = BaseObserver.gOnSecurityNoticeError;
        if (onSecurityNoticeError != null) {
            onSecurityNoticeError.onSecurityNoticeError(e);
        }
    }

    public static class ChildThirdConsumer<T> implements Consumer<T> {
        private final BaseRequestCallback<T> callback;

        public ChildThirdConsumer(BaseRequestCallback<T> callback) {
            this.callback = callback;
        }


        @Override
        public void accept(T t) throws Exception {
            if (callback != null) {
                callback.handleInChildThread(t);
            }
        }
    }

    public static class ChildThirdErrorConsumer implements Consumer<Throwable> {
        private final BaseRequestCallback callback;

        public ChildThirdErrorConsumer(BaseRequestCallback callback) {
            this.callback = callback;
        }


        @Override
        public void accept(Throwable throwable) throws Exception {
            if (throwable instanceof LoginException) {
                return;
            }
            if (throwable instanceof NullPointerException && callback != null) {
                //表示data 返回null 也是success
                callback.handleInChildThread(null);
            } else if (throwable instanceof ResponseException && callback != null) {
                ResponseException exception = (ResponseException) throwable;
                ErrorReason errorReason = new ErrorReason(exception.getErrCode(), exception.getErrMsg(), exception);
                callback.handleErrorInChildThread(errorReason);
            } else {
                if (throwable instanceof Exception && callback != null) {
                    ErrorReason errorReason = new ErrorReason(ResponseException.ERROR_UNKNOWN_CODE, ErrorReason.ERROR_UNKNOWN, (Exception) throwable);
                    callback.handleErrorInChildThread(errorReason);
                }
            }
        }
    }
}
