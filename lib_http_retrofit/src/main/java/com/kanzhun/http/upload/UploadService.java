package com.kanzhun.http.upload;

import com.google.gson.JsonObject;
import com.kanzhun.http.response.BaseResponse;

import java.util.List;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.MultipartBody;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Url;

/**
 * Created by ChaiJiangpeng
 * Date: 2022/2/22
 */
public interface UploadService {
    @Multipart
    @POST
    Observable<BaseResponse<JsonObject>> uploadFiles(@Url String url, @Part List<MultipartBody.Part> params);

    @Multipart
    @POST
    Observable<BaseResponse<JsonObject>> uploadFile(@Url String url, @Part List<MultipartBody.Part> params);
}
