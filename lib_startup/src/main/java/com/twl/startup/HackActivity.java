package com.twl.startup;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;


final class HackActivity extends Activity {
    private static final String TAG = "StartupCore.HackActivity";

    public String mOriginal;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        StartupLog.d(TAG, "onCreate: ");
        setVisible(false);
        Intent intent = new Intent(this, StartupCore.getInstence().getSpalashClass());
        intent.putExtra(StartupConstant.SPLASH_HASHCODE, hashCode());
        startActivityForResult(intent, StartupConstant.REQUEST_CODE);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        StartupLog.d(TAG, "onResume() called");
    }

    @Override
    protected void onPause() {
        super.onPause();
        StartupLog.d(TAG, "onPause() called");
    }

    @Override
    protected void onDestroy() {
        StartupLog.d(TAG, "onDestroy() called");
        setVisible(true);
        StartupCore.getInstence().getHackActivities().remove(this);
        super.onDestroy();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (requestCode == StartupConstant.REQUEST_CODE) {
            if (resultCode != StartupConstant.RESULT_CODE) {
                onBackPressed();
            }
        } else {
            finish();
            StartupLog.e(TAG, "onActivityResult: requestCode = [%d]", requestCode);
        }
    }
}
