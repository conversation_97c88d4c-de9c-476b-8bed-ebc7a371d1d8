<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="contact"
            type="com.kanzhun.foundation.model.Contact" />
    </data>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_translate">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_float"
            android:layout_width="match_parent"
            android:layout_height="94dp"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:background="@drawable/video_bg_window_remote_call"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <com.kanzhun.common.views.image.OImageView
                android:id="@+id/ov_avatar"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_margin="2dp"
                android:tag="contact_avatar"
                app:common_circle="true"
                app:common_placeholder="@mipmap/common_default_avatar"
                app:imageUrl="@{contact.avatar}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_invite_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:singleLine="true"
                android:text="@{contact.nickName}"
                android:textColor="@color/common_white"
                android:textSize="@dimen/common_text_sp_16"
                app:layout_constraintBottom_toTopOf="@+id/tv_tips"
                app:layout_constraintLeft_toRightOf="@+id/ov_avatar"
                app:layout_constraintRight_toLeftOf="@+id/iv_call_cancel"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_chainStyle="packed"
                tools:text="Cathy 猫" />

            <TextView
                android:id="@+id/tv_tips"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:text="@string/video_chat_invitation_audio"
                android:textColor="@color/common_color_FFFFFF_80"
                android:textSize="@dimen/common_text_sp_14"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/ov_avatar"
                app:layout_constraintRight_toLeftOf="@+id/iv_call_cancel"
                app:layout_constraintTop_toBottomOf="@+id/tv_invite_name" />

            <ImageView
                android:id="@+id/iv_call_cancel"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginRight="16dp"
                android:scaleType="centerInside"
                android:src="@drawable/video_ic_icon_call_hang_up"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toLeftOf="@+id/iv_call"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/iv_call"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:scaleType="centerInside"
                android:src="@drawable/video_ic_icon_call_on_call"
                app:layout_constraintBottom_toBottomOf="@id/iv_call_cancel"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/iv_call_cancel" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>
</layout>