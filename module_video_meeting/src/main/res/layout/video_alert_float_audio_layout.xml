<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/small_size_frame_layout"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@mipmap/video_bg_window_call">

    <LinearLayout
        android:layout_width="@dimen/video_meeting_window_audio_width"
        android:layout_height="@dimen/video_meeting_window_audio_height"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="vertical">

        <com.kanzhun.common.views.image.OImageView
            android:layout_width="46dp"
            android:layout_height="46dp"
            android:layout_marginTop="12dp" />

        <com.kanzhun.marry.videomeeting.view.VideoChatClockView
            android:id="@+id/tv_clock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="5dp"
            android:textColor="@color/video_00c782"
            android:textSize="@dimen/common_text_sp_14"
            tools:text="12:54" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="3dp"
            android:textColor="@color/video_00c782"
            android:textSize="@dimen/common_text_sp_14"
            tools:text="呼叫中" />
    </LinearLayout>
</FrameLayout>