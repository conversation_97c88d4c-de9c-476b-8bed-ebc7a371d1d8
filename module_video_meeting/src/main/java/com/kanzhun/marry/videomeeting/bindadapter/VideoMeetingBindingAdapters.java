package com.kanzhun.marry.videomeeting.bindadapter;

import android.animation.ValueAnimator;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.databinding.BindingAdapter;
import androidx.databinding.BindingMethod;
import androidx.databinding.BindingMethods;

/**
 * <AUTHOR>
 * @date 2022/6/1.
 */
@BindingMethods({@BindingMethod(type = TextView.class, attribute = "movementMethod", method = "setMovementMethod")})
public class VideoMeetingBindingAdapters {

    @BindingAdapter({"show", "alphaS", "alphaE", "duration"})
    public static void setViewAlpha(View view, boolean show, float alphaS, float alphaE, long duration) {
        if (show) {
            view.setAlpha(alphaS);
            view.setVisibility(View.VISIBLE);
            ValueAnimator valueAnimator = ValueAnimator.ofFloat(alphaS, alphaE);
            valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                @Override
                public void onAnimationUpdate(ValueAnimator animation) {
                    float value = (float) animation.getAnimatedValue();
                    view.setAlpha(value);
                }
            });
            valueAnimator.setDuration(duration);
            valueAnimator.start();
        } else {
            view.setVisibility(View.GONE);
        }
    }

    @BindingAdapter({"connected", "inviteName"})
    public static void setInviteMeName(TextView textView, boolean connected, String inviteName) {
        StringBuilder stringBuilder = new StringBuilder();
        if (!TextUtils.isEmpty(inviteName)) {
            stringBuilder.append(inviteName);
            if (connected) {
                stringBuilder.append(" & ");
                stringBuilder.append("我");
            }
        }
        textView.setText(stringBuilder.toString());
    }
}
