package com.kanzhun.marry.videomeeting;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.Message;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.databinding.DataBindingUtil;

import com.sankuai.waimai.router.Router;
import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.AppUtil;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.common.util.StatusBarUtil;
import com.kanzhun.common.constract.BundleConstants;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.kernel.account.AccountHelper;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.media.IMeetingService;
import com.kanzhun.foundation.media.MediaConstants;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.page.VideoAudioFloatPageManager;
import com.kanzhun.foundation.ui.Navigation;
import com.kanzhun.marry.videomeeting.callback.VideoChatEngineListener;
import com.kanzhun.marry.videomeeting.databinding.VideoRemoteFloatAudioLayoutBinding;

/**
 * <AUTHOR>
 * @date 2022/6/2.
 */
public class VideoAudioFloatPage extends VideoAudioFloatBasePage {
    private WindowManager mWindowManager;
    private WindowManager.LayoutParams wmParams;
    private View mFloatingLayout;

    @Override
    public void create(Context context) {
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        //设置好悬浮窗的参数
        wmParams = new WindowManager.LayoutParams();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            wmParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            wmParams.type = WindowManager.LayoutParams.TYPE_PHONE;
        }
        wmParams.flags = WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;

        //设置悬浮窗口长宽数据
        wmParams.width = WindowManager.LayoutParams.MATCH_PARENT;
        wmParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
        // 悬浮窗默认显示以左上角为起始坐标
        wmParams.gravity = Gravity.LEFT | Gravity.TOP;
        //悬浮窗的开始位置，因为设置的是从右上角开始，所以屏幕左上角是x=0;y=0
        wmParams.x = 0;
        wmParams.y = StatusBarUtil.getStatusBarHeight(context);
        wmParams.format = PixelFormat.RGBA_8888;
        mFloatingLayout = getFloatingLayout(context);
        mWindowManager.addView(mFloatingLayout, wmParams);
        startValueAnimator();
        listener = Router.getService(IMeetingService.class, MediaConstants.ENGINE_LISTENER_KEY);
        if (listener != null) {
            listener.setAlive(true);
            listener.setListener(this);
            if (floatParamBean != null) {
                listener.setRoomId(floatParamBean.getRoomId());
                listener.initEngine(String.valueOf(AccountHelper.getInstance().getAccount().getUserId()), floatParamBean.getSenderId(), floatParamBean.getNebulaId(), VideoAudioFloatPage.this, Constants.VIDEO_CHAT_AUDIO);
            }
            if (listener.isConnected() || listener.isMyRoom()) {
                myHandler.removeMessages(VideoAndAudioChatActivity.S_HEART_MESSAGE_WHAT);
                myHandler.sendMessageDelayed(VideoAndAudioChatActivity.createHeartMessage(), VideoAndAudioChatActivity.S_HEART_INTERVAL);
            }
        }
    }

    @Override
    public View getFloatingLayout(Context context) {
        VideoRemoteFloatAudioLayoutBinding binding = DataBindingUtil.inflate(LayoutInflater.from(context), R.layout.video_remote_float_audio_layout, null, false);
        View view = binding.getRoot();
        if (floatParamBean != null) {
            Contact contact = ServiceManager.getInstance().getContactService().getContactById(floatParamBean.getSenderId());
            if (contact != null) {
                binding.setContact(contact);
            }
        }
        view.findViewById(R.id.cl_float).setOnClickListener(this);
        view.findViewById(R.id.iv_call_cancel).setOnClickListener(this);
        view.findViewById(R.id.iv_call).setOnClickListener(this);
        return view;
    }

    private void startValueAnimator() {
        ValueAnimator valueAnimator = new ValueAnimator();
        valueAnimator.setDuration(ANIMATION_TIME);
        valueAnimator.setFloatValues(0, 1);
        valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                if (mFloatingLayout != null) {
                    float value = (float) animation.getAnimatedValue();
                    float scale = value * 0.5F + 0.5F;
                    mFloatingLayout.setAlpha(value);
                    mFloatingLayout.setScaleY(scale);
                    mFloatingLayout.setScaleX(scale);
                }
            }
        });
        valueAnimator.start();
    }

    @Override
    public void destroy(String action) {
        super.destroy(action);
        if (listener != null) {
            listener.removeListener(this);
        }
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                ValueAnimator valueAnimator = new ValueAnimator();
                valueAnimator.setDuration(ANIMATION_TIME);
                valueAnimator.setFloatValues(1, 0);
                valueAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        if (mFloatingLayout != null) {
                            float value = (float) animation.getAnimatedValue();
                            float scale = value * 0.5F + 0.5F;
                            if (value <= 0) {
                                mFloatingLayout.setVisibility(View.GONE);
                            }
                            mFloatingLayout.setAlpha(value);
                            mFloatingLayout.setScaleY(scale);
                            mFloatingLayout.setScaleX(scale);
                        }
                    }
                });
                valueAnimator.addListener(new Animator.AnimatorListener() {
                    @Override
                    public void onAnimationStart(Animator animation) {

                    }

                    @Override
                    public void onAnimationEnd(Animator animation) {
                        // 移除悬浮窗口
                        if (mFloatingLayout != null) {
                            mWindowManager.removeView(mFloatingLayout);
                            mFloatingLayout = null;
                        }
                        mWindowManager = null;
                        wmParams = null;
                        VideoAudioFloatPageManager.getInstance().removePage(VideoAudioFloatPage.class);
                    }

                    @Override
                    public void onAnimationCancel(Animator animation) {
                        VideoAudioFloatPageManager.getInstance().removePage(VideoAudioFloatPage.class);
                    }

                    @Override
                    public void onAnimationRepeat(Animator animation) {

                    }
                });
                valueAnimator.start();
            }
        });
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.cl_float || id == R.id.iv_call) {
            if (floatParamBean != null) {
                Context context = BaseApplication.getApplication().getTopContext();
                Intent intent = Navigation.getVideoChatActivity(context);
                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_ROOM_ID, floatParamBean.getRoomId());
                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_TYPE, floatParamBean.getLinkType());
                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_INVITATION, floatParamBean.getSenderId());
                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_NEBULA_ID, floatParamBean.getNebulaId());
                intent.putExtra(BundleConstants.BUNDLE_VIDEO_CHAT_ACCEPT, id == R.id.iv_call ? true : false);
                if (!(context instanceof Activity)) {
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                }
                AppUtil.startActivity(context, intent);
            }
            destroy(VideoChatEngineListener.FLOAT_CLOSE);
        } else if (id == R.id.iv_call_cancel) {
            if (listener != null && floatParamBean != null) {
                listener.leaveRoom(VideoChatEngineListener.FLOAT_CLOSE, "");
                listener.refuse(floatParamBean.getRoomId(), BaseApplication.getApplication().getResources().getString(R.string.video_chat_refuse));
            }
        }
    }

    @Override
    public void onPeerConnectTimeout(String peerId) {

    }

    @Override
    public void leaveRoom(String action, String tips) {
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                destroy(action);
            }
        });
    }

    @Override
    public void setSelfInvisible() {

    }

    @Override
    public void changeAudio() {

    }

    @Override
    public void videoAccept() {

    }

    @Override
    public void connectOverLocalTime() {
        destroy(VideoChatEngineListener.OVER_LOCAL_TIME);
        if (listener != null && floatParamBean != null) {
            listener.refuse(floatParamBean.getRoomId(), BaseApplication.getApplication().getResources().getString(R.string.video_chat_refuse));
        }
    }

    @Override
    public void handleMessage(@NonNull Message msg) {

    }
}
