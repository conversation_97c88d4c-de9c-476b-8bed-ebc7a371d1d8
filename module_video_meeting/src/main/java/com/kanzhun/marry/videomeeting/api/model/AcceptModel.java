package com.kanzhun.marry.videomeeting.api.model;

/**
 * <AUTHOR>
 * @date 2022/5/30.
 */
public class AcceptModel {
    public String roomId; // 房间 id
    public String confCode;// 会议资源 id
    public String creatorId; // 房间创建者 id
    public String hostId; // 房间主持人 id
    public int status;// 房间状态 1 初始化 2 使用中 3 已关闭 4 已销毁
    public String theme;// 房间主题
    public int linkType;// 1 - 语音 2- 视频
    public int roomType; // 1 单聊 2-会议
    public long beginTime;// 实际开始时间 第一个用户进入房间时间 ，第一次查询该接口更新
}
