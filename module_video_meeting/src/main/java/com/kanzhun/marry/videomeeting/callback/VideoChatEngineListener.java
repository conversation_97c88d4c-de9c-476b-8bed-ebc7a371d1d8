package com.kanzhun.marry.videomeeting.callback;

import android.content.Context;
import android.media.MediaPlayer;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.kanzhun.common.base.BaseApplication;
import com.kanzhun.common.util.ExecutorFactory;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.base.HostConfig;
import com.kanzhun.foundation.facade.VideoMessageRepository;
import com.kanzhun.foundation.media.IMeetingService;
import com.kanzhun.foundation.media.MediaConstants;
import com.kanzhun.foundation.model.message.MessageForAction;
import com.kanzhun.foundation.utils.HeadsetPlugManager;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.videomeeting.ForegroundService;
import com.kanzhun.marry.videomeeting.R;
import com.kanzhun.marry.videomeeting.api.VideoMeetingApi;
import com.kanzhun.marry.videomeeting.api.model.MeetingAuthModel;
import com.kanzhun.utils.T;
import com.sankuai.waimai.router.annotation.RouterProvider;
import com.sankuai.waimai.router.annotation.RouterService;
import com.sdk.nebulartc.NebulaRtcCloud;
import com.sdk.nebulartc.bean.NebulaRtcParams;
import com.sdk.nebulartc.constant.NebulaRtcDef;
import com.sdk.nebulartc.listener.NebulaRtcCloudListener;
import com.sdk.nebulartc.manager.NebulaRtcDeviceManager;
import com.techwolf.lib.tlog.TLog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

import io.reactivex.rxjava3.core.Observable;


@RouterService(interfaces = IMeetingService.class, key = MediaConstants.ENGINE_LISTENER_KEY, singleton = true)
public class VideoChatEngineListener extends NebulaRtcCloudListener implements IMeetingService {
    public static final String TAG = "VideoChatEngineListener";
    public static final String INVITATION_NULL = "001";//邀请人信息为空
    public static final String LINK_OVER_CODE = "002";//2073
    public static final String END_CALL_CLICK = "003";//点击取消按钮
    public static final String OVER_LOCAL_TIME = "004";//本地60s时间超时
    public static final String FLOAT_CLOSE = "005";//悬浮窗，主动关闭
    public static final String ENTER_ROOM_FAIL = "006";//sdk进入房间失败
    public static final String EXIST_ROOM = "007";//sdk退出房间
    public static final String REMOTE_EXIST_ROOM = "008";//sdk远程用户退出房间
    public static final String OTHER_ERROR = "009";//其他错误
    public static final String LOGOUT_ERROR = "010";//退出登录
    public static final String SDK_SYNC_ERROR = "011";//sdk初始化完成，但是没有回调UI，sdk初始化中，语音被取消
    private Handler mHandler;
    protected NebulaRtcCloud engine;
    private Context mContext;
    private String mSelfId;
    private String mPeerId;
    private String mNebulaId;
    private boolean selfMute = false;
    private List<VideoChatListener> mListeners = new CopyOnWriteArrayList<>();
    private boolean hasJoinRoom;
    private boolean mAudioSpeaker;//当前声音模式，如带耳机，会强制转换
    private boolean isInit;
    private boolean alive;//是否启动了语音通话，并且未结束状态
    private boolean isConnected;
    private boolean isMyRoom;
    private int linkType;
    private String roomId;
    private boolean isWindowShow;
    private boolean isSpeaker;//耳机模式下保证可以还原到原有声音模式下
    private AtomicBoolean isCreateRoom = new AtomicBoolean(false);
    private AtomicBoolean hasRelease = new AtomicBoolean(false);//已经释放了所有
    private MediaPlayer mediaWaitingPlayer;
    private MediaPlayer mediaClosePlayer;
    private int mHeadsetType = HeadsetPlugManager.NO_HEADSET; //是否有耳机;
    public static final VideoChatEngineListener INSTANCE = new VideoChatEngineListener();
    public static final int OVER_TIME_WHAT = 300;
    public static final int PHONE_NOT_AROUND_WHAT = 301;
    private ObservableField<String> mTips = new ObservableField<String>();
    public static final int OVER_TIME_DELAY = 60000;
    public static final int PHONE_NOT_AROUND_TIME_DELAY = 20000;
    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case OVER_TIME_WHAT:
                    for (VideoChatListener callback : mListeners) {
                        callback.connectOverLocalTime();
                    }
                    leaveRoom(OVER_LOCAL_TIME, "");
                    break;
                case PHONE_NOT_AROUND_WHAT:
                    mTips.set(BaseApplication.getApplication().getResources().getString(R.string.video_meeting_phone_not_around));
                    break;
            }
        }
    };

    public void cleanOverTimeMessage() {
        handler.removeMessages(OVER_TIME_WHAT);
        handler.removeMessages(PHONE_NOT_AROUND_WHAT);
    }

    // 使用注解声明该方法是一个Provider
    @RouterProvider
    public static VideoChatEngineListener provideInstance() {
        return INSTANCE;
    }

    public boolean isMyRoom() {
        return isMyRoom;
    }

    public void setMyRoom(boolean myRoom) {
        isMyRoom = myRoom;
        if (isMyRoom) {
            mTips.set(BaseApplication.getApplication().getResources().getString(R.string.video_chat_waiting));
            handler.sendEmptyMessageDelayed(PHONE_NOT_AROUND_WHAT, PHONE_NOT_AROUND_TIME_DELAY);
        }
    }

    public int getLinkType() {
        return linkType;
    }

    public void setLinkType(int linkType) {
        this.linkType = linkType;
    }

    public String getNebulaId() {
        return mNebulaId;
    }

    public void setNebulaId(String mNebulaId) {
        this.mNebulaId = mNebulaId;
    }

    public String getPeerId() {
        return mPeerId;
    }

    public void setPeerId(String mPeerId) {
        this.mPeerId = mPeerId;
    }

    @Override
    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public VideoChatEngineListener() {
        mContext = BaseApplication.getApplication();
    }

    public String getSelfId() {
        return mSelfId;
    }

    public ObservableField<String> getTips() {
        return mTips;
    }

    @Override
    public void setAlive(boolean alive) {
        this.alive = alive;
        hasRelease.set(alive);
    }

    public void initEngine(String selfId, String peerId, String nebulaId, VideoChatListener listener, int linkType) {
        mSelfId = selfId;
        mPeerId = peerId;
        mNebulaId = nebulaId;
        this.linkType = linkType;
        setListener(listener);
        TLog.info(VideoChatEngineListener.TAG, "Engine init");
        initSDK();
    }

    public void setListener(VideoChatListener listener) {
        if (!mListeners.contains(listener)) {
            mListeners.add(listener);
        }
    }

    public void removeListener(VideoChatListener listener) {
        mListeners.remove(listener);
    }

    private void initSDK() {
        isInit = true;
        isCreateRoom.set(true);
        engine = NebulaRtcCloud.sharedInstance(mContext);
        engine.setEnv(HostConfig.getCONFIG().getVideoChatAddr());
        //默认使用听筒播放
        engine.getDeviceManager().setDefaultAudioRoute(NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_EARPIECE);
        TLog.error("VideoChatEngineListener", "sdk onSuccess  roomId=" + roomId);
        setRTCCloudListener(engine);
        startLocalAudio();
        for (VideoChatListener l : mListeners) {
            l.onSdkReady();
        }
        initWaitingMediaPlayer();
        if (mListeners.isEmpty()) {
            TLog.error("VideoChatEngineListener", "mListeners empty");
            leaveRoom(SDK_SYNC_ERROR, "");
        }
    }


    private void release() {
        mTips.set(BaseApplication.getApplication().getResources().getString(R.string.video_chat_waiting));
        isCreateRoom.set(false);
        hasRelease.set(false);
        alive = false;
        isInit = false;
        engine = null;
        roomId = "";
        mNebulaId = "";
        mSelfId = "";
        mPeerId = "";
        isConnected = false;
        selfMute = false;
        hasJoinRoom = false;
        mAudioSpeaker = false;
        isWindowShow = false;
        isMyRoom = false;
        linkType = Constants.VIDEO_CHAT_AUDIO;
        mListeners.clear();
        destroyWaitingMediaPlayer();
        ForegroundService.stopForegroundService();
    }

    /**
     * 初始化铃声播放器
     */
    public void initWaitingMediaPlayer() {
        if (mediaWaitingPlayer == null) {
            mediaWaitingPlayer = MediaPlayer.create(mContext, R.raw.video_chat_ring);
            mediaWaitingPlayer.setLooping(true);
            mediaWaitingPlayer.start();
        }
    }

    /**
     * 销毁铃声播放器
     */
    public void destroyWaitingMediaPlayer() {
        if (mediaWaitingPlayer != null) {
            mediaWaitingPlayer.stop();
            mediaWaitingPlayer.reset();
            mediaWaitingPlayer = null;
        }
    }

    /**
     * 初始化铃声播放器
     */
    public void initCloseMediaPlayer() {
        mediaClosePlayer = MediaPlayer.create(mContext, R.raw.video_chat_ring_over);
        mediaClosePlayer.setLooping(false);
        mediaClosePlayer.start();
        ExecutorFactory.execMainTaskDelay(new Runnable() {
            @Override
            public void run() {
                destroyCloseMediaPlayer();
            }
        }, 1000);
    }

    /**
     * 销毁铃声播放器
     */
    public void destroyCloseMediaPlayer() {
        if (mediaClosePlayer != null) {
            mediaClosePlayer.stop();
            mediaClosePlayer.reset();
            mediaClosePlayer = null;
        }
    }

    public void joinRoom() {
        if (engine == null) {
            joinError();
            return;
        }

        Observable<BaseResponse<MeetingAuthModel>> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).meetingAuth();
        HttpExecutor.execute(observable, new BaseRequestCallback<MeetingAuthModel>() {
            @Override
            public void onSuccess(MeetingAuthModel data) {
                if (data != null && !TextUtils.isEmpty(data.getAppId())
                        && !TextUtils.isEmpty(data.getSignature())
                        && !TextUtils.isEmpty(data.getAuthorization())) {
                    NebulaRtcParams params = new NebulaRtcParams();
                    params.setAppId(data.getAppId());
                    params.setRoomId(mNebulaId);
                    params.setSig(data.getSignature());
                    params.setAuth(data.getAuthorization());
                    params.setUserId(mSelfId);
                    params.setRole(NebulaRtcDef.NEBULA_RTC_ROLE_CODE_ANCHOR);
                    engine.enterRoom(params, NebulaRtcDef.NEBULA_RTC_APP_SCENE_VIDEOCALL);
                } else {
                    joinError();
                }

            }

            @Override
            public void dealFail(ErrorReason reason) {
                joinError();
            }
        });

    }

    private void joinError() {
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.onError(-1, "engine init error");
            }
        }
        leaveRoom(OTHER_ERROR, "");
    }


    protected void setRTCCloudListener(NebulaRtcCloud engine) {
        if (engine != null) {
            if (mHandler == null) {
                mHandler = new Handler();
            }
            engine.setListenerHandler(mHandler);
            engine.setListener(this);
            TLog.info(TAG, "rtc setRTCCloudListener");
        } else {
            TLog.error(TAG, "engine init SDK ERROR");
        }
    }

    /**
     * 开启本地 音频
     */
    public void startLocalAudio() {
        if (engine != null) {
            engine.startLocalAudio();
            TLog.error(TAG, "startLocalAudio---------------");
        }
    }

    public void onPause() {
//        if (mediaWaitingPlayer != null) {
//            mediaWaitingPlayer.pause();
//        }
    }

    public void onStop() {

    }

    public void onResume() {
//        if (!isConnected()) {
//            if (mediaWaitingPlayer != null && !mediaWaitingPlayer.isPlaying()) {
//                try {
//                    mediaWaitingPlayer.prepare();
//                    mediaWaitingPlayer.start();
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//            }
//        }
    }

    public void stopLocalAudio() {
        if (engine != null) {
            engine.stopLocalAudio();
        }
    }

    public void setSelfOutputMute(boolean mute) {
        if (engine != null) {
            engine.muteLocalAudio(mute);
        }
        this.selfMute = mute;
    }

    /**
     * 判断 是否禁音
     *
     * @return
     */
    public boolean isSelfMute() {
        return selfMute;
    }

    public void leaveRoom(String action, String tips) {
        TLog.error(TAG, "action=" + action);
        cleanOverTimeMessage();
        if (isCreateRoom.compareAndSet(true, false)) {
            if (isConnected) {
                Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).leave(roomId);
                HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
                    @Override
                    public void onSuccess() {

                    }

                    @Override
                    public void dealFail(ErrorReason reason) {

                    }
                });
            }
            ExecutorFactory.execMainTask(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (engine != null) {
                            stopLocalAudio();
                            TLog.error(TAG, "engine  destroy   hasJoinRoom=" + hasJoinRoom);
                            if (hasJoinRoom) {
                                engine.exitRoom();
                            }
                            NebulaRtcCloud.destroySharedInstance();
                        }
                    } catch (Exception e) {

                    }
                }
            });
        } else {
            // 部分从引擎退出导致的退出房间没有提示语，需要补充提示语
            if (!TextUtils.isEmpty(tips)) {
                T.bSS(tips);
            }
        }
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                TLog.error(TAG, "leaveRoom  listener");
                listener.leaveRoom(action, tips);
            }
        }
        mListeners.clear();
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                TLog.error(TAG, "leaveRoom  end");
                destroyWaitingMediaPlayer();
                if (hasRelease.compareAndSet(true, false)) {
                    initCloseMediaPlayer();
                }
                release();
            }
        });
    }

    @Override
    public void onError(int errCode, String errMsg, Bundle extraInfo) {
        super.onError(errCode, errMsg, extraInfo);
        TLog.info(TAG, "onError errMsg : %s", errMsg);
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.onError(errCode, errMsg);
            }
        }
        leaveRoom(OTHER_ERROR, "");
    }

    /**
     * @param result >=0表示成功，<0表示失败（sdk文档上面没有=0的情况说明，其实也是成功，result是进房间延迟）
     */
    @Override
    public void onEnterRoom(long result) {
        super.onEnterRoom(result);
        TLog.info(TAG, "onEnterRoom result : %d", result);
        hasJoinRoom = result >= 0;
        if (hasJoinRoom) {

            for (VideoChatListener listener : mListeners) {
                if (listener != null) {
                    listener.onSelfEnterRoom(mSelfId);
                }
            }
        } else {
            //进入房间失败
            for (VideoChatListener listener : mListeners) {
                if (listener != null) {
                    listener.onError(-1, "连接异常，请重试");
                }
            }
            leaveRoom(ENTER_ROOM_FAIL, "");
        }
    }

    @Override
    public void onExitRoom(int reason) {
        super.onExitRoom(reason);
        TLog.info(TAG, "onExitRoom reason : %d", reason);
        leaveRoom(EXIST_ROOM, "");
    }

    @Override
    public void onRemoteUserEnterRoom(String userId) {
        super.onRemoteUserEnterRoom(userId);
        TLog.info(TAG, "onRemoteUserEnterRoom , userId : %s", userId);
        isConnected = true;
        cleanOverTimeMessage();
        destroyWaitingMediaPlayer();
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.onPeerEnterRoom(userId);
            }
        }
    }

    @Override
    public void onRemoteUserLeaveRoom(String userId, int reason) {
        super.onRemoteUserLeaveRoom(userId, reason);
        TLog.info(TAG, "onRemoteUserLeaveRoom userId : %s, reason : %d, size = %d", userId, reason, mListeners.size());
        leaveRoom(REMOTE_EXIST_ROOM, "");
    }

    @Override
    public void onUserAudioAvailable(String userId, boolean available) {
        super.onUserAudioAvailable(userId, available);
        TLog.info(TAG, "onUserAudioAvailable userId : %s, available : %s", userId, available);
        if (engine != null) {
            engine.muteRemoteAudio(userId, !available);
        }
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.onUserAudioAvailable(userId, available);
            }
        }
    }

    @Override
    public void onUserVideoAvailable(String userId, boolean available) {
        super.onUserVideoAvailable(userId, available);
        TLog.info(TAG, "onUserVideoAvailable userId : %s, available : %s", userId, available);
    }

    @Override
    public void onConnectionLostTimeout() {
        super.onConnectionLostTimeout();
        TLog.info(TAG, "onConnectionLostTimeout");

    }

    @Override
    public void onCameraDidReady() {
        super.onCameraDidReady();
        TLog.info(TAG, "onCameraDidReady");
    }

    @Override
    public void onNetworkQuality(NebulaRtcDef.NebulaRtcQuality localQuality, List<NebulaRtcDef.NebulaRtcQuality> remoteQuality) {
        super.onNetworkQuality(localQuality, remoteQuality);
        if (localQuality != null && localQuality.quality > NebulaRtcDef.NEBULA_RTC_NETWORK_QUALITY_POOR) {
            for (VideoChatListener listener : mListeners) {
                if (listener != null) {
                    listener.onSelfOffline(localQuality.userId, localQuality.quality);
                }
            }
        }
    }

    @Override
    public void onUserVoiceVolume(ArrayList<NebulaRtcDef.NebulaRtcVolumeInfo> userVolumes, int totalVolume) {
        super.onUserVoiceVolume(userVolumes, totalVolume);
        TLog.info(TAG, "onUserVoiceVolume");
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.onUserVoiceVolume(userVolumes, totalVolume);
            }
        }
    }


    public void setAudioSpeaker(boolean audioSpeaker) {
        if (engine != null) {
            mAudioSpeaker = audioSpeaker;
            TLog.info(TAG, "setAudioSpeaker : %s", audioSpeaker);
            NebulaRtcDeviceManager deviceManager = engine.getDeviceManager();
            if (deviceManager != null) {
                deviceManager.setAudioRoute(getAudioRouteCode());
            }
        }
    }

    private String getAudioRouteCode() {
        if(mHeadsetType == HeadsetPlugManager.NO_HEADSET){
            return mAudioSpeaker ? NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_SPEAKER : NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_EARPIECE;
        }else if(mHeadsetType == HeadsetPlugManager.WIRED_HEADSET){
            return NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_HEADSET;
        }else if(mHeadsetType == HeadsetPlugManager.BLUETOOTH_HEADSET) {
            return NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_HEADSETBLUETOOTH;
        }else{
            return NebulaRtcDef.NEBULA_RTC_TYPE_AUDIO_OUTPUT_DEVICE_SPEAKER;
        }
    }

    public boolean getAudioSpeaker() {
        return mAudioSpeaker;
    }

    @Override
    public void onFirstVideoFrame(String userId, int streamType, int width, int height) {
        super.onFirstVideoFrame(userId, streamType, width, height);
    }

    public boolean isInit() {
        return isInit;
    }

    public boolean isConnected() {
        return isConnected;
    }

    public void setConnected(boolean connected) {
        isConnected = connected;
    }

    public void setServiceInvisible() {
        setWindowShow(false);
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.setSelfInvisible();
            }
        }
    }

    @Override
    public boolean isWindowShow() {
        return isWindowShow;
    }

    public void setWindowShow(boolean windowShow) {
        this.isWindowShow = windowShow;
    }

    public void changeAudio() {
//        setLinkType(Constants.VIDEO_CHAT_AUDIO);
//        setSelfOutputVideo(true);
//        for (VideoChatListener listener : mListeners) {
//            if (listener != null) {
//                listener.changeAudio();
//            }
//        }
    }

    public boolean isSpeaker() {
        return isSpeaker;
    }

    public void setSpeaker(boolean speaker) {
        isSpeaker = speaker;
    }

    @Override
    public void videoAbnormal(String action) {
        String tips = "";
        if (TextUtils.equals(action, MessageForAction.TYPE_VIDEO_CHAT_REFUSE)) {
            tips = "对方拒绝了你的通话请求";
        } else if (TextUtils.equals(action, MessageForAction.TYPE_VIDEO_CHAT_OVER)) {
            tips = mContext.getResources().getString(R.string.video_chat_peer_over);
        } else if (TextUtils.equals(action, MessageForAction.TYPE_VIDEO_CHAT_BROKEN)) {
            tips = mContext.getResources().getString(R.string.video_chat_peer_over);
        }
        leaveRoom(action, tips);
    }

    @Override
    public void videoAccept() {
        TLog.error(TAG, "accept==========");
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                destroyWaitingMediaPlayer();
                cleanOverTimeMessage();
                for (VideoChatListener listener : mListeners) {
                    if (listener != null) {
                        listener.videoAccept();
                    }
                }
            }
        });
    }

    @Override
    public void videoNetworkQuality(VideoMessageRepository.VideoChatNetStatus status) {
        if (status.networkQuality > NebulaRtcDef.NEBULA_RTC_NETWORK_QUALITY_POOR) {
            T.bSSD(R.string.video_chat_other_networkQuality_bad);
        }
    }

    @Override
    public void changeToAudio() {
//        ExecutorFactory.execMainTask(new Runnable() {
//            @Override
//            public void run() {
//                changeAudio();
//            }
//        });
    }

    @Override
    public void postLocalOverTimeMessage() {
        handler.removeMessages(OVER_TIME_WHAT);
        handler.sendEmptyMessageDelayed(OVER_TIME_WHAT, OVER_TIME_DELAY);
    }

    /**
     * 用户退出登录调用
     */
    @Override
    public void close() {
        if (!TextUtils.isEmpty(roomId) && !isConnected) {
            if (isMyRoom) {
                cancelVideo(roomId, "");
            } else {
                refuse(roomId, "");
            }
        }
        cleanOverTimeMessage();
        if (isCreateRoom.compareAndSet(true, false)) {
            if (isConnected) {
                Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).leave(roomId);
                HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
                    @Override
                    public void onSuccess() {

                    }

                    @Override
                    public void dealFail(ErrorReason reason) {

                    }
                });
            }
            ExecutorFactory.execMainTask(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (engine != null) {
                            stopLocalAudio();
                            if (hasJoinRoom) {
                                engine.exitRoom();
                            }
                            NebulaRtcCloud.destroySharedInstance();
                        }
                    } catch (Exception e) {

                    }
                }
            });
        }
        for (VideoChatListener listener : mListeners) {
            if (listener != null) {
                listener.leaveRoom(LOGOUT_ERROR, "");
            }
        }
        mListeners.clear();
        ExecutorFactory.execMainTask(new Runnable() {
            @Override
            public void run() {
                release();
            }
        });
    }

    @Override
    public boolean alive() {
        return alive;
    }

    public int getHeadsetType() {
        return mHeadsetType;
    }

    public void setHeadsetType(int mHeadsetType) {
        this.mHeadsetType = mHeadsetType;
    }

    public void refuse(String roomId, String tips) {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).refuse(roomId);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                T.bSS(tips);
            }
        });

    }

    public void cancelVideo(String roomId, String tips) {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).cancel(roomId);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {

            }

            @Override
            public void dealFail(ErrorReason reason) {

            }

            @Override
            public void onComplete() {
                super.onComplete();
                T.bSS(tips);
            }
        });

    }
}
