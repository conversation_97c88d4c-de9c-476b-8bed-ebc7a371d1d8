package com.kanzhun.marry.videomeeting.viewmodel;

import android.app.Application;

import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;
import androidx.lifecycle.MutableLiveData;

import com.sankuai.waimai.router.Router;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.api.model.CreateModel;
import com.kanzhun.foundation.base.FoundationViewModel;
import com.kanzhun.foundation.logic.service.ServiceManager;
import com.kanzhun.foundation.media.IMeetingService;
import com.kanzhun.foundation.media.MediaConstants;
import com.kanzhun.foundation.model.Contact;
import com.kanzhun.foundation.model.User;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.callback.SimpleRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.videomeeting.ForegroundService;
import com.kanzhun.marry.videomeeting.api.VideoMeetingApi;
import com.kanzhun.marry.videomeeting.api.model.AcceptModel;
import com.kanzhun.marry.videomeeting.api.model.HeartbeatModel;
import com.kanzhun.marry.videomeeting.callback.VideoChatEngineListener;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.disposables.Disposable;

public class VideoAndAudioChatViewModel extends FoundationViewModel {
    private static final String TAG = "VideoAndAudioChatViewModel";
    private String mRoomId;//服务端虚拟房间号，与音视频SDK中的NebulaId一一对应
    private ObservableField<Contact> invitation = new ObservableField<>();
    private ObservableField<String> meAvatar = new ObservableField<>();
    private ObservableField<String> mTips = new ObservableField<>();
    private MutableLiveData<String> mErrorString = new MutableLiveData<>();
    private MutableLiveData<Boolean> heartbeat = new MutableLiveData<>();
    private boolean HeadsetInit; //耳机是否初始化完成
    private int networkQuality = 1;
    private MutableLiveData<Boolean> inviteSuccess = new MutableLiveData<>();
    private MutableLiveData<Integer> busyCode = new MutableLiveData<>();
    private int linkType = Constants.VIDEO_CHAT_AUDIO;
    private ObservableBoolean isConnected = new ObservableBoolean(false);//是否语音通话连接成功
    private ObservableBoolean isMyRoom = new ObservableBoolean(false);//是否是我自己的房间
    private MutableLiveData<Boolean> isOver = new MutableLiveData<>(false);
    private MutableLiveData<String> changeToConnected = new MutableLiveData<>();
    private MutableLiveData<CreateModel> callRoom = new MutableLiveData<>();
    VideoChatEngineListener engineListener;
    private String mNebulaId;
    private String mInvitationId;
    private LinkedHashMap<Observable, Disposable> disposableCache = new LinkedHashMap<>();

    public VideoAndAudioChatViewModel(Application application) {
        super(application);
        ForegroundService.startForegroundService("语音通话中...");
        engineListener = Router.getService(IMeetingService.class, MediaConstants.ENGINE_LISTENER_KEY);
        if (engineListener != null) {
            mTips = engineListener.getTips();
        }
    }

    public MutableLiveData<String> getChangeToConnected() {
        return changeToConnected;
    }

    public MutableLiveData<Integer> getBusyCode() {
        return busyCode;
    }

    public MutableLiveData<CreateModel> getCallRoom() {
        return callRoom;
    }

    public void setRoomId(String mRoomId) {
        this.mRoomId = mRoomId;
    }

    public String getRoomId() {
        return mRoomId;
    }

    public String getNebulaId() {
        return mNebulaId;
    }

    public void setNebulaId(String mNebulaId) {
        this.mNebulaId = mNebulaId;
    }

    public String getInvitationId() {
        return mInvitationId;
    }

    public void setInvitationId(String mInvitationId) {
        this.mInvitationId = mInvitationId;
        setInvitation(mInvitationId);
    }

    public ObservableField<Contact> getInvitation() {
        return invitation;
    }

    public ObservableField<String> getMeAvatar() {
        return meAvatar;
    }

    public ObservableField<String> getTips() {
        return mTips;
    }

    public void setTips(String tips) {
        mTips.set(tips);
    }

    public void setTipString(String tips) {
        mTips.set(tips);
    }

    public ObservableBoolean getIsConnected() {
        return isConnected;
    }

    public void setIsConnected(boolean isConnected) {
        this.isConnected.set(isConnected);
    }

    public ObservableBoolean getIsMyRoom() {
        return isMyRoom;
    }

    public void setIsMyRoom(boolean isMyRoom) {
        this.isMyRoom.set(isMyRoom);
    }

    public void setInvitation(String invitationId) {
        try {
            Contact invitation = ServiceManager.getInstance().getContactService().getContactById(invitationId);
            this.invitation.set(invitation);
            User user = ServiceManager.getInstance().getProfileService().getUserLiveData().getValue();
            if (user != null) {
                this.meAvatar.set(user.getTinyAvatar());
            }
        } catch (Exception e) {
            mErrorString.setValue("没有找到该用户");
        }
    }

    public void invite() {
        Observable<BaseResponse> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).invite(mRoomId, mInvitationId, linkType);
        HttpExecutor.requestSimple(observable, new SimpleRequestCallback() {
            @Override
            public void onSuccess() {
                setIsMyRoom(true);
                inviteSuccess.postValue(true);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason.getErrCode() == Constants.LINK_USER_BUSY_CODE) {
                    busyCode.postValue(Constants.LINK_USER_BUSY_CODE);
                } else {
                    mErrorString.setValue(reason.getErrReason());
                }
            }
        });
    }

    public void createCallRoom() {
        Observable<BaseResponse<CreateModel>> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).createVideoMeeting(mInvitationId, linkType);
        HttpExecutor.execute(observable, new BaseRequestCallback<CreateModel>() {
            @Override
            public void handleInChildThread(CreateModel data) {
                super.handleInChildThread(data);
                mNebulaId = data.nebulaId;
                mRoomId = data.roomId;
                engineListener.setRoomId(data.roomId);
                callRoom.postValue(data);
            }

            @Override
            public void onSuccess(CreateModel data) {

            }

            @Override
            public void dealFail(ErrorReason reason) {
                mErrorString.setValue(reason.getErrReason());
            }
        });

    }

    public MutableLiveData<String> getErrorString() {
        return mErrorString;
    }

    /**
     * 接受聊天
     */
    public void accept() {
        accept(getRoomId(), false);
    }

    public void accept(String roomId, boolean showConnect) {
        Observable<BaseResponse<AcceptModel>> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).accept(roomId);
        HttpExecutor.execute(observable, new BaseRequestCallback<AcceptModel>() {
            @Override
            public void onSuccess(AcceptModel data) {
                if (showConnect) {
                    changeToConnected.postValue(roomId);
                }
            }

            @Override
            public void dealFail(ErrorReason reason) {
                mErrorString.setValue(reason.getErrReason());
            }
        });
    }

    public void heartbeat(String roomId, int networkQuality) {
        Observable<BaseResponse<HeartbeatModel>> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).reportHeartbeat(roomId, networkQuality, linkType);
        HttpExecutor.execute(observable, new BaseRequestCallback<HeartbeatModel>() {
            @Override
            public void onStart(Disposable disposable) {
                super.onStart(disposable);
                if (disposableCache.size() > 5) {//防止心跳请求缓存太多，超过5个，清除旧的缓存
                    disposableCache.clear();
                }
                disposableCache.put(observable, disposable);
            }

            @Override
            public void onSuccess(HeartbeatModel data) {
            }

            @Override
            public void onComplete() {
                super.onComplete();
                disposableCache.remove(observable);
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason.getErrCode() == Constants.LINK_OVER_CODE) {
                    isOver.postValue(true);
                }
            }
        });
    }

    public void heartbeat() {
        heartbeat(getRoomId(), 1);
    }

    public void cancelHeartBeatRequest() {
        Iterator<Map.Entry<Observable, Disposable>> iterator = disposableCache.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Observable, Disposable> entry = iterator.next();
            Disposable disposable = entry.getValue();
            if (disposable != null && !disposable.isDisposed()) {
                disposable.dispose();
            }
            iterator.remove();
        }
    }

    public MutableLiveData<Boolean> getHeartbeat() {
        return heartbeat;
    }

    public boolean isHeadsetInit() {
        return HeadsetInit;
    }

    public void setHeadsetInit(boolean headsetInit) {
        HeadsetInit = headsetInit;
    }

    public int getNetworkQuality() {
        return networkQuality;
    }

    public void setNetworkQuality(int networkQuality) {
        this.networkQuality = networkQuality;
    }

    public int getLinkType() {
        return linkType;
    }

    public void setLinkType(int linkType) {
        this.linkType = linkType;
    }

    public MutableLiveData<Boolean> getInviteSuccess() {
        return inviteSuccess;
    }

    public MutableLiveData<Boolean> getIsOver() {
        return isOver;
    }
}