package com.kanzhun.marry.videomeeting;

import android.os.Handler;
import android.os.Message;
import android.view.View;

import androidx.annotation.NonNull;

import com.sdk.nebulartc.constant.NebulaRtcDef;
import com.kanzhun.foundation.Constants;
import com.kanzhun.foundation.page.FloatParamBean;
import com.kanzhun.foundation.page.VideoAudioFloatPageInterface;
import com.kanzhun.http.HttpExecutor;
import com.kanzhun.http.RetrofitManager;
import com.kanzhun.http.callback.BaseRequestCallback;
import com.kanzhun.http.error.ErrorReason;
import com.kanzhun.http.response.BaseResponse;
import com.kanzhun.marry.videomeeting.api.VideoMeetingApi;
import com.kanzhun.marry.videomeeting.api.model.HeartbeatModel;
import com.kanzhun.marry.videomeeting.callback.VideoChatEngineListener;
import com.kanzhun.marry.videomeeting.callback.VideoChatListener;

import java.lang.ref.WeakReference;
import java.util.ArrayList;

import io.reactivex.rxjava3.core.Observable;

/**
 * <AUTHOR>
 * @date 2022/6/10.
 */
public abstract class VideoAudioFloatBasePage implements VideoAudioFloatPageInterface, View.OnClickListener, VideoChatListener {
    public static final long ANIMATION_TIME = 300L;
    FloatParamBean floatParamBean;
    VideoChatEngineListener listener;
    int mLinkType;
    String roomId;
    MyHandler myHandler = new MyHandler(VideoAudioFloatBasePage.this);

    @Override
    public void destroy(String action) {
        if (myHandler != null) {
            myHandler.removeMessages(VideoAndAudioChatActivity.S_HEART_MESSAGE_WHAT);
        }
    }

    @Override
    public void setFloatParam(FloatParamBean param) {
        this.floatParamBean = param;
        this.mLinkType = param.getLinkType();
        this.roomId = param.getRoomId();
    }

    @Override
    public void onSdkReady() {

    }

    @Override
    public void onSelfEnterRoom(String selfId) {

    }

    @Override
    public void onSelfOffline(String selfId, int networkQuality) {
        myHandler.removeMessages(VideoAndAudioChatActivity.S_HEART_MESSAGE_WHAT);
        heartbeat(roomId, networkQuality);
        myHandler.sendMessageDelayed(VideoAndAudioChatActivity.createHeartMessage(), VideoAndAudioChatActivity.S_HEART_INTERVAL);
    }

    @Override
    public void onPeerEnterRoom(String peerId) {

    }

    @Override
    public void onUserVoiceVolume(ArrayList<NebulaRtcDef.NebulaRtcVolumeInfo> userVolumes, int i) {

    }

    @Override
    public void onPeerConnectTimeout(String peerId) {

    }

    @Override
    public void onError(int errCode, String errMsg) {
        destroy(VideoChatEngineListener.OTHER_ERROR);
    }

    @Override
    public void onUserAudioAvailable(String userId, boolean available) {

    }

    @Override
    public void leaveRoom(String action, String tips) {

    }

    @Override
    public void setSelfInvisible() {

    }

    @Override
    public void changeAudio() {

    }

    @Override
    public void videoAccept() {

    }

    @Override
    public void connectOverLocalTime() {

    }

    public void handleMessage(@NonNull Message msg) {
        switch (msg.what) {
            case VideoAndAudioChatActivity.S_HEART_MESSAGE_WHAT:
                heartbeat(floatParamBean.getRoomId());
                myHandler.sendMessageDelayed(VideoAndAudioChatActivity.createHeartMessage(), VideoAndAudioChatActivity.S_HEART_INTERVAL);
                break;
        }
    }

    public class MyHandler extends Handler {
        WeakReference<VideoAudioFloatBasePage> reference;

        public MyHandler(VideoAudioFloatBasePage page) {
            reference = new WeakReference<>(page);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            if (null != reference) {
                VideoAudioFloatBasePage page = reference.get();
                if (null != page) {
                    page.handleMessage(msg);
                }
            }
        }
    }

    public void heartbeat(String roomId) {
        heartbeat(roomId, 1);
    }

    public void heartbeat(String roomId, int networkQuality) {
        Observable<BaseResponse<HeartbeatModel>> observable = RetrofitManager.getInstance().createApi(VideoMeetingApi.class).reportHeartbeat(roomId, networkQuality, mLinkType);
        HttpExecutor.execute(observable, new BaseRequestCallback<HeartbeatModel>() {
            @Override
            public void onSuccess(HeartbeatModel data) {
            }

            @Override
            public void dealFail(ErrorReason reason) {
                if (reason.getErrCode() == Constants.LINK_OVER_CODE) {
                    if (listener != null) {
                        listener.leaveRoom(VideoChatEngineListener.LINK_OVER_CODE, reason.getErrReason());
                    } else {
                        destroy(VideoChatEngineListener.LINK_OVER_CODE);
                    }
                }
            }
        });
    }
}
